import * as React from 'react';
import { Document, pdfjs, Page } from 'react-pdf';
import { Document as Doc } from '../../model';
import { getDocumentUrl } from '../../service';
import DetailsModal from '../../../Modal/DetailsModal/DetailsModal';

import './_preview-modal.scss';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

interface Props {
  document: Doc;
  closePreviewModal: () => void;
}

pdfjs.GlobalWorkerOptions.workerSrc = 'https://assets.kylas.io/react-assets/pdf.worker.min.js';

const PreviewModal:React.FC<Props> = ({ document, closePreviewModal }) => {

  const [documentUrl, setDocumentUrl] = React.useState<string>(null);
  const [noOfPages, setNumberOfPages] = React.useState<number>(0);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [scale, setScale] = React.useState<number>(1.0);
  const [showDocInfo, setShowDocInfo] = React.useState<boolean>(false);
  const documentContainerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    getDocumentUrl(document.id).then((response) => {
      setDocumentUrl(response.data.url);
    }).catch(() => {
      //
    });
  }, [document.id]);

  // Zoom functionality
  const handleZoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.25, 3.0));
  };

  const handleZoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.25, 0.5));
  };

  const handleWheelZoom = (event: React.WheelEvent) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      if (event.deltaY < 0) {
        handleZoomIn();
      } else {
        handleZoomOut();
      }
    }
  };

  // Page tracking on scroll
  const handleScroll = React.useCallback(() => {
    if (!documentContainerRef.current) return;

    const container = documentContainerRef.current;
    const pages = container.querySelectorAll('.react-pdf__Page');
    const containerTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    const centerY = containerTop + containerHeight / 2;

    let closestPage = 1;
    let closestDistance = Infinity;

    pages.forEach((page, index) => {
      const pageElement = page as HTMLElement;
      const pageTop = pageElement.offsetTop;
      const pageHeight = pageElement.offsetHeight;
      const pageCenter = pageTop + pageHeight / 2;
      const distance = Math.abs(pageCenter - centerY);

      if (distance < closestDistance) {
        closestDistance = distance;
        closestPage = index + 1;
      }
    });

    setCurrentPage(closestPage);
  }, []);

  React.useEffect(() => {
    const container = documentContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Download functionality
  const handleDownload = () => {
    if (documentUrl) {
      const link = window.document.createElement('a');
      link.href = documentUrl;
      link.download = document.name || 'document.pdf';
      link.click();
    }
  };

  const renderHeaderContent = () => {
    return (
      <div className="header">
        <div className="docName">{document.name}</div>
        <div className="center-actions">
          <span className="pageNumbers">{currentPage}/{noOfPages}</span>
          <div className="zoom-actions">
            <button onClick={handleZoomOut} disabled={scale <= 0.5}>-</button>
            <span>{Math.round(scale * 100)}%</span>
            <button onClick={handleZoomIn} disabled={scale >= 3.0}>+</button>
          </div>
        </div>
        <div className="actions">
          <button onClick={handleDownload} className="action-btn" title="Download">
            <i className="fas fa-download"></i>
          </button>
          <button
            onClick={() => setShowDocInfo(!showDocInfo)}
            className="action-btn"
            title="Document Info"
          >
            <i className="fas fa-info-circle"></i>
          </button>
          <button onClick={closePreviewModal} className="action-btn" title="Close">
            <i className="fas fa-times"></i>
          </button>
        </div>
      </div>
    );
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumberOfPages(numPages);
  };

  const renderDocumentInfo = () => {
    if (!showDocInfo) return null;

    return (
      <div className="document-info-panel">
        <h4>Document Information</h4>
        <div className="info-item">
          <label>Name:</label>
          <span>{document.name}</span>
        </div>
        <div className="info-item">
          <label>Pages:</label>
          <span>{noOfPages}</span>
        </div>
        <div className="info-item">
          <label>Current Page:</label>
          <span>{currentPage}</span>
        </div>
        <div className="info-item">
          <label>Zoom:</label>
          <span>{Math.round(scale * 100)}%</span>
        </div>
        {document.size && (
          <div className="info-item">
            <label>Size:</label>
            <span>{(document.size / 1024 / 1024).toFixed(2)} MB</span>
          </div>
        )}
        {document.createdAt && (
          <div className="info-item">
            <label>Created:</label>
            <span>{new Date(document.createdAt).toLocaleDateString()}</span>
          </div>
        )}
      </div>
    );
  };

  return(
    <DetailsModal
      show={true}
      className="document-preview"
      title={'Preview'}
      onClickClose={closePreviewModal}
      headerContent={renderHeaderContent()}
    >
      <div className="pdf-viewer-container">
        <div
          className="document-container"
          ref={documentContainerRef}
          onWheel={handleWheelZoom}
        >
          <Document
            file={documentUrl}
            onLoadError={console.error}
            externalLinkTarget="_blank"
            renderMode="svg"
            onLoadSuccess={onDocumentLoadSuccess}
            options={{
              cMapUrl: 'https://assets.kylas.io/react-assets/cmaps/',
              cMapPacked: true
            }}
          >
            {
              Array.from({ length: noOfPages }, (_, index) => (
                <Page
                  key={index}
                  pageNumber={index + 1}
                  scale={scale}
                  className="pdf-page"
                />
              ))
            }
          </Document>
        </div>
        {renderDocumentInfo()}
      </div>
    </DetailsModal>
  );
};

export default PreviewModal;
