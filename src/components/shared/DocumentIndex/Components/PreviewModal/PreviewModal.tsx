import * as React from 'react';
import { Document, pdfjs, Page } from 'react-pdf';
import { Document as Doc } from '../../model';
import { getDocumentUrl } from '../../service';
import DetailsModal from '../../../Modal/DetailsModal/DetailsModal';

import './_preview-modal.scss';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

interface Props {
  document: Doc;
  closePreviewModal: () => void;
}

pdfjs.GlobalWorkerOptions.workerSrc = 'https://assets.kylas.io/react-assets/pdf.worker.min.js';

const PreviewModal:React.FC<Props> = ({ document, closePreviewModal }) => {

  const [documentUrl, setDocumentUrl] = React.useState<string>(null);
  const [noOfPages, setNumberOfPages] = React.useState<number>(0);

  React.useEffect(() => {
    getDocumentUrl(document.id).then((response) => {
      setDocumentUrl(response.data.url);
    }).catch(() => {
      //
    });
  },              []);

  const renderHeaderContent = () => {
    return (
      <div className="header">
        <div className="docName">{document.name}</div>
        <div className="center-actions">
          <span className="pageNumbers">1/{noOfPages}</span>
          <div className="zoom-actions">
            <button>+</button>
            <span>100%</span>
            <button>-</button>
          </div>
        </div>
        <div className="actions">
          <i className="fas fa-download"></i>
          <i className=""></i>
        </div>
      </div>
    );
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumberOfPages(numPages);
  };

  return(
    <DetailsModal
      show={true}
      className="document-preview"
      title={'Preview'}
      onClickClose={closePreviewModal}
      headerContent={renderHeaderContent()}
    >
      <Document
        file={documentUrl}
        onLoadError={console.error}
        externalLinkTarget="_blank"
        renderMode="svg"
        onLoadSuccess={onDocumentLoadSuccess}
        options={{
          cMapUrl: 'https://assets.kylas.io/react-assets/cmaps/',
          cMapPacked: true
        }}
      >
        {
          Array.from(new Array(noOfPages), (el, index) => (
            <Page key={index} pageNumber={index + 1} />
          ))
        }
      </Document>
    </DetailsModal>
  );
};

export default PreviewModal;
