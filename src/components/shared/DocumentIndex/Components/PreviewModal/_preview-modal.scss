#details_modal.document-preview {
  margin: 0;

  .modal-dialog {
    margin: 0;
    height: 100%;
    max-width: 100%;
    max-height: 100%;

    .modal-content {
      max-height: 100%;
      background: #2E384D;
      color: white;
    }

    .modal-body {
      height: 100%;
      padding: 0;
      overflow: hidden;
    }

    .pdf-viewer-container {
      display: flex;
      height: 100%;

      .document-container {
        flex: 1;
        overflow: auto;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;

        .react-pdf__Document {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .pdf-page {
          margin-bottom: 1rem;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          overflow: hidden;
        }
      }

      .document-info-panel {
        width: 300px;
        background: #3a4a5c;
        padding: 1rem;
        border-left: 1px solid #687790;
        overflow-y: auto;

        h4 {
          margin-bottom: 1rem;
          color: white;
          font-size: 1.1rem;
        }

        .info-item {
          margin-bottom: 0.75rem;

          label {
            display: block;
            font-size: 0.85rem;
            color: #a0a8b0;
            margin-bottom: 0.25rem;
          }

          span {
            color: white;
            font-size: 0.9rem;
          }
        }
      }
    }

    .header {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .docName {
        font-size: 1.2rem;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 1rem;
      }

      .center-actions {
        display: flex;
        align-items: center;
        margin-right: 1rem;
      }

      .pageNumbers {
        padding-right: 1rem;
        border-right: 1px solid #687790;
        margin-right: 1rem;
        font-size: 0.9rem;
      }

      .zoom-actions {
        display: flex;
        align-items: center;

        button {
          border: 1px solid #687790;
          font-size: 1.2rem;
          background: #4a5568;
          color: white;
          margin: 0 0.5rem;
          width: 32px;
          height: 32px;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover:not(:disabled) {
            background: #5a6578;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        span {
          background: #1a202c;
          border-radius: 0.3rem;
          padding: 0.3rem 0.6rem;
          font-size: 0.85rem;
          min-width: 50px;
          text-align: center;
        }
      }

      .actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .action-btn {
          border: none;
          background: none;
          color: white;
          font-size: 1.1rem;
          padding: 0.5rem;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }
}
