// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CampaignActivityResponseField component should render component 1`] = `
<CampaignActivityResponseField
  campaignActivityResponse={
    Array [
      Object {
        "activities": Array [
          Object {
            "id": 1,
            "name": "Kylas Summer Activity",
          },
        ],
        "id": 1,
        "name": "Kyas Summer Campaign",
      },
    ]
  }
  data={
    Object {
      "content": Array [
        Object {
          "actualExpense": Object {
            "currencyId": 400,
            "value": 110000,
          },
          "bulkJobId": null,
          "campaign": Object {
            "id": 123,
            "name": "campaign name",
          },
          "createdAt": "2021-09-10T04:04:23.835Z",
          "createdBy": Object {
            "id": 3788,
            "name": "<PERSON>",
          },
          "endDate": "2021-09-11T04:04:23.835Z",
          "endedAt": "2021-09-10T04:04:23.835Z",
          "endedBy": Object {
            "id": 3788,
            "name": "<PERSON>",
          },
          "entity": "LEAD",
          "estimatedBudget": Object {
            "currencyId": 400,
            "value": 100000,
          },
          "filters": Object {
            "jsonRule": Object {
              "condition": "AND",
              "rules": Array [
                Object {
                  "field": "id",
                  "id": "id",
                  "operator": "in",
                  "type": "long",
                  "value": "553092,553052",
                },
              ],
              "valid": true,
            },
          },
          "id": 1,
          "lastPausedAt": "2021-09-10T04:04:23.835Z",
          "lastPausedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "name": "Kylas Activity",
          "payload": Object {
            "connectedAccount": Object {
              "id": 1,
              "name": "Whatsapp Business Account",
            },
            "sentTo": "PRIMARY_PHONE_NUMBER",
            "type": "WHATSAPP",
            "whatsappTemplate": Object {
              "id": 47,
              "name": "Welcome to Kylas",
            },
          },
          "recordActions": Object {
            "delete": true,
            "read": true,
            "readAll": true,
            "update": true,
            "updateAll": true,
            "write": true,
          },
          "recordsGenerated": 0,
          "startDate": "2021-09-10T04:04:23.835Z",
          "startedAt": "2021-09-10T04:04:23.835Z",
          "startedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "status": "DRAFT",
          "totalEngagement": 0,
          "updatedAt": "2021-09-10T04:04:23.835Z",
          "updatedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "utmCampaign": "utm campaign",
          "utmContent": "content",
          "utmMedium": "medium",
          "utmSource": "google",
          "utmTerm": "term",
        },
      ],
      "first": true,
      "last": false,
      "number": 0,
      "numberOfElements": 1,
      "size": 10,
      "totalElements": 1,
      "totalPages": 1,
    }
  }
  dateFormat="MMM D, YYYY [at] h:mm a"
  entityName="Lead From Campaign"
  error={null}
  history={
    Object {
      "push": [MockFunction],
    }
  }
  loading={false}
  timezone="Asia/Calcutta"
>
  <div
    className="campaign-activity__response-field"
    onClick={[Function]}
  >
    <a
      className="link-primary cursor-pointer"
      onClick={[Function]}
    >
      View Activities
    </a>
    <DetailsModal
      className="campaign-activity__response-field__popup"
      onClickClose={[Function]}
      show={true}
      title="Campaign Activities related to Lead From Campaign (1)"
    >
      <Portal
        containerInfo={
          <body>
            <div
              aria-hidden="true"
              aria-labelledby="detailsModal"
              class="campaign-activity__response-field__popup modal fade show d-block"
              data-backdrop="static"
              id="details_modal"
              role="dialog"
            >
              <div
                class="modal-dialog modal-dialog-scrollable"
                role="document"
              >
                <div
                  class="modal-content"
                >
                  <div
                    class="modal-header"
                  >
                    <h2
                      class="h2 modal-title text-primary text-break w-100"
                      data-toggle="tooltip"
                      data-trigger="hover"
                      title=""
                    >
                      Campaign Activities related to Lead From Campaign (1)
                    </h2>
                    <button
                      aria-label="Close"
                      class="close"
                      data-dismiss="modal"
                      type="button"
                    >
                      <i
                        class="far fa-times"
                      />
                    </button>
                  </div>
                  <div
                    class="modal-body"
                  >
                    <div>
                      <div
                        class="campaign-activity__info"
                      >
                        <div
                          class="d-flex"
                        >
                          <img
                            alt="Activity Type"
                            src="test-file-stub"
                          />
                          <div
                            class="campaign-activity__name"
                          >
                            <h3
                              class="f-15"
                            >
                              Kylas Activity
                            </h3>
                            <span
                              class="f-14 pt-1"
                            >
                              Campaign
                               Name: 
                              <a
                                class="link-primary"
                              >
                                campaign name
                              </a>
                            </span>
                          </div>
                        </div>
                        <div
                          class="campaign-activity__utm-info"
                        >
                          <div
                            class="row"
                          >
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                Started At: 
                              </span>
                              <span>
                                Sep 10, 2021 at 9:34 am
                              </span>
                            </div>
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                UTM Campaign: 
                              </span>
                              <span>
                                utm campaign
                              </span>
                            </div>
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                UTM Source: 
                              </span>
                              <span>
                                google
                              </span>
                            </div>
                          </div>
                          <div
                            class="row"
                          >
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                UTM Medium: 
                              </span>
                              <span>
                                medium
                              </span>
                            </div>
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                UTM Content: 
                              </span>
                              <span>
                                content
                              </span>
                            </div>
                            <div
                              class="col-4"
                            >
                              <span
                                class="label"
                              >
                                UTM Term: 
                              </span>
                              <span>
                                term
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="text-center m-3"
                      >
                        <button
                          class="btn link-primary"
                        >
                          View More
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="modal-backdrop fade show"
            />
          </body>
        }
      >
        <div
          aria-hidden="true"
          aria-labelledby="detailsModal"
          className="campaign-activity__response-field__popup modal fade show d-block"
          data-backdrop="static"
          id="details_modal"
          role="dialog"
        >
          <div
            className="modal-dialog modal-dialog-scrollable"
            role="document"
          >
            <div
              className="modal-content"
            >
              <div
                className="modal-header"
              >
                <h2
                  className="h2 modal-title text-primary text-break w-100"
                  data-toggle="tooltip"
                  data-trigger="hover"
                  title=""
                >
                  Campaign Activities related to Lead From Campaign (1)
                </h2>
                <button
                  aria-label="Close"
                  className="close"
                  data-dismiss="modal"
                  disabled={false}
                  onClick={[Function]}
                  type="button"
                >
                  <i
                    className="far fa-times"
                  />
                </button>
              </div>
              <div
                className="modal-body"
              >
                <div>
                  <div
                    className="campaign-activity__info"
                    key="1"
                  >
                    <div
                      className="d-flex"
                    >
                      <img
                        alt="Activity Type"
                        src="test-file-stub"
                      />
                      <div
                        className="campaign-activity__name"
                      >
                        <h3
                          className="f-15"
                        >
                          Kylas Activity
                        </h3>
                        <span
                          className="f-14 pt-1"
                        >
                          Campaign
                           Name: 
                          <a
                            className="link-primary"
                            onClick={[Function]}
                          >
                            campaign name
                          </a>
                        </span>
                      </div>
                    </div>
                    <div
                      className="campaign-activity__utm-info"
                    >
                      <div
                        className="row"
                      >
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            Started At: 
                          </span>
                          <span>
                            Sep 10, 2021 at 9:34 am
                          </span>
                        </div>
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            UTM Campaign: 
                          </span>
                          <span>
                            utm campaign
                          </span>
                        </div>
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            UTM Source: 
                          </span>
                          <span>
                            google
                          </span>
                        </div>
                      </div>
                      <div
                        className="row"
                      >
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            UTM Medium: 
                          </span>
                          <span>
                            medium
                          </span>
                        </div>
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            UTM Content: 
                          </span>
                          <span>
                            content
                          </span>
                        </div>
                        <div
                          className="col-4"
                        >
                          <span
                            className="label"
                          >
                            UTM Term: 
                          </span>
                          <span>
                            term
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    className="text-center m-3"
                  >
                    <button
                      className="btn link-primary"
                      onClick={[Function]}
                    >
                      View More
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="modal-backdrop fade show"
        />
      </Portal>
    </DetailsModal>
  </div>
</CampaignActivityResponseField>
`;
