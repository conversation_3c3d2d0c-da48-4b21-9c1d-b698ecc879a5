import * as React from 'react';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';

import { EntityFormAction } from '../../../../../formLayout/models/Form';
import { TriggerName, WorkflowAction, WorkflowActionsType, WorkflowEntity, WorkflowFormSectionProps } from '../../../../models/Workflow';
import { isProd } from '../../../../../../../config/apiKeys';

import { WorkflowStep } from '../WorkflowStep/WorkflowStep';
import { WebhookAction } from './WorkflowActions/Webhook/WebhookAction';
import { AssignToAction } from './WorkflowActions/AssignToAction/AssignToAction';
import { FormRowAddAction, FormRowDeleteAction } from '../../FormRowActions/FormRowAction';
import { SelectField } from '../../../../../../shared/Input/FormInput/SelectField/SelectField';
import { WorkflowActionEditProperty } from './WorkflowActions/EditProperty/EditPropertyAction';
import ShareAction from './WorkflowActions/ShareAction/ShareAction';
import SendEmailAction from './WorkflowActions/SendEmail/SendEmailAction';
import ConvertLeadAction from './WorkflowActions/ConvertLead/ConvertLead';
import { getDataFromLocalStorage, shouldShowAIAssistants } from '../../../../../../../utils/util';
import { isBlank } from '../../../../../../../utils/globalUtil';
import { getSmartAssistantActivationInfo } from '../../../../../../shared/AIFeatures/EmailAssistant/service';
import { showErrorToast } from '../../../../../../../middlewares/errorToastr';
import { setDataInLocalStorage } from '../../../../../../../utils/util';
import { defaultPrompts } from './WorkflowActions/GenerateCallSummaryAction/constants';
import TriggerWorkflowAction from './WorkflowActions/TriggerWorkflow/TriggerWorkflow';
import FormField from '../../../../../../../higherOrderComponents/FormField/FormField';
import TaskAction from '../WorkflowTriggerActionsSection/WorkflowActions/Task/TaskAction';
import ReassignEntityAction from './WorkflowActions/ReassignEntityAction/ReassignEntityAction';
import WithPortalTooltip from '../../../../../../shared/WithPortalTooltip/WithPortalTooltip';
import WorkflowMarketPlaceAction from './WorkflowActions/WorkflowMarketplaceAction/WorkflowMarketplaceAction';
import SendWhatsAppMessageAction from './WorkflowActions/SendWhatsAppMessageAction/SendWhatsAppMessageAction';
import GenerateCallSummaryAction from './WorkflowActions/GenerateCallSummaryAction/GenerateCallSummaryAction';
import TooltipInitialiseContainer from '../../../../../../shared/TooltipInitialiseContainer/TooltipInitialiseContainer';
import GenerateCallSummaryPromptsModal from './WorkflowActions/GenerateCallSummaryAction/GenerateCallSummaryPromptsModal';

import './_workflow-trigger-actions-section.scss';

interface DropDownOption {
  label: string;
  value: string;
}

const workflowActionOptions = [
  { label: 'Edit Field', value: WorkflowActionsType.EDIT_PROPERTY },
  { label: 'Webhook', value: WorkflowActionsType.WEBHOOK },
  { label: 'Reassign', value: WorkflowActionsType.REASSIGN },
  { label: 'Assign To', value: WorkflowActionsType.ASSIGN_TO },
  { label: 'Share', value: WorkflowActionsType.SHARE },
  { label: 'Convert', value: WorkflowActionsType.CONVERT_LEAD },
  { label: 'Create Task', value: WorkflowActionsType.CREATE_TASK },
  { label: 'Send Email', value: WorkflowActionsType.SEND_EMAIL },
  { label: 'Marketplace Actions', value: WorkflowActionsType.MARKETPLACE_ACTION },
  { label: 'Execute Another Workflow', value: WorkflowActionsType.TRIGGER_WORKFLOW },
  { label: 'Send WhatsApp Message', value: WorkflowActionsType.SEND_WHATSAPP_MESSAGE },
  { label: 'Generate Call Summary', value: WorkflowActionsType.GENERATE_CALL_SUMMARY }
];

const WorkflowTriggerActionsSection: React.FC<WorkflowFormSectionProps> = ({ history, form, form: { entityType, condition, actions, trigger, executionCondition }, onFormValueChange, mode, layoutFields, entityLabelMap, profilePermissions, tenantId }) => {

  const [isCallSummaryEnabled, setIsCallSummaryEnabled] = React.useState(false);
  const [showGenerateCallSummaryPromptsModal, setShowGenerateCallSummaryPromptsModal] = React.useState(false);
  const [selectedPrompt, setSelectedPrompt] = React.useState<string>(defaultPrompts[0].content);

  React.useEffect(() => {
    if(shouldShowAIAssistants(tenantId) && entityType === WorkflowEntity.CALL_LOG){
      let callSummaryActivationInfo = getDataFromLocalStorage(`${tenantId}.call`, 'smartAssistant');

      if(isBlank(callSummaryActivationInfo)){

        getSmartAssistantActivationInfo()
        .then((response) => {
          callSummaryActivationInfo = response.data?.find(feature => feature?.featureType === 'call');

          setIsCallSummaryEnabled(callSummaryActivationInfo?.featureResponse?.enabled);

          setDataInLocalStorage(`${tenantId}.call`, 'smartAssistant', callSummaryActivationInfo?.featureResponse);
        })
        .catch((error) => {
          setIsCallSummaryEnabled(false);
          showErrorToast(error);
        });
      }else{
        setIsCallSummaryEnabled(callSummaryActivationInfo.enabled);
      }
    }
  },              [entityType]);

  const addRemoveActionItem = (updatedActions: WorkflowAction[]) => {
    onFormValueChange({ ...form, actions: updatedActions });
  };

  const updateWorkflowAction = (updatedAction, i) => {
    const workflowActions = cloneDeep(actions);
    workflowActions[i] = { ...workflowActions[i], ...updatedAction };
    onFormValueChange({ ...form, actions: workflowActions });
  };

  const setWorkflowActionType = (option: DropDownOption, i) => {
    const updatedType = {
      type: option.value as WorkflowActionsType,
      payload: option.value === WorkflowActionsType.CONVERT_LEAD ? { deal: false, contact: false, company: false } :
               option.value === WorkflowActionsType.GENERATE_CALL_SUMMARY ? { prompt: selectedPrompt } : null
    };

    updateWorkflowAction(updatedType, i);
  };

  const setWorkflowActionPayload = (payload, i) => {
    const updatedPayload = { payload };

    updateWorkflowAction(updatedPayload, i);
  };

  const isReadOnly = mode === EntityFormAction.VIEW;
  const actionsProps = {
    isReadOnly,
    setWorkflowActionPayload,
    allActions: actions
  };

  const actionsToBeSelectedOnce = [WorkflowActionsType.REASSIGN, WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.CONVERT_LEAD];

  const actionsToExcludeForEntity = {
    LEAD: [WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.TRIGGER_WORKFLOW, WorkflowActionsType.GENERATE_CALL_SUMMARY],
    DEAL: [WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.TRIGGER_WORKFLOW, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.GENERATE_CALL_SUMMARY, (isProd() && WorkflowActionsType.SEND_WHATSAPP_MESSAGE)],
    CONTACT: [WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.TRIGGER_WORKFLOW, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.GENERATE_CALL_SUMMARY],
    CALL_LOG: [WorkflowActionsType.CREATE_TASK, WorkflowActionsType.REASSIGN, WorkflowActionsType.SHARE, WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.SEND_WHATSAPP_MESSAGE],
    TASK: [WorkflowActionsType.CREATE_TASK, WorkflowActionsType.REASSIGN, WorkflowActionsType.SHARE, WorkflowActionsType.MARKETPLACE_ACTION, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.SEND_WHATSAPP_MESSAGE, WorkflowActionsType.GENERATE_CALL_SUMMARY],
    MEETING: [WorkflowActionsType.REASSIGN, WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.SHARE, WorkflowActionsType.CREATE_TASK, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.SEND_WHATSAPP_MESSAGE, WorkflowActionsType.GENERATE_CALL_SUMMARY],
    EMAIL: [WorkflowActionsType.REASSIGN, WorkflowActionsType.ASSIGN_TO, WorkflowActionsType.SHARE, WorkflowActionsType.CREATE_TASK, WorkflowActionsType.CONVERT_LEAD, WorkflowActionsType.SEND_EMAIL, WorkflowActionsType.MARKETPLACE_ACTION, WorkflowActionsType.EDIT_PROPERTY, WorkflowActionsType.SEND_WHATSAPP_MESSAGE, WorkflowActionsType.GENERATE_CALL_SUMMARY]
  };

  const getOptionLabel = (option, params) => {
    if(params.context === 'value'){
      return option.label;
    }

    return (
      <>
      {
        option.value === WorkflowActionsType.GENERATE_CALL_SUMMARY ?
          <WithPortalTooltip
            defaultTooltipPosition={'right'}
            tooltipContent={
              option.isDisabled ?
                (<div>
                  <div>This feature is not enabled on your account. Please enable it in the Call Intelligence section to use this action.</div>
                  <div className="underline" onClick={() => history.push('/setup/ai/call-intelligence/transcript-summary')}><b>Enable Call Intelligence</b></div>
                </div>)
              :
              'This will generate the call transcript summary, which will be added to the Call Summary field and the Notes section of the call log.'
            }
          >
            {option.label}
        </WithPortalTooltip>
        :
          <TooltipInitialiseContainer>
            <div data-toggle="tooltip" data-template={'<div class="tooltip workflow-action-option" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'} data-placement="right"
                title={getOptionTooltipTitle(option)}>
              {option.label}
            </div>
          </TooltipInitialiseContainer>
      }
      </>
    );
  };

  const getOptionTooltipTitle = (option) => {
    if(option.value === WorkflowActionsType.TRIGGER_WORKFLOW){
      return 'By using \Execute another workflow\ action, you can trigger another workflow for the associated entities';
    }

    return '';
  };
  return (
    <div className={classNames('workflow-section', { disabled: trigger?.name === TriggerName.EVENT ? !condition?.conditionType : !(trigger?.name === TriggerName.DELAYED && condition?.conditionType && executionCondition?.conditionType), 'border-bottom-0': mode === EntityFormAction.CREATE })}>
      <WorkflowStep
        stepNumber={trigger?.name === TriggerName.DELAYED ? 6 : 4}
        stepName="Set actions to be performed"
        stepDescription="Set any actions you want the system to perform automatically when the above selected trigger and conditions met"
      />
      {	(trigger?.name === TriggerName.EVENT ? !!condition?.conditionType : (trigger?.name === TriggerName.DELAYED && !!condition?.conditionType && !!executionCondition?.conditionType)) &&
        <div className="workflow-section__content">
          { actions.map((action, i) => (
            <div className="workflow__action" key={`${i}_${action.type}_${action.payload?.name}`}>
              <div className="d-flex">
                <div className="workflow__action-number">
                  <span >{i + 1}</span>
                </div>
                <div className="action-container">
                  <div className="row w-100">
                    <div className="col-3 pr-4">
                      <FormField
                        name={`editAction_action_${i}`}
                        inputId={`${i}_action`}
                        isRequired
                        isDisabled={isReadOnly}
                        value={workflowActionOptions.find(op => op.value === action.type)}
                        onChange={option => setWorkflowActionType(option, i)}
                        options={workflowActionOptions.filter(op => !actionsToExcludeForEntity[entityType]?.includes(op.value) && (!shouldShowAIAssistants(tenantId) ? op.value !== WorkflowActionsType.GENERATE_CALL_SUMMARY : true))
                                                      .map(op => ({ ...op, isDisabled: actionsToBeSelectedOnce.includes(op.value) && actions.some(a => a.type === op.value) || (entityType === WorkflowEntity.CALL_LOG && op.value === WorkflowActionsType.GENERATE_CALL_SUMMARY && !isCallSummaryEnabled) }))}
                        Component={SelectField}
                        menuPosition={'fixed'}
                        formatOptionLabel={getOptionLabel}
                      />
                    </div>
                    <div className="col-9 ml-n3">
                      <div className="row">
                        {
                          new Map([
                            [WorkflowActionsType.EDIT_PROPERTY,
                              <WorkflowActionEditProperty
                                {...actionsProps}
                                form={form}
                                actionIndex={i}
                                key="editProperty"
                                layoutFields={layoutFields}
                              />
                            ],
                            [WorkflowActionsType.WEBHOOK,
                              <WebhookAction key="webhook" actionIndex={i} form={form} onFormValueChange={onFormValueChange} isReadOnly={isReadOnly} entityLabelMap={entityLabelMap}/>
                            ],
                            [WorkflowActionsType.REASSIGN,
                              <ReassignEntityAction key="reassign" actionIndex={i} entityType={entityType} {...actionsProps}  />
                            ],
                            [WorkflowActionsType.SHARE,
                              <ShareAction key="share"
                                          actionIndex={i}
                                          form={form}
                                          onFormValueChange={onFormValueChange}
                                          mode={mode}
                                          profilePermissions={profilePermissions}
                                          entityLabelMap={entityLabelMap}/>
                            ],
                            [WorkflowActionsType.CREATE_TASK,
                              <TaskAction key="task" actionIndex={i} form={form} onFormValueChange={onFormValueChange} isReadOnly={isReadOnly} />
                            ],
                            [
                              WorkflowActionsType.SEND_EMAIL,
                              <SendEmailAction key="sendEmail" actionIndex={i} form={form} onFormValueChange={onFormValueChange} isReadOnly={isReadOnly} mode={mode}/>
                            ],
                            [
                              WorkflowActionsType.MARKETPLACE_ACTION,
                              <WorkflowMarketPlaceAction key={`marketplaceAction_${i}`} mode={mode} actionIndex={i} form={form} onFormValueChange={onFormValueChange} isReadOnly={isReadOnly}/>
                            ],
                            [WorkflowActionsType.ASSIGN_TO,
                              <AssignToAction key="assignTo" actionIndex={i} {...actionsProps} />
                            ],
                            [WorkflowActionsType.TRIGGER_WORKFLOW,
                              <TriggerWorkflowAction key="triggerWorkflow" actionIndex={i} {...actionsProps}/>
                            ],
                            [WorkflowActionsType.CONVERT_LEAD,
                              <ConvertLeadAction key="convertLead" actionIndex={i} {...actionsProps}/>
                            ],
                            [
                              WorkflowActionsType.SEND_WHATSAPP_MESSAGE,
                              <SendWhatsAppMessageAction
                                key="sendWhatsappMessage"
                                form={form}
                                mode={mode}
                                actionIndex={i}
                                isReadOnly={isReadOnly}
                                onFormValueChange={onFormValueChange}
                              />
                            ]
                          ]).get(action.type as WorkflowActionsType)
                        }
                        {
                          entityType === WorkflowEntity.CALL_LOG && action.type === WorkflowActionsType.GENERATE_CALL_SUMMARY && (
                            <div className="show-suggested-prompt link-primary ml-2" onClick={() => setShowGenerateCallSummaryPromptsModal(true)}>Use a Suggested Prompt</div>
                          )
                        }
                      </div>
                    </div>
                  </div>
                  {
                    entityType === WorkflowEntity.CALL_LOG && action.type === WorkflowActionsType.GENERATE_CALL_SUMMARY && (
                      <GenerateCallSummaryAction key="generateCallSummary" actionIndex={i} {...actionsProps}/>
                    )
                  }
                </div>
                <div className="workflow__action-buttons">
                  { !isReadOnly &&
                    <div className="form-group">
                      <div className="form-row__action-buttons">
                        <FormRowDeleteAction actions={actions} index={i} onDelete={addRemoveActionItem} />
                        <FormRowAddAction actions={actions} newAction={{ type: null }} index={i} onAdd={addRemoveActionItem} />
                      </div>
                    </div>
                  }
                </div>
                {
                  showGenerateCallSummaryPromptsModal && (
                    <GenerateCallSummaryPromptsModal
                      onSelectPrompt={(prompt) => {
                        setSelectedPrompt(prompt);
                        setWorkflowActionPayload({ prompt }, i);
                      }}
                      selectedPrompt={selectedPrompt}
                      onClose={() => setShowGenerateCallSummaryPromptsModal(false)}
                    />
                  )
                }
              </div>
            </div>
          ))}
        </div>
      }
    </div>
  );
};

export { WorkflowTriggerActionsSection };
