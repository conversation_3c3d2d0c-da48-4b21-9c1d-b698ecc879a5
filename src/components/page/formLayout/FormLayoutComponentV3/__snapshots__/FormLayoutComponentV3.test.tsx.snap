// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormLayoutComponentV3 component should render component 1`] = `
<Fragment>
  <form
    className="form-layout-v3"
    onSubmit={[MockFunction]}
  >
    <div
      className="form-header"
    >
      <div
        className="form-title"
      >
        Create
         
        Campaign
      </div>
    </div>
    <div
      className="form-body page-content min-height-0"
    >
      <div
        className="left-section"
      >
        <LeftNavSearchV2
          sectionMap={
            Array [
              Object {
                "description": "Add campaign basics like name, budget, and schedule to get started",
                "label": "General Information",
                "value": 1,
              },
              Object {
                "description": "Enter UTM details to track campaign performance across sources",
                "label": "UTM Information",
                "value": 2,
              },
            ]
          }
        />
      </div>
      <div
        className="right-section"
      >
        <div
          className="overflow-scroll position-relative"
          data-spy="scroll"
          data-target="#v-pills-tab"
        >
          <div
            className="data-container"
            id="1"
            key="0"
          >
            <div
              className="layout-header"
            >
              <h2
                className="h2"
              >
                General Information
              </h2>
            </div>
            <div
              className="layout-body"
            >
              <div
                className="row"
              >
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 1,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "Campaign Name",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 1,
                        "important": false,
                        "internal": false,
                        "internalName": "name",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 3,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 1,
                      "type": "FIELD",
                      "width": 12,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="0"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 2,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "Description",
                        "entity": null,
                        "filterable": false,
                        "greaterThan": null,
                        "id": 2,
                        "important": false,
                        "internal": false,
                        "internalName": "description",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 2550,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": false,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "PARAGRAPH_TEXT",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 2,
                      "type": "FIELD",
                      "width": 12,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="1"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 3,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "Estimated Budget",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 3,
                        "important": false,
                        "internal": false,
                        "internalName": "estimatedBudget",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "MONEY",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 3,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="2"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 2,
                      "id": 4,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "Actual Expense",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 4,
                        "important": false,
                        "internal": false,
                        "internalName": "actualExpense",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": true,
                        "regex": null,
                        "required": true,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "MONEY",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 3,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="3"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 5,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "Start Date",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 5,
                        "important": false,
                        "internal": false,
                        "internalName": "startDate",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": true,
                        "standard": true,
                        "type": "DATETIME_PICKER",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 4,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="4"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 2,
                      "id": 6,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "End Date",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 6,
                        "important": false,
                        "internal": false,
                        "internalName": "endDate",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "multiValue": false,
                        "pickLists": undefined,
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": false,
                        "sectionId": 1,
                        "showDefaultOptions": false,
                        "sortable": true,
                        "standard": true,
                        "type": "DATETIME_PICKER",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 4,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="5"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={0}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
              </div>
            </div>
          </div>
          <div
            className="data-container"
            id="2"
            key="1"
          >
            <div
              className="layout-header"
            >
              <h2
                className="h2"
              >
                UTM Information
              </h2>
            </div>
            <div
              className="layout-body"
            >
              <div
                className="row"
              >
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 1,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "UTM Campaign",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 1,
                        "important": false,
                        "internal": false,
                        "internalName": "utmCampaign",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 2,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 1,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="0"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={1}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 2,
                      "id": 2,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "UTM Source",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 2,
                        "important": false,
                        "internal": false,
                        "internalName": "utmSource",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "placeholder": "e.g. google, facebook, newsletter, linkedin",
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 2,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 1,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="1"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={1}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 3,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "UTM Medium",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 3,
                        "important": false,
                        "internal": false,
                        "internalName": "utmMedium",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "placeholder": "e.g. cpc, email, social, referral",
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": true,
                        "sectionId": 2,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 2,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="2"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={1}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 2,
                      "id": 4,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "UTM Content",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 4,
                        "important": false,
                        "internal": false,
                        "internalName": "utmContent",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "placeholder": "e.g. cta_top, image_ad, button_v1",
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": false,
                        "sectionId": 2,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 2,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="3"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={1}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
                <formFields
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  disableDependentField={false}
                  entity="campaigns"
                  entityAction="create"
                  form={
                    Object {
                      "column": 1,
                      "id": 5,
                      "item": Object {
                        "active": true,
                        "colorConfiguration": Array [],
                        "description": null,
                        "displayName": "UTM Term",
                        "entity": null,
                        "filterable": true,
                        "greaterThan": null,
                        "id": 5,
                        "important": false,
                        "internal": false,
                        "internalName": "utmTerm",
                        "isReadOnly": false,
                        "length": null,
                        "lessThan": null,
                        "lookupUrl": null,
                        "masked": false,
                        "max": 255,
                        "min": 0,
                        "multiValue": false,
                        "pickLists": undefined,
                        "placeholder": "e.g. crm+software, inventory+tool",
                        "primaryField": null,
                        "readOnly": false,
                        "regex": null,
                        "required": false,
                        "sectionId": 2,
                        "showDefaultOptions": false,
                        "sortable": false,
                        "standard": true,
                        "type": "TEXT_FIELD",
                        "unique": false,
                      },
                      "layoutItems": Array [],
                      "row": 3,
                      "type": "FIELD",
                      "width": 6,
                    }
                  }
                  formLayout={
                    Object {
                      "active": true,
                      "default": true,
                      "displayName": "Create Campaign Layout",
                      "entity": "CAMPAIGN",
                      "id": 1,
                      "layoutActions": Array [],
                      "layoutHeader": Object {
                        "label": null,
                      },
                      "layoutItems": Array [
                        Object {
                          "column": 1,
                          "id": 1,
                          "item": Object {
                            "collapsible": false,
                            "description": "Add campaign basics like name, budget, and schedule to get started",
                            "heading": "General Information",
                            "id": 1,
                            "name": "generalInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Campaign Name",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "name",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 3,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Description",
                                "entity": null,
                                "filterable": false,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "description",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 2550,
                                "min": 0,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "PARAGRAPH_TEXT",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 12,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Estimated Budget",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "estimatedBudget",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Actual Expense",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "actualExpense",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": true,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "MONEY",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "Start Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "startDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 6,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "End Date",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 6,
                                "important": false,
                                "internal": false,
                                "internalName": "endDate",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "multiValue": false,
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 1,
                                "showDefaultOptions": false,
                                "sortable": true,
                                "standard": true,
                                "type": "DATETIME_PICKER",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 4,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 1,
                          "type": "SECTION",
                          "width": 4,
                        },
                        Object {
                          "column": 1,
                          "id": 2,
                          "item": Object {
                            "collapsible": false,
                            "description": "Enter UTM details to track campaign performance across sources",
                            "heading": "UTM Information",
                            "id": 2,
                            "name": "utmInformation",
                          },
                          "layoutItems": Array [
                            Object {
                              "column": 1,
                              "id": 1,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Campaign",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 1,
                                "important": false,
                                "internal": false,
                                "internalName": "utmCampaign",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 2,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Source",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 2,
                                "important": false,
                                "internal": false,
                                "internalName": "utmSource",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. google, facebook, newsletter, linkedin",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 1,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 3,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Medium",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 3,
                                "important": false,
                                "internal": false,
                                "internalName": "utmMedium",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cpc, email, social, referral",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": true,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 2,
                              "id": 4,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Content",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 4,
                                "important": false,
                                "internal": false,
                                "internalName": "utmContent",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. cta_top, image_ad, button_v1",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 2,
                              "type": "FIELD",
                              "width": 6,
                            },
                            Object {
                              "column": 1,
                              "id": 5,
                              "item": Object {
                                "active": true,
                                "colorConfiguration": Array [],
                                "description": null,
                                "displayName": "UTM Term",
                                "entity": null,
                                "filterable": true,
                                "greaterThan": null,
                                "id": 5,
                                "important": false,
                                "internal": false,
                                "internalName": "utmTerm",
                                "length": null,
                                "lessThan": null,
                                "lookupUrl": null,
                                "masked": false,
                                "max": 255,
                                "min": 0,
                                "multiValue": false,
                                "placeholder": "e.g. crm+software, inventory+tool",
                                "primaryField": null,
                                "readOnly": false,
                                "regex": null,
                                "required": false,
                                "sectionId": 2,
                                "showDefaultOptions": false,
                                "sortable": false,
                                "standard": true,
                                "type": "TEXT_FIELD",
                                "unique": false,
                              },
                              "layoutItems": Array [],
                              "row": 3,
                              "type": "FIELD",
                              "width": 6,
                            },
                          ],
                          "row": 2,
                          "type": "SECTION",
                          "width": 4,
                        },
                      ],
                      "mode": "create",
                      "name": "createCampaign",
                      "showOnlyImportantField": true,
                      "systemDefault": true,
                    }
                  }
                  key="4"
                  nameAlreadyResolved={false}
                  parent={
                    Object {
                      "props": Object {
                        "campaignRemainingBudgetValue": Object {
                          "currencyId": 400,
                          "value": 0,
                        },
                        "change": [Function],
                        "entityAction": "create",
                        "formValues": Object {
                          "activities": Array [
                            Object {
                              "actualExpense": Object {
                                "currencyId": 400,
                                "value": 110000,
                              },
                              "bulkJobId": null,
                              "campaign": Object {
                                "id": 123,
                                "name": "campaign name",
                              },
                              "createdAt": "2021-09-10T04:04:23.835Z",
                              "createdBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "endedAt": "2021-09-10T04:04:23.835Z",
                              "endedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "entity": "LEAD",
                              "estimatedBudget": Object {
                                "currencyId": 400,
                                "value": 100000,
                              },
                              "filters": Object {
                                "jsonRule": Object {
                                  "condition": "AND",
                                  "rules": Array [
                                    Object {
                                      "field": "id",
                                      "id": "id",
                                      "operator": "in",
                                      "type": "long",
                                      "value": "553092,553052",
                                    },
                                  ],
                                  "valid": true,
                                },
                              },
                              "id": 1,
                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                              "lastPausedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "name": "Kylas Activity",
                              "payload": Object {
                                "connectedAccount": Object {
                                  "id": 1,
                                  "name": "Whatsapp Business Account",
                                },
                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                "type": "WHATSAPP",
                                "whatsappTemplate": Object {
                                  "id": 47,
                                  "name": "Welcome to Kylas",
                                },
                              },
                              "recordActions": Object {
                                "delete": true,
                                "read": true,
                                "readAll": true,
                                "update": true,
                                "updateAll": true,
                                "write": true,
                              },
                              "recordsGenerated": 0,
                              "startDate": "2021-09-10T04:04:23.835Z",
                              "startedAt": "2021-09-10T04:04:23.835Z",
                              "startedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "status": "DRAFT",
                              "totalEngagement": 0,
                              "updatedAt": "2021-09-10T04:04:23.835Z",
                              "updatedBy": Object {
                                "id": 3788,
                                "name": "Andrew Strauss",
                              },
                              "utmCampaign": "utm campaign",
                              "utmContent": "content",
                              "utmMedium": "medium",
                              "utmSource": "google",
                              "utmTerm": "term",
                            },
                          ],
                          "actualExpense": Object {
                            "currencyId": 400,
                            "value": 110000,
                          },
                          "createdAt": "2021-09-10T04:04:23.835Z",
                          "createdBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "description": "Kylas Campaign for Diwali festival",
                          "endDate": "2021-09-11T04:04:23.835Z",
                          "endedAt": "2021-09-10T04:04:23.835Z",
                          "endedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "estimatedBudget": Object {
                            "currencyId": 400,
                            "value": 100000,
                          },
                          "id": 1,
                          "name": "Kylas Campaign",
                          "recordActions": Object {
                            "delete": true,
                            "read": true,
                            "readAll": true,
                            "update": true,
                            "updateAll": true,
                            "write": true,
                          },
                          "recordsGenerated": 0,
                          "startDate": "2021-09-10T04:04:23.835Z",
                          "startedAt": "2021-09-10T04:04:23.835Z",
                          "startedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "status": "DRAFT",
                          "totalEngagement": 0,
                          "updatedAt": "2021-09-10T04:04:23.835Z",
                          "updatedBy": Object {
                            "id": 3788,
                            "name": "Andrew Strauss",
                          },
                          "utmCampaign": "utm campaign",
                          "utmContent": "content",
                          "utmMedium": "medium",
                          "utmSource": "google",
                          "utmTerm": "term",
                        },
                      },
                    }
                  }
                  sectionId={1}
                  tenantCurrency="INR"
                  timezone="Asia/Calcutta"
                  userCurrency="INR"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="form-footer"
    >
      <div
        className="helptext-wrapper"
      />
      <div
        className="form-buttons"
      >
        <button
          className="btn btn-outline-primary cursor-pointer"
          disabled={false}
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </button>
        <button
          className="btn btn-outline-primary"
          disabled={false}
          onClick={[Function]}
          type="submit"
        >
          Save as Draft
        </button>
        <SubmitButton
          extraProps={
            Object {
              "onClick": [Function],
            }
          }
          loading={false}
          pristine={false}
          shouldAlsoDisableOn={[Function]}
          submitText="Save and Add Actvity"
        />
      </div>
    </div>
  </form>
  <withRouter(RouteLeavingGuard)
    cancelBtnLabel="Cancel"
    confirmBtnClass="btn-primary"
    confirmBtnLabel="Okay"
    isNavigationBlocked={[Function]}
    message={
      <div>
        <div
          className="mb-3"
        >
          Are you sure you want to discard the 
          Campaign
          ?
        </div>
        Please note, that choosing to Okay will delete all the changes made to this 
        campaign
        <br />
        <br />
        Do you want to proceed?
      </div>
    }
    showRouteChangeWarning={true}
    title="Discard Campaign"
  />
</Fragment>
`;
