import { FieldTypes } from '../FieldSettings/models/Field';
import { CampaignActivityStatus, CampaignActivityType, PHONE_NUMBER_TYPE } from '../Campaign/model';

import { storeData } from '../../../store/mockStore';
import { entities, FILTER_OPERATORS } from '../../../utils/constants';
import { mockCampaignActivityFormValues, mockGetCampaignActivity } from '../Campaign/stub';
import { formatFormlayoutV3Payload, getFormLookupUrl, getFormPickListOptions, getInitialFormLayoutV3Values, shouldSetDefaultRangeForDateTimeField } from './utils';

describe('Formlayout utils', () => {
  const entityLabelMap = storeData.loginForm.entityLabelMap;

  it('should set default range for dateTime field', () => {
    expect(shouldSetDefaultRangeForDateTimeField(entities.LEADS, 'expectedClosureOn', true)).toEqual(false);
    expect(shouldSetDefaultRangeForDateTimeField(entities.DEALS, 'cfCustomDateField', false)).toEqual(false);
    expect(shouldSetDefaultRangeForDateTimeField(entities.CAMPAIGNS, 'startDate', true)).toEqual(false);
  });

  it('should get form pickList options', () => {
    const entityField = {
      id: 1,
      row: 1,
      column: 1,
      width: 6,
      type: 'FIELD',
      item: {
        internalName: 'entity',
        displayName: 'Entity',
        type: FieldTypes.PICK_LIST,
        id: 1,
        sectionId: 3,
        regex: null,
        entity: null,
        length: null,
        active: true,
        masked: false,
        unique: false,
        required: true,
        standard: true,
        lessThan: null,
        sortable: false,
        readOnly: false,
        internal: false,
        lookupUrl: null,
        filterable: true,
        important: false,
        description: null,
        multiValue: false,
        greaterThan: null,
        primaryField: null,
        colorConfiguration: [],
        showDefaultOptions: false,
        pickLists: [
          {
            id: 1,
            name: 'LEAD',
            displayName: 'Lead',
            disabled: false
          },
          {
            id: 2,
            name: 'CONTACT',
            displayName: 'Contact',
            disabled: false
          }
        ]
      }
    };

    expect(getFormPickListOptions(entities.CAMPAIGNS, entityLabelMap, entityField, null)).toEqual(entityField.item.pickLists);
    expect(getFormPickListOptions(entities.CAMPAIGN_ACTIVITIES, entityLabelMap, entityField, entityField.item.pickLists[0])).toEqual(entityField.item.pickLists[0]);
    expect(getFormPickListOptions(entities.CAMPAIGN_ACTIVITIES, entityLabelMap, entityField, null)).toEqual([
      {
        id: 1,
        name: 'LEAD',
        displayName: 'Teacher',
        disabled: false
      },
      {
        id: 2,
        name: 'CONTACT',
        displayName: 'Student',
        disabled: false
      }
    ]);
  });

  it('should get lookup url', () => {
    const entityField = {
      id: 1,
      row: 1,
      column: 1,
      width: 6,
      type: 'FIELD',
      item: {
        internalName: 'whatsappTemplate',
        displayName: 'Whatsapp Template',
        type: FieldTypes.LOOK_UP,
        id: 1,
        sectionId: 3,
        regex: null,
        entity: null,
        length: null,
        active: true,
        masked: false,
        unique: false,
        required: true,
        standard: true,
        lessThan: null,
        sortable: false,
        readOnly: false,
        internal: false,
        lookupUrl: null,
        filterable: true,
        important: false,
        pickLists: null,
        description: null,
        multiValue: false,
        greaterThan: null,
        primaryField: null,
        colorConfiguration: [],
        showDefaultOptions: true
      }
    };

    const formValues = {
      connectedAccount: { id: 1, name: 'whatsapp account' },
      entity: { id: 'LEAD', name: 'LEAD', displayName: 'Student' }
    };

    expect(getFormLookupUrl(entities.CAMPAIGN_ACTIVITIES, entityField, formValues)).toEqual('/messages/whatsapp-templates/lookup?connectedAccountId=1&entityType=lead&q=');
    expect(getFormLookupUrl(entities.CAMPAIGN_ACTIVITIES, entityField, null)).toEqual(null);
  });

  it('should format payload', () => {
    expect(formatFormlayoutV3Payload(entities.CAMPAIGN_ACTIVITIES, mockCampaignActivityFormValues)).toEqual({
      name: 'Kylas Campaign Activity',
      status: CampaignActivityStatus.DRAFT,
      startDate: '2025-05-05T09:00:00.000Z',
      endDate: '2025-05-05T11:00:00.000Z',
      utmSource: 'google',
      utmMedium: 'email',
      utmTerm: 'crm+software',
      utmContent: 'image_ad',
      utmCampaign: 'summer sales',
      entity: 'LEAD',
      campaign: { id: 1, name: 'Kylas Campaign' },
      actualExpense: { currencyId: 400, value: 100 },
      estimatedBudget: { currencyId: 400, value: 100 },
      filters: {
        jsonRule: {
          valid: true,
          condition: 'AND',
          rules: [
            {
              id: 'createdAt',
              field: 'createdAt',
              operator: FILTER_OPERATORS.today.internalName,
              type: 'date',
              not: null,
              input: null,
              value: null,
              data: null,
              rules: null,
              group: false,
              property: null,
              condition: null,
              primaryField: null,
              timeZone: 'Asia/Calcutta'
            }
          ]
        }
      },
      payload: {
        type: CampaignActivityType.WHATSAPP,
        sentTo: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
        whatsappTemplate: { id: 1, name: 'Welcome to Kylas' },
        connectedAccount: { id: 1, name: 'whatsapp account', businessName: 'whatsapp account' }
      }
    });
  });

  it('should get initial form values', () => {
    expect(getInitialFormLayoutV3Values(entities.CAMPAIGN_ACTIVITIES, mockGetCampaignActivity)).toEqual({
      id: 1,
      name: 'Kylas Activity',
      status: CampaignActivityStatus.DRAFT,
      bulkJobId: null,
      utmTerm: 'term',
      utmMedium: 'medium',
      utmSource: 'google',
      utmContent: 'content',
      totalEngagement: 0,
      recordsGenerated: 0,
      utmCampaign: 'utm campaign',
      endDate: '2021-09-11T04:04:23.835Z',
      endedAt: '2021-09-10T04:04:23.835Z',
      createdAt: '2021-09-10T04:04:23.835Z',
      startedAt: '2021-09-10T04:04:23.835Z',
      startDate: '2021-09-10T04:04:23.835Z',
      updatedAt: '2021-09-10T04:04:23.835Z',
      lastPausedAt: '2021-09-10T04:04:23.835Z',
      campaign: { id: 123, name: 'campaign name' },
      endedBy: { id: 3788, name: 'Andrew Strauss' },
      updatedBy: { id: 3788, name: 'Andrew Strauss' },
      startedBy: { id: 3788, name: 'Andrew Strauss' },
      createdBy: { id: 3788, name: 'Andrew Strauss' },
      actualExpense: { currencyId: 400, value: 110000 },
      lastPausedBy: { id: 3788, name: 'Andrew Strauss' },
      estimatedBudget: { currencyId: 400, value: 100000 },
      whatsappTemplate: { id: 47, name: 'Welcome to Kylas' },
      connectedAccount: { id: 1, name: 'Whatsapp Business Account' },
      entity: {
        id: 'LEAD',
        name: 'LEAD',
        displayName: 'Lead',
        disabled: false,
        systemDefault: true
      },
      filters: [
        {
          id: 'id',
          field: 'id',
          operator: FILTER_OPERATORS.in.internalName,
          type: 'long',
          value: '553092,553052'
        }
      ],
      recordActions: {
        delete: true,
        read: true,
        readAll: true,
        update: true,
        updateAll: true,
        write: true
      },
      sentTo: {
        id: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
        name: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
        displayName: 'Primary Phone number',
        disabled: false,
        systemDefault: true
      },
      type: {
        id: CampaignActivityType.WHATSAPP,
        name: CampaignActivityType.WHATSAPP,
        disabled: false,
        displayName: 'WhatsApp',
        systemDefault: true
      }
    });
  });
});
