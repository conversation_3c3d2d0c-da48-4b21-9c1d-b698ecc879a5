// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CampaignEdit component should render component 1`] = `
<Fragment>
  <withRouter(ApiStateHandler)
    ErrorFallbackComponent={[Function]}
    SkeletonComponent={[Function]}
    error={null}
    loading={false}
    showErrorComponent={true}
  >
    <Connect(ReduxForm)
      campaignRemainingBudgetValue={null}
      entity="campaign-activities"
      entityLayout={
        Object {
          "active": true,
          "default": true,
          "displayName": "Create Campaign Activity Layout",
          "entity": "CAMPAIGN_ACTIVITY",
          "id": 1,
          "layoutActions": Array [],
          "layoutHeader": Object {
            "label": null,
          },
          "layoutItems": Array [
            Object {
              "column": 1,
              "id": 1,
              "item": Object {
                "collapsible": false,
                "description": "Add a name, select type, and define budget for your campaign activity",
                "heading": "Activity Details",
                "id": 1,
                "name": "activityDetails",
              },
              "layoutItems": Array [
                Object {
                  "column": 1,
                  "id": 1,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Activity Name",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 1,
                    "important": false,
                    "internal": false,
                    "internalName": "name",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 3,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 12,
                },
                Object {
                  "column": 1,
                  "id": 2,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Campaign Name",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 2,
                    "important": false,
                    "internal": false,
                    "internalName": "campaign",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": "/campaigns/lookup?excludeStatus=COMPLETED&q=name:",
                    "masked": false,
                    "multiValue": false,
                    "pickLists": null,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": true,
                    "sortable": false,
                    "standard": true,
                    "type": "LOOK_UP",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 2,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 3,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Activity Type",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 3,
                    "important": false,
                    "internal": false,
                    "internalName": "type",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "pickLists": Array [
                      Object {
                        "disabled": false,
                        "displayName": "WhatsApp",
                        "id": "WHATSAPP",
                        "name": "WHATSAPP",
                        "systemDefault": true,
                      },
                    ],
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "PICK_LIST",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 2,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 4,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Estimated Budget",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 4,
                    "important": false,
                    "internal": false,
                    "internalName": "estimatedBudget",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "MONEY",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 5,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Actual Expense",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 5,
                    "important": false,
                    "internal": false,
                    "internalName": "actualExpense",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": true,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "MONEY",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 6,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Start Date",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 6,
                    "important": false,
                    "internal": false,
                    "internalName": "startDate",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": true,
                    "standard": true,
                    "type": "DATETIME_PICKER",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 6,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "End Date",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 7,
                    "important": false,
                    "internal": false,
                    "internalName": "endDate",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": false,
                    "sectionId": 1,
                    "showDefaultOptions": false,
                    "sortable": true,
                    "standard": true,
                    "type": "DATETIME_PICKER",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
              ],
              "row": 1,
              "type": "SECTION",
              "width": 4,
            },
            Object {
              "column": 1,
              "id": 2,
              "item": Object {
                "collapsible": false,
                "description": "Enter UTM details to track campaign activity performance across sources",
                "heading": "UTM Information",
                "id": 2,
                "name": "utmInformation",
              },
              "layoutItems": Array [
                Object {
                  "column": 2,
                  "id": 1,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "UTM Campaign",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 1,
                    "important": false,
                    "internal": false,
                    "internalName": "utmCampaign",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 0,
                    "multiValue": false,
                    "placeholder": "e.g. summer_sale, product_launch, leadgen2025",
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 2,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 2,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "UTM Source",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 2,
                    "important": false,
                    "internal": false,
                    "internalName": "utmSource",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 0,
                    "multiValue": false,
                    "placeholder": "e.g. google, facebook, newsletter, linkedin",
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 2,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 3,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "UTM Medium",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 3,
                    "important": false,
                    "internal": false,
                    "internalName": "utmMedium",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 0,
                    "multiValue": false,
                    "placeholder": "e.g. cpc, email, social, referral",
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 2,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 2,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 4,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "UTM Content",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 4,
                    "important": false,
                    "internal": false,
                    "internalName": "utmContent",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 0,
                    "multiValue": false,
                    "placeholder": "e.g. cta_top, image_ad, button_v1",
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": false,
                    "sectionId": 2,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 2,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 5,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "UTM Term",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 5,
                    "important": false,
                    "internal": false,
                    "internalName": "utmTerm",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "max": 255,
                    "min": 0,
                    "multiValue": false,
                    "placeholder": "e.g. crm+software, inventory+tool",
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": false,
                    "sectionId": 2,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "TEXT_FIELD",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
              ],
              "row": 2,
              "type": "SECTION",
              "width": 4,
            },
            Object {
              "column": 1,
              "id": 3,
              "item": Object {
                "collapsible": false,
                "description": "Choose who will receive this activity leads, contacts, or custom lists",
                "heading": "Recipients",
                "id": 3,
                "name": "recipients",
              },
              "layoutItems": Array [
                Object {
                  "column": 1,
                  "id": 1,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Entity",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 1,
                    "important": false,
                    "internal": false,
                    "internalName": "entity",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "pickLists": Array [
                      Object {
                        "disabled": false,
                        "displayName": "Lead",
                        "id": "LEAD",
                        "name": "LEAD",
                        "systemDefault": true,
                      },
                      Object {
                        "disabled": false,
                        "displayName": "Contact",
                        "id": "CONTACT",
                        "name": "CONTACT",
                        "systemDefault": true,
                      },
                    ],
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 3,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "PICK_LIST",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 2,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Smartlist",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 2,
                    "important": false,
                    "internal": false,
                    "internalName": "selectedSmartlist",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": false,
                    "sectionId": 3,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "PICK_LIST",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 3,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Sent To",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 3,
                    "important": false,
                    "internal": false,
                    "internalName": "sentTo",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "pickLists": Array [
                      Object {
                        "disabled": false,
                        "displayName": "Primary Phone number",
                        "id": "PRIMARY_PHONE_NUMBER",
                        "name": "PRIMARY_PHONE_NUMBER",
                        "systemDefault": true,
                      },
                      Object {
                        "disabled": false,
                        "displayName": "All Available Phone numbers",
                        "id": "ALL_PHONE_NUMBERS",
                        "name": "ALL_PHONE_NUMBERS",
                        "systemDefault": true,
                      },
                    ],
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 3,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "PICK_LIST",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 2,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 1,
                  "id": 4,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Filters",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 4,
                    "important": false,
                    "internal": false,
                    "internalName": "filters",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": false,
                    "sectionId": 3,
                    "showDefaultOptions": false,
                    "sortable": false,
                    "standard": true,
                    "type": "PICK_LIST",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 3,
                  "type": "FIELD",
                  "width": 6,
                },
              ],
              "row": 3,
              "type": "SECTION",
              "width": 4,
            },
            Object {
              "column": 1,
              "id": 4,
              "item": Object {
                "collapsible": false,
                "description": "Pick a saved template to speed up content creation",
                "heading": "Select Template",
                "id": 4,
                "name": "selectTemplate",
              },
              "layoutItems": Array [
                Object {
                  "column": 1,
                  "id": 1,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Whatsapp Account",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 1,
                    "important": false,
                    "internal": false,
                    "internalName": "connectedAccount",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": "/messages/connected-accounts/lookup?q=",
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 4,
                    "showDefaultOptions": true,
                    "sortable": false,
                    "standard": true,
                    "type": "LOOK_UP",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
                Object {
                  "column": 2,
                  "id": 2,
                  "item": Object {
                    "active": true,
                    "colorConfiguration": Array [],
                    "description": null,
                    "displayName": "Template Name",
                    "entity": null,
                    "filterable": true,
                    "greaterThan": null,
                    "id": 2,
                    "important": false,
                    "internal": false,
                    "internalName": "whatsappTemplate",
                    "length": null,
                    "lessThan": null,
                    "lookupUrl": null,
                    "masked": false,
                    "multiValue": false,
                    "primaryField": null,
                    "readOnly": false,
                    "regex": null,
                    "required": true,
                    "sectionId": 4,
                    "showDefaultOptions": true,
                    "sortable": false,
                    "standard": true,
                    "type": "LOOK_UP",
                    "unique": false,
                  },
                  "layoutItems": Array [],
                  "row": 1,
                  "type": "FIELD",
                  "width": 6,
                },
              ],
              "row": 4,
              "type": "SECTION",
              "width": 4,
            },
          ],
          "mode": "create",
          "name": "createCampaignActivity",
          "showOnlyImportantField": true,
          "systemDefault": true,
        }
      }
      formFooterRouteActions={
        Object {
          "onCancelBtnClick": [Function],
          "onPrimarySaveBtnClick": [Function],
          "onSecondarySaveBtnClick": [Function],
        }
      }
      history={
        Object {
          "location": Object {
            "pathname": "/sales/campaigns/view/1",
          },
          "push": [MockFunction],
        }
      }
      initialValues={
        Object {
          "actualExpense": Object {
            "currencyId": 400,
            "value": 110000,
          },
          "bulkJobId": null,
          "campaign": Object {
            "id": 123,
            "name": "campaign name",
          },
          "connectedAccount": Object {
            "id": 1,
            "name": "Whatsapp Business Account",
          },
          "createdAt": "2021-09-10T04:04:23.835Z",
          "createdBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "endDate": "2021-09-11T04:04:23.835Z",
          "endedAt": "2021-09-10T04:04:23.835Z",
          "endedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "entity": Object {
            "disabled": false,
            "displayName": "Lead",
            "id": "LEAD",
            "name": "LEAD",
            "systemDefault": true,
          },
          "estimatedBudget": Object {
            "currencyId": 400,
            "value": 100000,
          },
          "filters": Array [
            Object {
              "field": "id",
              "id": "id",
              "operator": "in",
              "type": "long",
              "value": "553092,553052",
            },
          ],
          "id": 1,
          "lastPausedAt": "2021-09-10T04:04:23.835Z",
          "lastPausedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "name": "Kylas Activity",
          "recordActions": Object {
            "delete": true,
            "read": true,
            "readAll": true,
            "update": true,
            "updateAll": true,
            "write": true,
          },
          "recordsGenerated": 0,
          "sentTo": Object {
            "disabled": false,
            "displayName": "Primary Phone number",
            "id": "PRIMARY_PHONE_NUMBER",
            "name": "PRIMARY_PHONE_NUMBER",
            "systemDefault": true,
          },
          "startDate": "2021-09-10T04:04:23.835Z",
          "startedAt": "2021-09-10T04:04:23.835Z",
          "startedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "status": "DRAFT",
          "totalEngagement": 0,
          "type": Object {
            "disabled": false,
            "displayName": "WhatsApp",
            "id": "WHATSAPP",
            "name": "WHATSAPP",
            "systemDefault": true,
          },
          "updatedAt": "2021-09-10T04:04:23.835Z",
          "updatedBy": Object {
            "id": 3788,
            "name": "Andrew Strauss",
          },
          "utmCampaign": "utm campaign",
          "utmContent": "content",
          "utmMedium": "medium",
          "utmSource": "google",
          "utmTerm": "term",
          "whatsappTemplate": Object {
            "id": 47,
            "name": "Welcome to Kylas",
          },
        }
      }
      isFieldDisabled={[Function]}
      mode="edit"
      onFormSubmit={[Function]}
    />
  </withRouter(ApiStateHandler)>
</Fragment>
`;
