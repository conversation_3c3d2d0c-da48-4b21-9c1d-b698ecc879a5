// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignOverview component should render component 1`] = `
<div
  className="campaign-overview"
>
  <strong>
    Campaign
     Overview
  </strong>
  <div
    className="campaign-overview__section"
    style={
      Object {
        "height": "34.98rem",
      }
    }
  >
    <div
      className="campaign-report w-65"
    >
      <div
        className="campaign-activities__not-started"
      >
        <div
          className="d-flex flex-column align-items-center m-auto"
        >
          <img
            alt="report-data"
            className="empty__report-data"
            src="test-file-stub"
          />
          <div
            className="mt-1"
          >
            <img
              alt="bullseye-arrow"
              src="test-file-stub"
            />
            <i
              className="ml-1"
            >
              You're all set to launch!
            </i>
          </div>
          <div
            className="mt-2 add-instruction"
          >
            You've added an activity to this 
            campaign
            . Once it starts, you'll see key performance insights
          </div>
        </div>
      </div>
    </div>
    <div
      className="w-35"
    >
      <Connect(CampaignRelatedFormValues)
        entity="campaigns"
        formValues={
          Object {
            "activities": Array [
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 1,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
            ],
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "description": "Kylas Campaign for Diwali festival",
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "id": 1,
            "name": "Kylas Campaign",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
      />
    </div>
  </div>
</div>
`;

exports[`CampaignOverview component should render component for campaign analytics 1`] = `
<div
  className="campaign-overview"
>
  <strong>
    Campaign
     Overview
  </strong>
  <div
    className="campaign-overview__section"
    style={
      Object {
        "height": "",
      }
    }
  >
    <div
      className="campaign-report w-65"
    >
      <div
        className="campaign-analytics"
      >
        <CampaignActivitySummary
          actualExpense={
            Object {
              "currencyId": 400,
              "value": 110000,
            }
          }
          entityName={null}
          estimatedBudget={
            Object {
              "currencyId": 400,
              "value": 100000,
            }
          }
          recordsGenerated={0}
          totalEngagement={0}
        />
        <div
          className="row"
        >
          <div
            className="col-6"
          >
            <WithAbortController
              activityId={null}
              campaignId={1}
              entity="campaigns"
              history={
                Object {
                  "push": [MockFunction],
                }
              }
              isMultiLineChart={false}
              multiDimensionToRender={null}
              numberFormat="INDIAN_NUMBER_FORMAT"
              strokeColor="#006DEE"
              timezone="Asia/Calcutta"
              type="Overall Engagement"
            />
          </div>
          <div
            className="col-6"
          >
            <Connect(BarChartReport)
              currencyId={400}
              dateRange={
                Object {
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "startDate": "2021-09-10T04:04:23.835Z",
                }
              }
              entity="campaigns"
              reportData={
                Array [
                  Object {
                    "color": "#9747FF",
                    "dimension": Array [],
                    "id": null,
                    "name": "Budget",
                    "value": 100000,
                  },
                  Object {
                    "color": "#C2505A",
                    "dimension": Array [],
                    "id": null,
                    "name": "Actual",
                    "value": 110000,
                  },
                ]
              }
              type="Budget vs Actual Expense"
            />
          </div>
        </div>
        <div
          className="row"
        >
          <div
            className="col-12"
          >
            <WithAbortController
              activityId={null}
              campaignId={1}
              entity="campaigns"
              history={
                Object {
                  "push": [MockFunction],
                }
              }
              isMultiLineChart={true}
              multiDimensionToRender={Array []}
              numberFormat="INDIAN_NUMBER_FORMAT"
              strokeColor="#006DEE"
              timezone="Asia/Calcutta"
              type="Engagement per activity"
            />
          </div>
        </div>
        <div
          className="expense-per-activity__section"
        >
          <div
            className="f-14"
          >
            Budget vs Expense per activity
          </div>
          <div
            className="f-12 mb-2 mt-1"
          >
            Activity estimated budget and actual expense for this 
            campaign
             in (Expense / Budget) format
          </div>
          <div
            className="activity-expense"
            key="1"
          >
            <div
              className="d-flex w-60"
            >
              <img
                alt="WHATSAPP"
                src="test-file-stub"
              />
              <span
                className="f-14 align-self-center"
              >
                Kylas Activity
              </span>
            </div>
            <div
              className="money-field"
            >
              <Connect(ReadOnlyMoney)
                currencyId={400}
                value={110000}
              />
               / 
              <Connect(ReadOnlyMoney)
                currencyId={400}
                value={100000}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="w-35"
    >
      <Connect(CampaignRelatedFormValues)
        entity="campaigns"
        formValues={
          Object {
            "activities": Array [
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 1,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
            ],
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "description": "Kylas Campaign for Diwali festival",
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "id": 1,
            "name": "Kylas Campaign",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "IN_PROGRESS",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
      />
    </div>
  </div>
</div>
`;
