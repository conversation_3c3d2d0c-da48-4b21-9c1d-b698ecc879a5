import * as React from 'react';
import { connect } from 'react-redux';

import * as BullsEyeArrow from '../../../../assets/icons/bullseye-arrow.svg';
import * as ReportImage from '../../../../assets/images/placeholder/report-data.svg';
import * as whatsAppLogo from '../../../../assets/images/WhatsApp/whatsapp-logo.svg';

import { StateInterface } from '../../../../store/store';
import { Campaign, CampaignActivity, CampaignActivityStatus, CampaignAnalyticsType, CampaignStatus } from '../model';

import { isCampaignDisabled } from '../utils';
import { entities } from '../../../../utils/constants';
import { isBlank } from '../../../../utils/globalUtil';
import { capitalizeLabel, routeToEntity, routeToEntitySingular } from '../../../../utils/entityUtils';

import Can from '../../../shared/Permission/UserPermission';
import ReadOnlyMoney from '../../../shared/ReadOnlyMoney/ReadOnlyMoney';
import NoDataComponent from '../../../shared/NoDataComponent/NoDataComponent';
import BarChartReport from '../../../shared/BarChartReport/BarChartReport';
import CampaignRelatedFormValues from '../CampaignRelatedFormValues/CampaignRelatedFormValues';
import CampaignActivitySummary from '../CampaignAnalytics/CampaignActivitySummary/CampaignActivitySummary';
import CampaignActivityLineChart from '../CampaignAnalytics/CampaignActivityLineChart/CampaignActivityLineChart';

import './_campaign-overview.scss';

interface StoreProps {
  timezone: string;
  numberFormat: string;
}

interface OwnProps {
  history: any;
  campaign: Campaign;
}

type Props = StoreProps & OwnProps;

export const CampaignOverview:React.FC<Props> = ({ campaign, history, timezone, numberFormat }) => {
  const { id, status, startDate, endDate, recordActions, activities, estimatedBudget, actualExpense: { value: actualExpenseValue } } = campaign;

  return (
    <div className="campaign-overview">
      <strong>{capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))} Overview</strong>

        <div className="campaign-overview__section" style={{ height: ((campaign.status === CampaignStatus.DRAFT) && !isBlank(activities)) ? '34.98rem' : '' }}>
          <div className="campaign-report w-65">
            { isBlank(activities) ?
                <div className="no-activities-found">
                  <NoDataComponent title={'No Activities Found'} type={'table'}>
                    <div className="mt-2 add-instruction">Add at least one activity to this {routeToEntity(entities.CAMPAIGNS)} to start tracking performance and view analytics</div>

                    <Can I={'["write"]'} ability={recordActions}>
                      <button className="btn btn-primary mt-3" onClick={() => history.push(`/sales/${entities.CAMPAIGNS}/view/${id}`)}>+ Add Activity</button>
                    </Can>
                  </NoDataComponent>
                </div>
              :
                !isCampaignDisabled(status) ?
                  <div className="campaign-activities__not-started">
                    <div className="d-flex flex-column align-items-center m-auto">
                      <img className="empty__report-data" src={`${ReportImage}`} alt="report-data" />

                      <div className="mt-1">
                        <img src={`${BullsEyeArrow}`} alt="bullseye-arrow" />
                        <i className="ml-1">You're all set to launch!</i>
                      </div>

                      <div className="mt-2 add-instruction">You've added an activity to this {routeToEntity(entities.CAMPAIGNS)}. Once it starts, you'll see key performance insights</div>
                    </div>
                  </div>
                :
                  <div className="campaign-analytics">
                    <CampaignActivitySummary
                      entityName={null}
                      actualExpense={campaign.actualExpense}
                      estimatedBudget={campaign.estimatedBudget}
                      totalEngagement={campaign.totalEngagement}
                      recordsGenerated={campaign.recordsGenerated}
                    />

                    <div className="row">
                      <div className="col-6">
                        <CampaignActivityLineChart
                          campaignId={id}
                          activityId={null}
                          history={history}
                          timezone={timezone}
                          strokeColor="#006DEE"
                          isMultiLineChart={false}
                          entity={entities.CAMPAIGNS}
                          numberFormat={numberFormat}
                          multiDimensionToRender={null}
                          type={CampaignAnalyticsType.OVERALL_ENGAGEMENT}
                        />
                      </div>

                      <div className="col-6">
                        <BarChartReport
                          entity={entities.CAMPAIGNS}
                          dateRange={{ startDate, endDate }}
                          currencyId={estimatedBudget.currencyId}
                          type={CampaignAnalyticsType.BUDGET_VS_ACTUAL_EXPENSE}
                          reportData={[
                            {
                              id: null,
                              name: 'Budget',
                              dimension: [],
                              color: '#9747FF',
                              value: estimatedBudget.value
                            },
                            {
                              id: null,
                              name: 'Actual',
                              dimension: [],
                              color: '#C2505A',
                              value: actualExpenseValue
                            }
                          ]}
                        />
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-12">
                        <CampaignActivityLineChart
                          campaignId={id}
                          activityId={null}
                          history={history}
                          timezone={timezone}
                          strokeColor="#006DEE"
                          isMultiLineChart={true}
                          entity={entities.CAMPAIGNS}
                          numberFormat={numberFormat}
                          type={CampaignAnalyticsType.ENGAGEMENT_PER_ACTIVITY}
                          multiDimensionToRender={activities.filter(f => f.status !== CampaignActivityStatus.DRAFT).map(v => ({ id: v.id, name: v.name }))}
                        />
                      </div>
                    </div>

                    <div className="expense-per-activity__section">
                      <div className="f-14">Budget vs Expense per activity</div>
                      <div className="f-12 mb-2 mt-1">Activity estimated budget and actual expense for this {routeToEntity(entities.CAMPAIGNS)} in (Expense / Budget) format</div>

                      {
                        activities.sort((a, b) => (b.actualExpense.value - a.actualExpense.value)).map((activity: CampaignActivity) => {
                          const { id: activityId, name, estimatedBudget: activityBudget, actualExpense, payload: { type } } = activity;

                          return(
                            <div className="activity-expense" key={activityId}>
                              <div className="d-flex w-60">
                                <img src={`${whatsAppLogo}`} alt={type} />
                                <span className="f-14 align-self-center">{name}</span>
                              </div>

                              <div className="money-field">
                                <ReadOnlyMoney {...actualExpense} />
                                &nbsp;/&nbsp;
                                <ReadOnlyMoney {...activityBudget} />
                              </div>
                            </div>
                          );
                        })
                      }
                    </div>
                  </div>
            }
          </div>

          <div className="w-35">
            <CampaignRelatedFormValues entity={entities.CAMPAIGNS} formValues={campaign}  />
          </div>
        </div>
    </div>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  timezone: state.loginForm.userPreferences.timezone,
  numberFormat: state.loginForm.userPreferences.numberFormat
});

export default connect(mapStateToProps, null)(CampaignOverview);
