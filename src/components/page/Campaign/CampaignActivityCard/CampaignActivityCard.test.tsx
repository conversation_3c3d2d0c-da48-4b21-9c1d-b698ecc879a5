import * as React from 'react';
import { mount } from 'enzyme';
import thunk from 'redux-thunk';
import to<PERSON>son from 'enzyme-to-json';
import { Provider } from 'react-redux';
import { act } from 'react-dom/test-utils';
import { toastr } from 'react-redux-toastr';
import configureStore from 'redux-mock-store';
import { BrowserRouter as Router } from 'react-router-dom';

const runCampaignActivityMock = jest.fn();
const deleteCampaignActivityMock = jest.fn();
jest.mock('../service', () => ({
  ...jest.requireActual<typeof import('../service')>('../service'),
  runCampaignActivity: runCampaignActivityMock,
  deleteCampaignActivity: deleteCampaignActivityMock
}));

import { CampaignActionType } from '../model';

import { storeData } from '../../../../store/mockStore';
import { ReferrerFlow } from '../../../../utils/constants';
import { waitForComponentToPaint } from '../../../../../tests/utils';
import { mockGetCampaignActivity, mockGetCampaignList } from '../stub';

import { CampaignActivityCard } from './CampaignActivityCard';
import ConfirmModal from '../../../shared/Modal/ConfirmModal';
import MultiActionModal from '../../../shared/Input/MultiActionModal';

describe('CampaignActivityCard component', () => {
  const props = {
    activity: mockGetCampaignActivity,
    referrer: ReferrerFlow.CAMPAIGN_LIST,
    flexDirectionForProgressBar: 'flex-row',
    entityLabelMap: storeData.loginForm.entityLabelMap,
    timezone: storeData.loginForm.userPreferences.timezone,
    dateFormat: storeData.loginForm.userPreferences.dateFormat,
    recordActions: mockGetCampaignList.content[0].recordActions,
    history: { push: jest.fn() },
    fetchData: jest.fn()
  };

  const mockmiddlewares = [thunk];
  const mockStore = configureStore(mockmiddlewares);
  const store = mockStore(storeData);

  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    wrapper = mount(
      <Provider store={store}>
        <Router>
          <CampaignActivityCard {...props} />
        </Router>
      </Provider>
    );
  });

  const runCampaignActivityPromise = Promise.resolve({ status: 200 });
  runCampaignActivityMock.mockReturnValue(runCampaignActivityPromise);

  const deleteCampaignActivityPromise = Promise.resolve({ status: 200 });
  deleteCampaignActivityMock.mockReturnValue(deleteCampaignActivityPromise);

  toastr.success = jest.fn();

  it('should render component', () => {
    // @ts-ignore
    expect(toJson(wrapper)).toMatchSnapshot();
  });

  it('should redirect to campaign details page on click', () => {
    act(() => wrapper.find('.campaign-activity__card').props().onClick());

    expect(props.history.push).toHaveBeenCalledWith('/sales/campaigns/details/123', { activityId: 1 });
  });

  describe('Ellipses options', () => {
    it('should route to campagin details on click view page', () => {
      const viewOption = wrapper.find(MultiActionModal).props().options[0];
      expect(viewOption.label).toEqual('View');

      act(() => viewOption.action({ stopPropagation: jest.fn() }));
      expect(props.history.push).toHaveBeenCalledWith('/sales/campaigns/details/123', { activityId: 1 });
    });

    it('should route to edit campaign view page on click edit icon', () => {
      const editOption = wrapper.find(MultiActionModal).props().options[1];
      expect(editOption.label).toEqual('Edit');

      act(() => editOption.action({ stopPropagation: jest.fn() }));
      expect(props.history.push).toHaveBeenCalledWith('/sales/campaigns/view/123', { activityId: 1, campaignId: null, referrer: ReferrerFlow.CAMPAIGN_LIST });
    });

    it('should start campaign activity', async() => {
      const startCampaignActivityOption = wrapper.find(MultiActionModal).at(0).find('.dropdown-item').at(2);
      expect(startCampaignActivityOption.text()).toEqual('Start');

      act(() => startCampaignActivityOption.props().onClick({ stopPropagation: jest.fn() }));
      await waitForComponentToPaint(wrapper);

      expect(wrapper.find(ConfirmModal).length).toEqual(1);
      act(() => wrapper.find(ConfirmModal).props().onConfirm());
      await waitForComponentToPaint(wrapper);

      expect(runCampaignActivityMock).toHaveBeenCalledWith(1, 123, CampaignActionType.START, props.history);

      return runCampaignActivityPromise.then(() => {
        expect(props.fetchData).toHaveBeenCalled();
        expect(toastr.success).toHaveBeenCalledWith('Success', 'Activity Started');
      });
    });

    it('should delete campaign activity', async() => {
      const deleteOption = wrapper.find(MultiActionModal).at(0).find('.dropdown-item').at(3);
      expect(deleteOption.text()).toEqual('Delete');

      act(() => deleteOption.props().onClick({ stopPropagation: jest.fn() }));
      await waitForComponentToPaint(wrapper);

      expect(wrapper.find(ConfirmModal).length).toEqual(1);
      act(() => wrapper.find(ConfirmModal).props().onConfirm());
      await waitForComponentToPaint(wrapper);

      expect(deleteCampaignActivityMock).toHaveBeenCalledWith(1, 123, props.history);

      return deleteCampaignActivityPromise.then(() => {
        expect(props.fetchData).toHaveBeenCalled();
        expect(toastr.success).toHaveBeenCalledWith('Success', 'Activity Deleted');
      });
    });
  });
});
