import * as React from 'react';
import { connect } from 'react-redux';
import * as moment from 'moment-timezone';
import { toastr } from 'react-redux-toastr';

import * as whatsappLogoIcon from '../../../../assets/images/WhatsApp/whatsapp-logo.svg';

import { StateInterface } from '../../../../store/store';
import { EntityLabelMap } from '../../login/models/login';
import { PermissionAction } from '../../../../utils/models/PermissionModel';
import { CampaignActivity, CampaignActivityStatus, CampaignActionType } from '../model';

import { isBlank } from '../../../../utils/globalUtil';
import { formatDate } from '../../../../utils/dateUtils';
import { CampaignActivityStatusColors } from '../constants';
import { entities, ReferrerFlow } from '../../../../utils/constants';
import { showErrorToast } from '../../../../middlewares/errorToastr';
import { convertSnakeCaseTextToLabel } from '../../../../utils/stringUtils';
import { DELETE, isActionAllowed, UPDATE } from '../../../../utils/permissionUtil';
import { capitalizeLabel, getEntityLabelPlural, routeToEntity } from '../../../../utils/entityUtils';
import { getAvailableActionsForCampaignActivityAsPerStatus, getCampaignActionName, getConfirmModalPropsAsPerCampaignActionType, isCampaignActivityDisabled } from '../utils';

import { deleteCampaignActivity, runCampaignActivity } from '../service';

import ConfirmModal from '../../../shared/Modal/ConfirmModal';
import ProgressBar from '../../../shared/ProgressBar/ProgressBar';
import MultiActionModal from '../../../shared/Input/MultiActionModal';
import ReadOnlyMoney from '../../../shared/ReadOnlyMoney/ReadOnlyMoney';

import './_campaign-activity-card.scss';

interface StoreProps {
  timezone: string;
  dateFormat: string;
  entityLabelMap: EntityLabelMap;
}

interface OwnProps {
  history: any;
  referrer: ReferrerFlow;
  activity: CampaignActivity;
  recordActions: PermissionAction;
  flexDirectionForProgressBar: string;
  fetchData: (type: string) => void;
}

type Props = StoreProps & OwnProps;

export const CampaignActivityCard:React.FC<Props> = ({
  history,
  activity,
  timezone,
  referrer,
  fetchData,
  dateFormat,
  recordActions,
  entityLabelMap,
  flexDirectionForProgressBar
}) => {
  const [showModalFor, toggleModalFor] = React.useState<string>(null);

  const {
    id,
    name,
    status,
    entity,
    endDate,
    campaign,
    bulkJobId,
    startDate,
    actualExpense,
    estimatedBudget,
    totalEngagement,
    recordsGenerated,
    payload: { type }
  } = activity;

  const getCampaignProgressBarColorAsPerPercentageForCampaign = (isDateField: boolean, value: number) => {
    const VALUE_ABOVE_OR_EQUAL_100 = isDateField ? '#23B33A' : '#EA4335';
    const VALUE_ABOVE_75 = '#FCD062';
    const VALUE_ABOVE_0 = '#1C99FF';
    const VALUE_BELOW_OR_EQUAL_0 = '#E5F0FE';

    if(value >= 100) return VALUE_ABOVE_OR_EQUAL_100;
    if(value > 75) return VALUE_ABOVE_75;
    if(value > 0) return VALUE_ABOVE_0;
    return VALUE_BELOW_OR_EQUAL_0;
  };

  const getCampaignActivityProgressForDateField = (): { percentage: number, color: string } => {
    if(isBlank(endDate)) return { percentage: 0, color: getCampaignProgressBarColorAsPerPercentageForCampaign(true, 0) };

    const totalDuration = moment(endDate).tz(timezone).diff(moment(startDate).tz(timezone));
    const elapsedDuration = moment().tz(timezone).diff(moment(startDate).tz(timezone));

    if(elapsedDuration < 0) return { percentage: 0, color: getCampaignProgressBarColorAsPerPercentageForCampaign(true, 0) };

    if(totalDuration <= 0) return { percentage: 100, color: getCampaignProgressBarColorAsPerPercentageForCampaign(true, 100) };

    let percentage = (elapsedDuration / totalDuration) * 100;
    percentage = parseFloat(Math.max(0, Math.min(percentage, 100)).toFixed(0));

    return { percentage, color: getCampaignProgressBarColorAsPerPercentageForCampaign(true, percentage) };
  };

  const getCampaignActivityProgressForMoneyField = (): { percentage: number, color: string } => {
    let percentage = Math.abs((actualExpense.value || 1) / (estimatedBudget.value || 1)) * 100;
    percentage = parseFloat(Math.max(0, Math.min(percentage, 100)).toFixed(0));

    return { percentage, color: getCampaignProgressBarColorAsPerPercentageForCampaign(false, percentage) };
  };

  const redirectToUrl = (e: React.MouseEvent<HTMLAnchorElement>, url: string) => {
    {
      e.stopPropagation();

      const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);

      shouldOpenInNewTab ? window.open(url, '_blank') : history.push(url);
    }
  };

  const getMultiActionModalOptions = () => {
    const options = [];
    const canDeleteCampaignActivity = isActionAllowed([DELETE], recordActions);
    const canEditCampaignActivity = isActionAllowed([UPDATE], recordActions);

    options.push({ label: 'View', action: (e) => { e.stopPropagation(); history.push(`/sales/${entities.CAMPAIGNS}/details/${campaign.id}`, { activityId: id }); } });

    if(canEditCampaignActivity) {
      options.push({
        label: 'Edit',
        action: (e) => {
          e.stopPropagation();

          const url = (referrer === ReferrerFlow.CAMPAIGN_LIST) ? `/sales/${entities.CAMPAIGNS}/view/${campaign.id}` : `/sales/${entities.CAMPAIGNS}/activities/edit/${id}`;
          history.push(url, { referrer, activityId: id, campaignId: (referrer === ReferrerFlow.CAMPAIGN_ACTIVITY_LIST) ? campaign.id : null });
        }
      });
    }

    if(canEditCampaignActivity) {
      const requiredActions = getAvailableActionsForCampaignActivityAsPerStatus(status);

      requiredActions.forEach((action: CampaignActionType) => {
        options.push({
          label: (action === CampaignActionType.COMPLETE) ? 'Mark as complete' : capitalizeLabel(action),
          action: (e) => {
            e.stopPropagation();

            if(status === CampaignActivityStatus.PROCESSED) {
              runParticularCampaignActivity(action);

              return;
            }

            toggleModalFor(action);
          }
        });
      });
    }

    if(canDeleteCampaignActivity) {
      const shouldDisabled = isCampaignActivityDisabled(status);

      options.push({
        label: 'Delete',
        isDisabled: shouldDisabled,
        action: (e) => { e.stopPropagation(); toggleModalFor('delete_campaign_activity'); },
        tooltip: shouldDisabled ? 'This activity has already been started and cannot be deleted' : ''
      });
    }

    return options;
  };

  const deleteDraftCampaignActivity = () => {
    deleteCampaignActivity(id, campaign.id, history)
      .then(() => {
        fetchData('delete_campaign_activity');
        toastr.success('Success', `${capitalizeLabel(routeToEntity(entities.CAMPAIGN_ACTIVITIES))} Deleted`);
      })
      .catch(err => showErrorToast(err));
  };

  const runParticularCampaignActivity = (action: CampaignActionType) => {
    runCampaignActivity(id, campaign.id, action, history)
      .then(() => {
        fetchData('run_campaign_activity');
        toastr.success('Success', `Activity ${capitalizeLabel(getCampaignActionName(action))}`);
      })
      .catch(err => showErrorToast(err));
  };

  const renderModalPopup = () => {
    switch(showModalFor) {
      case 'delete_campaign_activity':
      case CampaignActionType.START:
      case CampaignActionType.PAUSE:
      case CampaignActionType.RESUME:
      case CampaignActionType.COMPLETE:
        const { title, message, confirmBtn } = getConfirmModalPropsAsPerCampaignActionType(entities.CAMPAIGN_ACTIVITIES, name, showModalFor);

        return (
          <ConfirmModal
            show
            title={title}
            message={message}
            confirmBtnLabel={confirmBtn.label}
            onCancel={() => toggleModalFor(null)}
            confirmBtnClass={confirmBtn.className}
            onConfirm={() => {
              toggleModalFor(null);

              if(showModalFor === 'delete_campaign_activity') {
                deleteDraftCampaignActivity();
                return;
              }

              runParticularCampaignActivity(showModalFor);
            }}
          />
        );
    }
  };

  return (
    <React.Fragment>
      <div className="campaign-activity__card" onClick={() => history.push(`/sales/${entities.CAMPAIGNS}/details/${campaign.id}`, { activityId: id })}>
        <div className="activity-card__header">
          <div className="d-flex">
            <img src={`${whatsappLogoIcon}`} alt={type} />

            { (referrer === ReferrerFlow.CAMPAIGN_ACTIVITY_LIST) ?
                <div className="campaign-activity__name">
                  <h3 className="f-15">{name}</h3>

                  <span className="f-13">
                    {capitalizeLabel(routeToEntity(entities.CAMPAIGNS))} Name:&nbsp;

                    <a className="link-primary" onClick={e => redirectToUrl(e, `/sales/${entities.CAMPAIGNS}/details/${campaign.id}`)}>{campaign.name}</a>
                  </span>
                </div>
              :
                <h3 className="f-15 text-break">{name}</h3>
            }
          </div>

          <div className="header-actions">
            { !isBlank(bulkJobId) && <a className="bulk-job__link link-primary" onClick={e => redirectToUrl(e, `/setup/data-management/bulk-jobs/list?id=${bulkJobId}`)}>View Bulk Job</a>}

            <div
              className="activity-status"
              style={{
                backgroundColor: CampaignActivityStatusColors[status].backgroundColor,
                color: CampaignActivityStatusColors[status].color,
                border: `1px solid ${CampaignActivityStatusColors[status].color}`
              }}
            >
              {convertSnakeCaseTextToLabel(status)}
            </div>

            { (getMultiActionModalOptions().length > 0) &&
              <MultiActionModal
                options={getMultiActionModalOptions()}
                icon={< i className="fas fa-ellipsis-v mt-1" />}
              />
            }
          </div>
        </div>

        <div className="activity-card__body">
          <div className="d-flex justify-content-between">
            <div className="activity-financial">
              <span className="name">Actual Expense</span>
              <ReadOnlyMoney {...actualExpense} />
            </div>

            <div className="activity-financial">
              <span className="name">Total Engagement</span>
              <div>{totalEngagement ?? '-'}</div>
            </div>

            <div className="activity-financial">
              <span className="name">{getEntityLabelPlural(entityLabelMap, entity)} Generated</span>
              <div>{recordsGenerated ?? '-'}</div>
            </div>
          </div>

          <div className={`activity-progress ${flexDirectionForProgressBar}`}>
            <div className="activity-duration">
              <div className="d-flex justify-content-sm-between mb-1">
                <span>Start Date: {formatDate(startDate, dateFormat, timezone)}</span>
                <span>End Date: {formatDate(endDate, dateFormat, timezone)}</span>
              </div>

              <ProgressBar
                width={getCampaignActivityProgressForDateField().percentage}
                indicatorColor={getCampaignActivityProgressForDateField().color}
              />
            </div>

            <div className="activity-budget">
              <div className="d-flex justify-content-sm-between mb-1">
                <span>Total Spend</span>

                <div className="d-flex f-13">
                  <ReadOnlyMoney {...actualExpense} />
                  <span>&nbsp;/&nbsp;</span>
                  <ReadOnlyMoney {...estimatedBudget} />
                </div>
              </div>

              <ProgressBar
                width={getCampaignActivityProgressForMoneyField().percentage}
                indicatorColor={getCampaignActivityProgressForMoneyField().color}
              />
            </div>
          </div>
        </div>
      </div>

      { !isBlank(showModalFor) && renderModalPopup() }
    </React.Fragment>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  entityLabelMap: state.loginForm.entityLabelMap,
  timezone: state.loginForm.userPreferences.timezone,
  dateFormat: state.loginForm.userPreferences.dateFormat
});

export default connect(mapStateToProps, {})(CampaignActivityCard);
