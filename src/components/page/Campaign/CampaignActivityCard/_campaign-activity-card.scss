@import '../../../../assets/styles/scss/base/variables';

.campaign-activity__card {
  padding: 1rem;
  cursor: pointer;
  background: $white;
  border-radius: 0.8rem;

  .activity-card {
    &__header {
      display: flex;
      justify-content: space-between;

      h3 {
        align-self: center;
        margin-left: 0.75rem;
        margin-bottom: 0 !important;
      }

      .campaign-activity__name {
        padding-top: 0.5rem;
        word-break: break-word;

        span {
          margin-left: 0.75rem;
        }
      }

      .header-actions {
        gap: 0.8rem;
        display: flex;

        .bulk-job__link {
          font-size: 0.8rem;
          margin-top: 0.7rem;
        }

        .activity-status {
          display: flex;
          height: 1.45rem;
          font-size: 0.8rem;
          padding: 0 0.75rem;
          margin: 0.375rem 0;
          align-items: center;
          justify-content: center;
          border-radius: 0.688rem;
        }
  
        .far.fa-ellipsis-v {
          font-size: 1.25rem;
        }
      }
    }

    &__body {
      .activity-financial {
        margin-top: 0.75rem;
        margin-bottom: 0.5rem;

        .name {
          color: $gray-650;
          font-size: 0.8rem;
        }

        .name + div {
          font-size: 0.9rem;
        }

        &:last-child {
          max-width: 33%;
          word-break: break-word;
        }
      }

      .activity-progress {
        gap: 1.25rem;
        display: flex;
        margin-bottom: 0.8rem;

        .activity-duration, .activity-budget {
          flex: 1 1 50%;

          span {
            color: $gray-650;
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}