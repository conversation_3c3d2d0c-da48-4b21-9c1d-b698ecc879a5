// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CampaignActivityOverview component should render component 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <Connect(CampaignActivityOverview)
        activity={
          Object {
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "bulkJobId": null,
            "campaign": Object {
              "id": 123,
              "name": "campaign name",
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew <PERSON>",
            },
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "entity": "LEAD",
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "filters": Object {
              "jsonRule": Object {
                "condition": "AND",
                "rules": Array [
                  Object {
                    "field": "id",
                    "id": "id",
                    "operator": "in",
                    "type": "long",
                    "value": "553092,553052",
                  },
                ],
                "valid": true,
              },
            },
            "id": 1,
            "lastPausedAt": "2021-09-10T04:04:23.835Z",
            "lastPausedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "name": "Kylas Activity",
            "payload": Object {
              "connectedAccount": Object {
                "id": 1,
                "name": "Whatsapp Business Account",
              },
              "sentTo": "PRIMARY_PHONE_NUMBER",
              "type": "WHATSAPP",
              "whatsappTemplate": Object {
                "id": 47,
                "name": "Welcome to Kylas",
              },
            },
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
        fetchData={[MockFunction]}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        numberFormat="INDIAN_NUMBER_FORMAT"
        recordActions={
          Object {
            "delete": true,
            "read": true,
            "readAll": true,
            "update": true,
            "updateAll": true,
            "write": true,
          }
        }
        timezone="Asia/Calcutta"
      >
        <CampaignActivityOverview
          activity={
            Object {
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "bulkJobId": null,
              "campaign": Object {
                "id": 123,
                "name": "campaign name",
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "entity": "LEAD",
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "filters": Object {
                "jsonRule": Object {
                  "condition": "AND",
                  "rules": Array [
                    Object {
                      "field": "id",
                      "id": "id",
                      "operator": "in",
                      "type": "long",
                      "value": "553092,553052",
                    },
                  ],
                  "valid": true,
                },
              },
              "id": 1,
              "lastPausedAt": "2021-09-10T04:04:23.835Z",
              "lastPausedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "name": "Kylas Activity",
              "payload": Object {
                "connectedAccount": Object {
                  "id": 1,
                  "name": "Whatsapp Business Account",
                },
                "sentTo": "PRIMARY_PHONE_NUMBER",
                "type": "WHATSAPP",
                "whatsappTemplate": Object {
                  "id": 47,
                  "name": "Welcome to Kylas",
                },
              },
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "DRAFT",
              "totalEngagement": 0,
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
            }
          }
          dispatch={[Function]}
          entityLabelMap={
            Object {
              "COMPANY": Object {
                "displayName": "Company",
                "displayNamePlural": "Companies",
              },
              "CONTACT": Object {
                "displayName": "Student",
                "displayNamePlural": "Contacts",
              },
              "DEAL": Object {
                "displayName": "Deal",
                "displayNamePlural": "Deals",
              },
              "LEAD": Object {
                "displayName": "Teacher",
                "displayNamePlural": "Teachers",
              },
              "TASK": Object {
                "displayName": "Task",
                "displayNamePlural": "Tasks",
              },
              "TEAM": Object {
                "displayName": "Team",
                "displayNamePlural": "Teams",
              },
              "USER": Object {
                "displayName": "User",
                "displayNamePlural": "Users",
              },
            }
          }
          fetchData={[MockFunction]}
          history={
            Object {
              "push": [MockFunction],
            }
          }
          numberFormat="INDIAN_NUMBER_FORMAT"
          recordActions={
            Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            }
          }
          timezone="Asia/Calcutta"
        >
          <div
            className="campaign-activity__overview"
          >
            <div
              className="d-flex justify-content-between"
            >
              <strong
                className="align-self-center"
              >
                Activity Overview
              </strong>
              <div
                className="actions"
              >
                <div
                  className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
                  onClick={[Function]}
                >
                  <EditIcon>
                    <svg
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                    >
                      <defs>
                        <clipPath
                          id="clip-Ic_Edit"
                        >
                          <rect
                            height="16"
                            width="16"
                          />
                        </clipPath>
                      </defs>
                      <g
                        clipPath="url(#clip-Ic_Edit)"
                        id="Ic_Edit"
                      >
                        <path
                          d="M9.075,2.921l4,4L4.388,15.608.822,16a.75.75,0,0,1-.828-.828l.4-3.569L9.075,2.921Zm6.475-.6L13.671.447a1.5,1.5,0,0,0-2.122,0L9.782,2.214l4,4L15.55,4.447a1.5,1.5,0,0,0,0-2.122Z"
                          data-name="Icon/Edit"
                          fill="#fff"
                          id="Icon_Edit"
                          transform="translate(0.011 -0.007)"
                        />
                      </g>
                    </svg>
                  </EditIcon>
                </div>
                <MultiActionModal
                  className="btn-down-arrow btn-primary"
                  icon={<DropdownIcon />}
                  options={
                    Array [
                      Object {
                        "action": [Function],
                        "label": "Start",
                      },
                      Object {
                        "action": [Function],
                        "isDisabled": false,
                        "label": "Delete",
                        "tooltip": "",
                      },
                    ]
                  }
                >
                  <div
                    className="dropdown "
                  >
                    <button
                      aria-expanded="false"
                      aria-haspopup="true"
                      className="btn dropdown-toggle btn-down-arrow btn-primary"
                      data-offset="0,3"
                      data-toggle="dropdown"
                      onClick={[Function]}
                      style={
                        Object {
                          "cursor": "pointer",
                        }
                      }
                      type="button"
                    >
                      <DropdownIcon>
                        <svg
                          height={16}
                          viewBox="0 0 16 16"
                          width={16}
                        >
                          <defs>
                            <clipPath
                              id="clip-Ic_Dropdown"
                            >
                              <rect
                                height="16"
                                width="16"
                              />
                            </clipPath>
                          </defs>
                          <g
                            clipPath="url(#clip-Ic_Dropdown)"
                            id="Ic_Dropdown"
                          >
                            <path
                              d="M17.944,288h9.981a1.007,1.007,0,0,1,.713,1.718L23.647,294.7a1,1,0,0,1-1.422,0l-4.995-4.987A1.007,1.007,0,0,1,17.944,288Z"
                              data-name="Icon/DropdownDown"
                              fill="#fff"
                              id="Icon_DropdownDown"
                              transform="translate(-14.934 -283)"
                            />
                          </g>
                        </svg>
                      </DropdownIcon>
                    </button>
                    <div
                      className="dropdown-menu dropdown-menu-right"
                      style={
                        Object {
                          "left": "20px",
                          "position": "absolute",
                          "top": "0px",
                          "transform": "translate3d(-129px, 18px, 0px)",
                          "willChange": "transform",
                        }
                      }
                      x-placement="bottom-end"
                    >
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="0_option"
                        onClick={[Function]}
                      >
                        Start
                      </a>
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="1_option"
                        onClick={[Function]}
                      >
                        Delete
                      </a>
                    </div>
                  </div>
                </MultiActionModal>
              </div>
            </div>
            <div
              className="d-flex"
              style={
                Object {
                  "gap": "1.5rem",
                  "marginTop": "1.25rem",
                }
              }
            >
              <div
                className="campaign-activity__report w-65"
              >
                <div
                  className="campaign-activity__not-started"
                >
                  <div
                    className="d-flex flex-column align-items-center m-auto"
                  >
                    <img
                      alt="Start Activity"
                      className="empty__report-data"
                      src="test-file-stub"
                    />
                    <div
                      className="mt-1"
                    >
                      Your activity is still in draft mode
                    </div>
                    <div
                      className="mt-2 add-instruction"
                    >
                      To begin tracking performance and viewing analytics, start the activity or complete its setup
                    </div>
                  </div>
                </div>
              </div>
              <div
                className="w-35"
              >
                <Connect(CampaignRelatedFormValues)
                  entity="campaign-activities"
                  formValues={
                    Object {
                      "actualExpense": Object {
                        "currencyId": 400,
                        "value": 110000,
                      },
                      "bulkJobId": null,
                      "campaign": Object {
                        "id": 123,
                        "name": "campaign name",
                      },
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "createdAt": "2021-09-10T04:04:23.835Z",
                      "createdBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "endDate": "2021-09-11T04:04:23.835Z",
                      "endedAt": "2021-09-10T04:04:23.835Z",
                      "endedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "entity": Object {
                        "disabled": false,
                        "displayName": "Lead",
                        "id": "LEAD",
                        "name": "LEAD",
                        "systemDefault": true,
                      },
                      "estimatedBudget": Object {
                        "currencyId": 400,
                        "value": 100000,
                      },
                      "filters": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "id": 1,
                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                      "lastPausedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "name": "Kylas Activity",
                      "recordActions": Object {
                        "delete": true,
                        "read": true,
                        "readAll": true,
                        "update": true,
                        "updateAll": true,
                        "write": true,
                      },
                      "recordsGenerated": 0,
                      "sentTo": Object {
                        "disabled": false,
                        "displayName": "Primary Phone number",
                        "id": "PRIMARY_PHONE_NUMBER",
                        "name": "PRIMARY_PHONE_NUMBER",
                        "systemDefault": true,
                      },
                      "startDate": "2021-09-10T04:04:23.835Z",
                      "startedAt": "2021-09-10T04:04:23.835Z",
                      "startedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "status": "DRAFT",
                      "totalEngagement": 0,
                      "type": Object {
                        "disabled": false,
                        "displayName": "WhatsApp",
                        "id": "WHATSAPP",
                        "name": "WHATSAPP",
                        "systemDefault": true,
                      },
                      "updatedAt": "2021-09-10T04:04:23.835Z",
                      "updatedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "utmCampaign": "utm campaign",
                      "utmContent": "content",
                      "utmMedium": "medium",
                      "utmSource": "google",
                      "utmTerm": "term",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    }
                  }
                >
                  <CampaignRelatedFormValues
                    dateFormat="MMM D, YYYY [at] h:mm a"
                    dispatch={[Function]}
                    entity="campaign-activities"
                    entityLabelMap={
                      Object {
                        "COMPANY": Object {
                          "displayName": "Company",
                          "displayNamePlural": "Companies",
                        },
                        "CONTACT": Object {
                          "displayName": "Student",
                          "displayNamePlural": "Contacts",
                        },
                        "DEAL": Object {
                          "displayName": "Deal",
                          "displayNamePlural": "Deals",
                        },
                        "LEAD": Object {
                          "displayName": "Teacher",
                          "displayNamePlural": "Teachers",
                        },
                        "TASK": Object {
                          "displayName": "Task",
                          "displayNamePlural": "Tasks",
                        },
                        "TEAM": Object {
                          "displayName": "Team",
                          "displayNamePlural": "Teams",
                        },
                        "USER": Object {
                          "displayName": "User",
                          "displayNamePlural": "Users",
                        },
                      }
                    }
                    formValues={
                      Object {
                        "actualExpense": Object {
                          "currencyId": 400,
                          "value": 110000,
                        },
                        "bulkJobId": null,
                        "campaign": Object {
                          "id": 123,
                          "name": "campaign name",
                        },
                        "connectedAccount": Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        },
                        "createdAt": "2021-09-10T04:04:23.835Z",
                        "createdBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "endDate": "2021-09-11T04:04:23.835Z",
                        "endedAt": "2021-09-10T04:04:23.835Z",
                        "endedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "entity": Object {
                          "disabled": false,
                          "displayName": "Lead",
                          "id": "LEAD",
                          "name": "LEAD",
                          "systemDefault": true,
                        },
                        "estimatedBudget": Object {
                          "currencyId": 400,
                          "value": 100000,
                        },
                        "filters": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "id": 1,
                        "lastPausedAt": "2021-09-10T04:04:23.835Z",
                        "lastPausedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "name": "Kylas Activity",
                        "recordActions": Object {
                          "delete": true,
                          "read": true,
                          "readAll": true,
                          "update": true,
                          "updateAll": true,
                          "write": true,
                        },
                        "recordsGenerated": 0,
                        "sentTo": Object {
                          "disabled": false,
                          "displayName": "Primary Phone number",
                          "id": "PRIMARY_PHONE_NUMBER",
                          "name": "PRIMARY_PHONE_NUMBER",
                          "systemDefault": true,
                        },
                        "startDate": "2021-09-10T04:04:23.835Z",
                        "startedAt": "2021-09-10T04:04:23.835Z",
                        "startedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "status": "DRAFT",
                        "totalEngagement": 0,
                        "type": Object {
                          "disabled": false,
                          "displayName": "WhatsApp",
                          "id": "WHATSAPP",
                          "name": "WHATSAPP",
                          "systemDefault": true,
                        },
                        "updatedAt": "2021-09-10T04:04:23.835Z",
                        "updatedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "utmCampaign": "utm campaign",
                        "utmContent": "content",
                        "utmMedium": "medium",
                        "utmSource": "google",
                        "utmTerm": "term",
                        "whatsappTemplate": Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        },
                      }
                    }
                    timezone="Asia/Calcutta"
                  >
                    <div
                      className="campaign-related__form-values campaign__draft-status"
                    />
                  </CampaignRelatedFormValues>
                </Connect(CampaignRelatedFormValues)>
              </div>
            </div>
          </div>
        </CampaignActivityOverview>
      </Connect(CampaignActivityOverview)>
    </Router>
  </BrowserRouter>
</Provider>
`;

exports[`CampaignActivityOverview component should render component for campaign activity analytics 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <Connect(CampaignActivityOverview)
        activity={
          Object {
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "bulkJobId": null,
            "campaign": Object {
              "id": 123,
              "name": "campaign name",
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "entity": "LEAD",
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "filters": Object {
              "jsonRule": Object {
                "condition": "AND",
                "rules": Array [
                  Object {
                    "field": "id",
                    "id": "id",
                    "operator": "in",
                    "type": "long",
                    "value": "553092,553052",
                  },
                ],
                "valid": true,
              },
            },
            "id": 1,
            "lastPausedAt": "2021-09-10T04:04:23.835Z",
            "lastPausedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "name": "Kylas Activity",
            "payload": Object {
              "connectedAccount": Object {
                "id": 1,
                "name": "Whatsapp Business Account",
              },
              "sentTo": "PRIMARY_PHONE_NUMBER",
              "type": "WHATSAPP",
              "whatsappTemplate": Object {
                "id": 47,
                "name": "Welcome to Kylas",
              },
            },
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "IN_PROGRESS",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
        fetchData={[MockFunction]}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        numberFormat="INDIAN_NUMBER_FORMAT"
        recordActions={
          Object {
            "delete": true,
            "read": true,
            "readAll": true,
            "update": true,
            "updateAll": true,
            "write": true,
          }
        }
        timezone="Asia/Calcutta"
      >
        <CampaignActivityOverview
          activity={
            Object {
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "bulkJobId": null,
              "campaign": Object {
                "id": 123,
                "name": "campaign name",
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "entity": "LEAD",
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "filters": Object {
                "jsonRule": Object {
                  "condition": "AND",
                  "rules": Array [
                    Object {
                      "field": "id",
                      "id": "id",
                      "operator": "in",
                      "type": "long",
                      "value": "553092,553052",
                    },
                  ],
                  "valid": true,
                },
              },
              "id": 1,
              "lastPausedAt": "2021-09-10T04:04:23.835Z",
              "lastPausedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "name": "Kylas Activity",
              "payload": Object {
                "connectedAccount": Object {
                  "id": 1,
                  "name": "Whatsapp Business Account",
                },
                "sentTo": "PRIMARY_PHONE_NUMBER",
                "type": "WHATSAPP",
                "whatsappTemplate": Object {
                  "id": 47,
                  "name": "Welcome to Kylas",
                },
              },
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "IN_PROGRESS",
              "totalEngagement": 0,
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
            }
          }
          dispatch={[Function]}
          entityLabelMap={
            Object {
              "COMPANY": Object {
                "displayName": "Company",
                "displayNamePlural": "Companies",
              },
              "CONTACT": Object {
                "displayName": "Student",
                "displayNamePlural": "Contacts",
              },
              "DEAL": Object {
                "displayName": "Deal",
                "displayNamePlural": "Deals",
              },
              "LEAD": Object {
                "displayName": "Teacher",
                "displayNamePlural": "Teachers",
              },
              "TASK": Object {
                "displayName": "Task",
                "displayNamePlural": "Tasks",
              },
              "TEAM": Object {
                "displayName": "Team",
                "displayNamePlural": "Teams",
              },
              "USER": Object {
                "displayName": "User",
                "displayNamePlural": "Users",
              },
            }
          }
          fetchData={[MockFunction]}
          history={
            Object {
              "push": [MockFunction],
            }
          }
          numberFormat="INDIAN_NUMBER_FORMAT"
          recordActions={
            Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            }
          }
          timezone="Asia/Calcutta"
        >
          <div
            className="campaign-activity__overview"
          >
            <div
              className="d-flex justify-content-between"
            >
              <strong
                className="align-self-center"
              >
                Activity Overview
              </strong>
              <div
                className="actions"
              >
                <a
                  className="recipient-status__link link-primary"
                  onClick={[Function]}
                >
                  View Recipient Status
                </a>
                <div
                  className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
                  onClick={[Function]}
                >
                  <EditIcon>
                    <svg
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                    >
                      <defs>
                        <clipPath
                          id="clip-Ic_Edit"
                        >
                          <rect
                            height="16"
                            width="16"
                          />
                        </clipPath>
                      </defs>
                      <g
                        clipPath="url(#clip-Ic_Edit)"
                        id="Ic_Edit"
                      >
                        <path
                          d="M9.075,2.921l4,4L4.388,15.608.822,16a.75.75,0,0,1-.828-.828l.4-3.569L9.075,2.921Zm6.475-.6L13.671.447a1.5,1.5,0,0,0-2.122,0L9.782,2.214l4,4L15.55,4.447a1.5,1.5,0,0,0,0-2.122Z"
                          data-name="Icon/Edit"
                          fill="#fff"
                          id="Icon_Edit"
                          transform="translate(0.011 -0.007)"
                        />
                      </g>
                    </svg>
                  </EditIcon>
                </div>
                <MultiActionModal
                  className="btn-down-arrow btn-primary"
                  icon={<DropdownIcon />}
                  options={
                    Array [
                      Object {
                        "action": [Function],
                        "label": "Pause",
                      },
                      Object {
                        "action": [Function],
                        "label": "Mark as complete",
                      },
                      Object {
                        "action": [Function],
                        "isDisabled": true,
                        "label": "Delete",
                        "tooltip": "This activity has already been started and cannot be deleted",
                      },
                    ]
                  }
                >
                  <div
                    className="dropdown "
                  >
                    <button
                      aria-expanded="false"
                      aria-haspopup="true"
                      className="btn dropdown-toggle btn-down-arrow btn-primary"
                      data-offset="0,3"
                      data-toggle="dropdown"
                      onClick={[Function]}
                      style={
                        Object {
                          "cursor": "pointer",
                        }
                      }
                      type="button"
                    >
                      <DropdownIcon>
                        <svg
                          height={16}
                          viewBox="0 0 16 16"
                          width={16}
                        >
                          <defs>
                            <clipPath
                              id="clip-Ic_Dropdown"
                            >
                              <rect
                                height="16"
                                width="16"
                              />
                            </clipPath>
                          </defs>
                          <g
                            clipPath="url(#clip-Ic_Dropdown)"
                            id="Ic_Dropdown"
                          >
                            <path
                              d="M17.944,288h9.981a1.007,1.007,0,0,1,.713,1.718L23.647,294.7a1,1,0,0,1-1.422,0l-4.995-4.987A1.007,1.007,0,0,1,17.944,288Z"
                              data-name="Icon/DropdownDown"
                              fill="#fff"
                              id="Icon_DropdownDown"
                              transform="translate(-14.934 -283)"
                            />
                          </g>
                        </svg>
                      </DropdownIcon>
                    </button>
                    <div
                      className="dropdown-menu dropdown-menu-right"
                      style={
                        Object {
                          "left": "20px",
                          "position": "absolute",
                          "top": "0px",
                          "transform": "translate3d(-129px, 18px, 0px)",
                          "willChange": "transform",
                        }
                      }
                      x-placement="bottom-end"
                    >
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="0_option"
                        onClick={[Function]}
                      >
                        Pause
                      </a>
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="1_option"
                        onClick={[Function]}
                      >
                        Mark as complete
                      </a>
                      <TooltipInitialiseContainer
                        key="2_option"
                      >
                        <div
                          className=""
                          onClick={[Function]}
                        >
                          <div
                            data-placement="left"
                            data-toggle="tooltip"
                            onClick={[Function]}
                            title="This activity has already been started and cannot be deleted"
                          >
                            <a
                              className="dropdown-item disabled"
                              href="javascript:void(0);"
                              key="2_option"
                              onClick={[Function]}
                            >
                              Delete
                            </a>
                          </div>
                        </div>
                      </TooltipInitialiseContainer>
                    </div>
                  </div>
                </MultiActionModal>
              </div>
            </div>
            <div
              className="d-flex"
              style={
                Object {
                  "gap": "1.5rem",
                  "marginTop": "1.25rem",
                }
              }
            >
              <div
                className="campaign-activity__report w-65"
              >
                <div
                  className="campaign-activity__analytics"
                >
                  <CampaignActivitySummary
                    actualExpense={
                      Object {
                        "currencyId": 400,
                        "value": 110000,
                      }
                    }
                    entityName="Teachers"
                    estimatedBudget={
                      Object {
                        "currencyId": 400,
                        "value": 100000,
                      }
                    }
                    recordsGenerated={0}
                    totalEngagement={0}
                  >
                    <div
                      className="campaign-activity__summary"
                    >
                      <div>
                        <span
                          className="label"
                        >
                          Estimated Budget
                        </span>
                        <Connect(ReadOnlyMoney)
                          currencyId={400}
                          value={100000}
                        >
                          <ReadOnlyMoney
                            currencies={
                              Array [
                                Object {
                                  "displayName": "Indian Rupee",
                                  "id": 13,
                                  "name": "INR",
                                },
                              ]
                            }
                            currencyId={400}
                            dispatch={[Function]}
                            numberFormat="INDIAN_NUMBER_FORMAT"
                            value={100000}
                          >
                            <TooltipInitialiseContainer>
                              <div
                                className=""
                                onClick={[Function]}
                              >
                                <span
                                  data-html="true"
                                  data-placement="top"
                                  data-toggle="tooltip"
                                  title=" 100,000"
                                >
                                   1 L
                                </span>
                              </div>
                            </TooltipInitialiseContainer>
                          </ReadOnlyMoney>
                        </Connect(ReadOnlyMoney)>
                      </div>
                      <div>
                        <span
                          className="label"
                        >
                          Actual Expense
                        </span>
                        <Connect(ReadOnlyMoney)
                          currencyId={400}
                          value={110000}
                        >
                          <ReadOnlyMoney
                            currencies={
                              Array [
                                Object {
                                  "displayName": "Indian Rupee",
                                  "id": 13,
                                  "name": "INR",
                                },
                              ]
                            }
                            currencyId={400}
                            dispatch={[Function]}
                            numberFormat="INDIAN_NUMBER_FORMAT"
                            value={110000}
                          >
                            <TooltipInitialiseContainer>
                              <div
                                className=""
                                onClick={[Function]}
                              >
                                <span
                                  data-html="true"
                                  data-placement="top"
                                  data-toggle="tooltip"
                                  title=" 110,000"
                                >
                                   1.1 L
                                </span>
                              </div>
                            </TooltipInitialiseContainer>
                          </ReadOnlyMoney>
                        </Connect(ReadOnlyMoney)>
                      </div>
                      <div>
                        <span
                          className="label"
                        >
                          Total Engagement
                        </span>
                        <div>
                          0
                        </div>
                      </div>
                      <div
                        className="records-generated"
                      >
                        <span
                          className="label"
                        >
                          Teachers
                           Generated
                        </span>
                        <div>
                          0
                        </div>
                      </div>
                    </div>
                  </CampaignActivitySummary>
                  <div
                    className="row"
                  >
                    <div
                      className="col-6"
                    >
                      <WithAbortController
                        activityId={1}
                        campaignId={123}
                        endDate="2021-09-11T04:04:23.835Z"
                        entity="campaign-activities"
                        history={
                          Object {
                            "push": [MockFunction],
                          }
                        }
                        key="1"
                        numberFormat="INDIAN_NUMBER_FORMAT"
                        startDate="2021-09-10T04:04:23.835Z"
                        timezone="Asia/Calcutta"
                        type="Response Count"
                      >
                        <Component
                          abortSignal={AbortSignal {}}
                          activityId={1}
                          campaignId={123}
                          endDate="2021-09-11T04:04:23.835Z"
                          entity="campaign-activities"
                          history={
                            Object {
                              "push": [MockFunction],
                            }
                          }
                          numberFormat="INDIAN_NUMBER_FORMAT"
                          startDate="2021-09-10T04:04:23.835Z"
                          timezone="Asia/Calcutta"
                          type="Response Count"
                        >
                          <CampaignActivityFunnelChart
                            abortSignal={AbortSignal {}}
                            activityId={1}
                            campaignId={123}
                            data={
                              Array [
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Sent",
                                  "value": 1000,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Delivered",
                                  "value": 800,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Read",
                                  "value": 500,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Failed",
                                  "value": 200,
                                },
                              ]
                            }
                            endDate="2021-09-11T04:04:23.835Z"
                            entity="campaign-activities"
                            error={null}
                            fetchData={[Function]}
                            history={
                              Object {
                                "push": [MockFunction],
                              }
                            }
                            loading={false}
                            numberFormat="INDIAN_NUMBER_FORMAT"
                            startDate="2021-09-10T04:04:23.835Z"
                            timezone="Asia/Calcutta"
                            type="Response Count"
                          >
                            <div
                              className="campaign-activity__funnel-chart"
                            >
                              <div
                                className="d-flex justify-content-between"
                              >
                                <div
                                  className="f-14"
                                >
                                  Response Count
                                </div>
                                <Connect(DateRangePickerComponent)
                                  defaultDateRange={
                                    Object {
                                      "endDate": "2021-09-11T18:29:59.999Z",
                                      "startDate": "2021-09-09T18:30:00.000Z",
                                    }
                                  }
                                  isDateRangeEditable={true}
                                  uniqueKey="campaign-activities_123_1_Response Count"
                                  updateDateRange={[Function]}
                                >
                                  <DateRangePickerComponent
                                    dateFormat="MMM D, YYYY [at] h:mm a"
                                    defaultDateRange={
                                      Object {
                                        "endDate": "2021-09-11T18:29:59.999Z",
                                        "startDate": "2021-09-09T18:30:00.000Z",
                                      }
                                    }
                                    dispatch={[Function]}
                                    isDateRangeEditable={true}
                                    timezone="Asia/Calcutta"
                                    uniqueKey="campaign-activities_123_1_Response Count"
                                    updateDateRange={[Function]}
                                  >
                                    <div
                                      className="date-range__picker"
                                    >
                                      <withStyles(DateRangePicker)
                                        anchorDirection="left"
                                        appendToBody={false}
                                        block={false}
                                        calendarInfoPosition="bottom"
                                        customArrowIcon={null}
                                        customCloseIcon={null}
                                        customInputIcon={null}
                                        daySize={24}
                                        disableScroll={false}
                                        disabled={false}
                                        displayFormat="MMM D, YYYY"
                                        enableOutsideDays={false}
                                        endDate={"2021-09-11T18:29:59.999Z"}
                                        endDateId="campaign-activities_123_1_Response Count_endDate"
                                        endDatePlaceholderText="End Date"
                                        firstDayOfWeek={null}
                                        focusedInput={null}
                                        hideKeyboardShortcutsPanel={false}
                                        horizontalMargin={0}
                                        initialVisibleMonth={null}
                                        inputIconPosition="before"
                                        isDayBlocked={[Function]}
                                        isDayHighlighted={[Function]}
                                        isOutsideRange={[Function]}
                                        isRTL={false}
                                        keepFocusOnInput={false}
                                        keepOpenOnDateSelect={true}
                                        minimumNights={0}
                                        monthFormat="MMMM YYYY"
                                        navNext={
                                          <i
                                            className="svg-inline--fa fa fa-angle-right"
                                          />
                                        }
                                        navPrev={
                                          <i
                                            className="svg-inline--fa fa fa-angle-left left-button"
                                          />
                                        }
                                        noBorder={false}
                                        numberOfMonths={2}
                                        onClose={[Function]}
                                        onDatesChange={[Function]}
                                        onFocusChange={[Function]}
                                        onNextMonthClick={[Function]}
                                        onPrevMonthClick={[Function]}
                                        openDirection="down"
                                        orientation="horizontal"
                                        phrases={
                                          Object {
                                            "calendarLabel": "Calendar",
                                            "chooseAvailableEndDate": [Function],
                                            "chooseAvailableStartDate": [Function],
                                            "clearDates": "Clear Dates",
                                            "closeDatePicker": "Close",
                                            "dateIsSelected": [Function],
                                            "dateIsSelectedAsEndDate": [Function],
                                            "dateIsSelectedAsStartDate": [Function],
                                            "dateIsUnavailable": [Function],
                                            "enterKey": "Enter key",
                                            "escape": "Escape key",
                                            "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                            "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                            "homeEnd": "Home and end keys",
                                            "jumpToNextMonth": "Move forward to switch to the next month.",
                                            "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                            "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                            "keyboardShortcuts": "Keyboard Shortcuts",
                                            "leftArrowRightArrow": "Right and left arrow keys",
                                            "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                            "moveFocusByOneMonth": "Switch months.",
                                            "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                            "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                            "openThisPanel": "Open this panel.",
                                            "pageUpPageDown": "page up and page down keys",
                                            "questionMark": "Question mark",
                                            "returnFocusToInput": "Return to the date input field.",
                                            "selectFocusedDate": "Select the date in focus.",
                                            "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                            "upArrowDownArrow": "up and down arrow keys",
                                          }
                                        }
                                        readOnly={false}
                                        regular={false}
                                        renderCalendarInfo={[Function]}
                                        renderDayContents={null}
                                        renderMonthElement={[Function]}
                                        renderMonthText={null}
                                        reopenPickerOnClearDates={false}
                                        required={false}
                                        screenReaderInputMessage=""
                                        showClearDates={false}
                                        showDefaultInputIcon={false}
                                        small={false}
                                        startDate={"2021-09-09T18:30:00.000Z"}
                                        startDateId="campaign-activities_123_1_Response Count_startDate"
                                        startDatePlaceholderText="Start Date"
                                        verticalHeight={null}
                                        verticalSpacing={22}
                                        weekDayFormat="dd"
                                        withFullScreenPortal={false}
                                        withPortal={false}
                                      >
                                        <DateRangePicker
                                          anchorDirection="left"
                                          appendToBody={false}
                                          block={false}
                                          calendarInfoPosition="bottom"
                                          css={[Function]}
                                          customArrowIcon={null}
                                          customCloseIcon={null}
                                          customInputIcon={null}
                                          daySize={24}
                                          disableScroll={false}
                                          disabled={false}
                                          displayFormat="MMM D, YYYY"
                                          enableOutsideDays={false}
                                          endDate={"2021-09-11T18:29:59.999Z"}
                                          endDateId="campaign-activities_123_1_Response Count_endDate"
                                          endDatePlaceholderText="End Date"
                                          firstDayOfWeek={null}
                                          focusedInput={null}
                                          hideKeyboardShortcutsPanel={false}
                                          horizontalMargin={0}
                                          initialVisibleMonth={null}
                                          inputIconPosition="before"
                                          isDayBlocked={[Function]}
                                          isDayHighlighted={[Function]}
                                          isOutsideRange={[Function]}
                                          isRTL={false}
                                          keepFocusOnInput={false}
                                          keepOpenOnDateSelect={true}
                                          minimumNights={0}
                                          monthFormat="MMMM YYYY"
                                          navNext={
                                            <i
                                              className="svg-inline--fa fa fa-angle-right"
                                            />
                                          }
                                          navPrev={
                                            <i
                                              className="svg-inline--fa fa fa-angle-left left-button"
                                            />
                                          }
                                          noBorder={false}
                                          numberOfMonths={2}
                                          onClose={[Function]}
                                          onDatesChange={[Function]}
                                          onFocusChange={[Function]}
                                          onNextMonthClick={[Function]}
                                          onPrevMonthClick={[Function]}
                                          openDirection="down"
                                          orientation="horizontal"
                                          phrases={
                                            Object {
                                              "calendarLabel": "Calendar",
                                              "chooseAvailableEndDate": [Function],
                                              "chooseAvailableStartDate": [Function],
                                              "clearDates": "Clear Dates",
                                              "closeDatePicker": "Close",
                                              "dateIsSelected": [Function],
                                              "dateIsSelectedAsEndDate": [Function],
                                              "dateIsSelectedAsStartDate": [Function],
                                              "dateIsUnavailable": [Function],
                                              "enterKey": "Enter key",
                                              "escape": "Escape key",
                                              "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                              "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                              "homeEnd": "Home and end keys",
                                              "jumpToNextMonth": "Move forward to switch to the next month.",
                                              "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                              "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                              "keyboardShortcuts": "Keyboard Shortcuts",
                                              "leftArrowRightArrow": "Right and left arrow keys",
                                              "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                              "moveFocusByOneMonth": "Switch months.",
                                              "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                              "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                              "openThisPanel": "Open this panel.",
                                              "pageUpPageDown": "page up and page down keys",
                                              "questionMark": "Question mark",
                                              "returnFocusToInput": "Return to the date input field.",
                                              "selectFocusedDate": "Select the date in focus.",
                                              "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                              "upArrowDownArrow": "up and down arrow keys",
                                            }
                                          }
                                          readOnly={false}
                                          regular={false}
                                          renderCalendarInfo={[Function]}
                                          renderDayContents={null}
                                          renderMonthElement={[Function]}
                                          renderMonthText={null}
                                          reopenPickerOnClearDates={false}
                                          required={false}
                                          screenReaderInputMessage=""
                                          showClearDates={false}
                                          showDefaultInputIcon={false}
                                          small={false}
                                          startDate={"2021-09-09T18:30:00.000Z"}
                                          startDateId="campaign-activities_123_1_Response Count_startDate"
                                          startDatePlaceholderText="Start Date"
                                          styles={
                                            Object {
                                              "DateRangePicker": "DateRangePicker",
                                              "DateRangePicker__block": "DateRangePicker__block",
                                              "DateRangePicker_closeButton": "DateRangePicker_closeButton",
                                              "DateRangePicker_closeButton_svg": "DateRangePicker_closeButton_svg",
                                              "DateRangePicker_picker": "DateRangePicker_picker",
                                              "DateRangePicker_picker__directionLeft": "DateRangePicker_picker__directionLeft",
                                              "DateRangePicker_picker__directionRight": "DateRangePicker_picker__directionRight",
                                              "DateRangePicker_picker__fullScreenPortal": "DateRangePicker_picker__fullScreenPortal",
                                              "DateRangePicker_picker__portal": "DateRangePicker_picker__portal",
                                              "DateRangePicker_picker__rtl": "DateRangePicker_picker__rtl",
                                            }
                                          }
                                          theme={
                                            Object {
                                              "reactDates": Object {
                                                "border": Object {
                                                  "input": Object {
                                                    "border": 0,
                                                    "borderBottom": "2px solid transparent",
                                                    "borderBottomFocused": "2px solid #008489",
                                                    "borderFocused": 0,
                                                    "borderLeft": 0,
                                                    "borderLeftFocused": 0,
                                                    "borderRadius": 0,
                                                    "borderRight": 0,
                                                    "borderRightFocused": 0,
                                                    "borderTop": 0,
                                                    "borderTopFocused": 0,
                                                    "outlineFocused": 0,
                                                  },
                                                  "pickerInput": Object {
                                                    "borderRadius": 2,
                                                    "borderStyle": "solid",
                                                    "borderWidth": 1,
                                                  },
                                                },
                                                "color": Object {
                                                  "background": "#fff",
                                                  "backgroundDark": "#f2f2f2",
                                                  "backgroundFocused": "#fff",
                                                  "blocked_calendar": Object {
                                                    "backgroundColor": "#cacccd",
                                                    "backgroundColor_active": "#cacccd",
                                                    "backgroundColor_hover": "#cacccd",
                                                    "borderColor": "#cacccd",
                                                    "borderColor_active": "#cacccd",
                                                    "borderColor_hover": "#cacccd",
                                                    "color": "#82888a",
                                                    "color_active": "#82888a",
                                                    "color_hover": "#82888a",
                                                  },
                                                  "blocked_out_of_range": Object {
                                                    "backgroundColor": "#fff",
                                                    "backgroundColor_active": "#fff",
                                                    "backgroundColor_hover": "#fff",
                                                    "borderColor": "#e4e7e7",
                                                    "borderColor_active": "#e4e7e7",
                                                    "borderColor_hover": "#e4e7e7",
                                                    "color": "#cacccd",
                                                    "color_active": "#cacccd",
                                                    "color_hover": "#cacccd",
                                                  },
                                                  "border": "rgb(219, 219, 219)",
                                                  "core": Object {
                                                    "border": "#dbdbdb",
                                                    "borderBright": "#f4f5f5",
                                                    "borderLight": "#e4e7e7",
                                                    "borderLighter": "#eceeee",
                                                    "borderMedium": "#c4c4c4",
                                                    "gray": "#484848",
                                                    "grayLight": "#82888a",
                                                    "grayLighter": "#cacccd",
                                                    "grayLightest": "#f2f2f2",
                                                    "primary": "#00a699",
                                                    "primaryShade_1": "#33dacd",
                                                    "primaryShade_2": "#66e2da",
                                                    "primaryShade_3": "#80e8e0",
                                                    "primaryShade_4": "#b2f1ec",
                                                    "primary_dark": "#008489",
                                                    "secondary": "#007a87",
                                                    "white": "#fff",
                                                    "yellow": "#ffe8bc",
                                                    "yellow_dark": "#ffce71",
                                                  },
                                                  "disabled": "#f2f2f2",
                                                  "highlighted": Object {
                                                    "backgroundColor": "#ffe8bc",
                                                    "backgroundColor_active": "#ffce71",
                                                    "backgroundColor_hover": "#ffce71",
                                                    "color": "#484848",
                                                    "color_active": "#484848",
                                                    "color_hover": "#484848",
                                                  },
                                                  "hoveredSpan": Object {
                                                    "backgroundColor": "#b2f1ec",
                                                    "backgroundColor_active": "#80e8e0",
                                                    "backgroundColor_hover": "#b2f1ec",
                                                    "borderColor": "#80e8e0",
                                                    "borderColor_active": "#80e8e0",
                                                    "borderColor_hover": "#80e8e0",
                                                    "color": "#007a87",
                                                    "color_active": "#007a87",
                                                    "color_hover": "#007a87",
                                                  },
                                                  "minimumNights": Object {
                                                    "backgroundColor": "#fff",
                                                    "backgroundColor_active": "#fff",
                                                    "backgroundColor_hover": "#fff",
                                                    "borderColor": "#eceeee",
                                                    "color": "#cacccd",
                                                    "color_active": "#cacccd",
                                                    "color_hover": "#cacccd",
                                                  },
                                                  "outside": Object {
                                                    "backgroundColor": "#fff",
                                                    "backgroundColor_active": "#fff",
                                                    "backgroundColor_hover": "#fff",
                                                    "color": "#484848",
                                                    "color_active": "#484848",
                                                    "color_hover": "#484848",
                                                  },
                                                  "placeholderText": "#757575",
                                                  "selected": Object {
                                                    "backgroundColor": "#00a699",
                                                    "backgroundColor_active": "#00a699",
                                                    "backgroundColor_hover": "#00a699",
                                                    "borderColor": "#00a699",
                                                    "borderColor_active": "#00a699",
                                                    "borderColor_hover": "#00a699",
                                                    "color": "#fff",
                                                    "color_active": "#fff",
                                                    "color_hover": "#fff",
                                                  },
                                                  "selectedSpan": Object {
                                                    "backgroundColor": "#66e2da",
                                                    "backgroundColor_active": "#33dacd",
                                                    "backgroundColor_hover": "#33dacd",
                                                    "borderColor": "#33dacd",
                                                    "borderColor_active": "#00a699",
                                                    "borderColor_hover": "#00a699",
                                                    "color": "#fff",
                                                    "color_active": "#fff",
                                                    "color_hover": "#fff",
                                                  },
                                                  "text": "#484848",
                                                  "textDisabled": "#dbdbdb",
                                                  "textFocused": "#007a87",
                                                },
                                                "font": Object {
                                                  "captionSize": 18,
                                                  "input": Object {
                                                    "letterSpacing_small": "0.2px",
                                                    "lineHeight": "24px",
                                                    "lineHeight_small": "18px",
                                                    "size": 19,
                                                    "size_small": 15,
                                                    "styleDisabled": "italic",
                                                  },
                                                  "size": 14,
                                                },
                                                "noScrollBarOnVerticalScrollable": false,
                                                "sizing": Object {
                                                  "arrowWidth": 24,
                                                  "inputWidth": 130,
                                                  "inputWidth_small": 97,
                                                },
                                                "spacing": Object {
                                                  "captionPaddingBottom": 37,
                                                  "captionPaddingTop": 22,
                                                  "dayPickerHorizontalPadding": 9,
                                                  "displayTextPaddingBottom": 9,
                                                  "displayTextPaddingBottom_small": 5,
                                                  "displayTextPaddingHorizontal": undefined,
                                                  "displayTextPaddingHorizontal_small": undefined,
                                                  "displayTextPaddingLeft": 11,
                                                  "displayTextPaddingLeft_small": 7,
                                                  "displayTextPaddingRight": 11,
                                                  "displayTextPaddingRight_small": 7,
                                                  "displayTextPaddingTop": 11,
                                                  "displayTextPaddingTop_small": 7,
                                                  "displayTextPaddingVertical": undefined,
                                                  "displayTextPaddingVertical_small": undefined,
                                                  "inputPadding": 0,
                                                },
                                                "zIndex": 0,
                                              },
                                            }
                                          }
                                          verticalHeight={null}
                                          verticalSpacing={22}
                                          weekDayFormat="dd"
                                          withFullScreenPortal={false}
                                          withPortal={false}
                                        >
                                          <div
                                            className="DateRangePicker DateRangePicker_1"
                                          >
                                            <OutsideClickHandler
                                              disabled={false}
                                              display="block"
                                              onOutsideClick={[Function]}
                                              useCapture={true}
                                            >
                                              <div>
                                                <DateRangePickerInputController
                                                  block={false}
                                                  customArrowIcon={null}
                                                  customCloseIcon={null}
                                                  customInputIcon={null}
                                                  disabled={false}
                                                  displayFormat="MMM D, YYYY"
                                                  endDate={"2021-09-11T18:29:59.999Z"}
                                                  endDateId="campaign-activities_123_1_Response Count_endDate"
                                                  endDatePlaceholderText="End Date"
                                                  inputIconPosition="before"
                                                  isEndDateFocused={false}
                                                  isFocused={false}
                                                  isOutsideRange={[Function]}
                                                  isRTL={false}
                                                  isStartDateFocused={false}
                                                  keepOpenOnDateSelect={true}
                                                  minimumNights={0}
                                                  noBorder={false}
                                                  onClose={[Function]}
                                                  onDatesChange={[Function]}
                                                  onFocusChange={[Function]}
                                                  onKeyDownArrowDown={[Function]}
                                                  onKeyDownQuestionMark={[Function]}
                                                  openDirection="down"
                                                  phrases={
                                                    Object {
                                                      "calendarLabel": "Calendar",
                                                      "chooseAvailableEndDate": [Function],
                                                      "chooseAvailableStartDate": [Function],
                                                      "clearDates": "Clear Dates",
                                                      "closeDatePicker": "Close",
                                                      "dateIsSelected": [Function],
                                                      "dateIsSelectedAsEndDate": [Function],
                                                      "dateIsSelectedAsStartDate": [Function],
                                                      "dateIsUnavailable": [Function],
                                                      "enterKey": "Enter key",
                                                      "escape": "Escape key",
                                                      "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                      "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                      "homeEnd": "Home and end keys",
                                                      "jumpToNextMonth": "Move forward to switch to the next month.",
                                                      "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                      "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                      "keyboardShortcuts": "Keyboard Shortcuts",
                                                      "leftArrowRightArrow": "Right and left arrow keys",
                                                      "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                      "moveFocusByOneMonth": "Switch months.",
                                                      "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                      "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                      "openThisPanel": "Open this panel.",
                                                      "pageUpPageDown": "page up and page down keys",
                                                      "questionMark": "Question mark",
                                                      "returnFocusToInput": "Return to the date input field.",
                                                      "selectFocusedDate": "Select the date in focus.",
                                                      "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                      "upArrowDownArrow": "up and down arrow keys",
                                                    }
                                                  }
                                                  readOnly={false}
                                                  regular={false}
                                                  reopenPickerOnClearDates={false}
                                                  required={false}
                                                  screenReaderMessage=""
                                                  showCaret={true}
                                                  showClearDates={false}
                                                  showDefaultInputIcon={false}
                                                  small={false}
                                                  startDate={"2021-09-09T18:30:00.000Z"}
                                                  startDateId="campaign-activities_123_1_Response Count_startDate"
                                                  startDatePlaceholderText="Start Date"
                                                  verticalSpacing={22}
                                                  withFullScreenPortal={false}
                                                >
                                                  <withStyles(DateRangePickerInput)
                                                    block={false}
                                                    customArrowIcon={null}
                                                    customCloseIcon={null}
                                                    customInputIcon={null}
                                                    disabled={false}
                                                    endDate="Sep 11, 2021"
                                                    endDateId="campaign-activities_123_1_Response Count_endDate"
                                                    endDatePlaceholderText="End Date"
                                                    inputIconPosition="before"
                                                    isEndDateFocused={false}
                                                    isFocused={false}
                                                    isRTL={false}
                                                    isStartDateFocused={false}
                                                    noBorder={false}
                                                    onClearDates={[Function]}
                                                    onEndDateChange={[Function]}
                                                    onEndDateFocus={[Function]}
                                                    onEndDateTab={[Function]}
                                                    onKeyDownArrowDown={[Function]}
                                                    onKeyDownQuestionMark={[Function]}
                                                    onStartDateChange={[Function]}
                                                    onStartDateFocus={[Function]}
                                                    onStartDateShiftTab={[Function]}
                                                    openDirection="down"
                                                    phrases={
                                                      Object {
                                                        "calendarLabel": "Calendar",
                                                        "chooseAvailableEndDate": [Function],
                                                        "chooseAvailableStartDate": [Function],
                                                        "clearDates": "Clear Dates",
                                                        "closeDatePicker": "Close",
                                                        "dateIsSelected": [Function],
                                                        "dateIsSelectedAsEndDate": [Function],
                                                        "dateIsSelectedAsStartDate": [Function],
                                                        "dateIsUnavailable": [Function],
                                                        "enterKey": "Enter key",
                                                        "escape": "Escape key",
                                                        "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                        "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                        "homeEnd": "Home and end keys",
                                                        "jumpToNextMonth": "Move forward to switch to the next month.",
                                                        "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                        "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                        "keyboardShortcuts": "Keyboard Shortcuts",
                                                        "leftArrowRightArrow": "Right and left arrow keys",
                                                        "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                        "moveFocusByOneMonth": "Switch months.",
                                                        "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                        "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                        "openThisPanel": "Open this panel.",
                                                        "pageUpPageDown": "page up and page down keys",
                                                        "questionMark": "Question mark",
                                                        "returnFocusToInput": "Return to the date input field.",
                                                        "selectFocusedDate": "Select the date in focus.",
                                                        "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                        "upArrowDownArrow": "up and down arrow keys",
                                                      }
                                                    }
                                                    readOnly={false}
                                                    regular={false}
                                                    required={false}
                                                    screenReaderMessage=""
                                                    showCaret={true}
                                                    showClearDates={false}
                                                    showDefaultInputIcon={false}
                                                    small={false}
                                                    startDate="Sep 10, 2021"
                                                    startDateId="campaign-activities_123_1_Response Count_startDate"
                                                    startDatePlaceholderText="Start Date"
                                                    verticalSpacing={22}
                                                  >
                                                    <DateRangePickerInput
                                                      block={false}
                                                      css={[Function]}
                                                      customArrowIcon={null}
                                                      customCloseIcon={null}
                                                      customInputIcon={null}
                                                      disabled={false}
                                                      endDate="Sep 11, 2021"
                                                      endDateId="campaign-activities_123_1_Response Count_endDate"
                                                      endDatePlaceholderText="End Date"
                                                      inputIconPosition="before"
                                                      isEndDateFocused={false}
                                                      isFocused={false}
                                                      isRTL={false}
                                                      isStartDateFocused={false}
                                                      noBorder={false}
                                                      onClearDates={[Function]}
                                                      onEndDateChange={[Function]}
                                                      onEndDateFocus={[Function]}
                                                      onEndDateTab={[Function]}
                                                      onKeyDownArrowDown={[Function]}
                                                      onKeyDownQuestionMark={[Function]}
                                                      onStartDateChange={[Function]}
                                                      onStartDateFocus={[Function]}
                                                      onStartDateShiftTab={[Function]}
                                                      openDirection="down"
                                                      phrases={
                                                        Object {
                                                          "calendarLabel": "Calendar",
                                                          "chooseAvailableEndDate": [Function],
                                                          "chooseAvailableStartDate": [Function],
                                                          "clearDates": "Clear Dates",
                                                          "closeDatePicker": "Close",
                                                          "dateIsSelected": [Function],
                                                          "dateIsSelectedAsEndDate": [Function],
                                                          "dateIsSelectedAsStartDate": [Function],
                                                          "dateIsUnavailable": [Function],
                                                          "enterKey": "Enter key",
                                                          "escape": "Escape key",
                                                          "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                          "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                          "homeEnd": "Home and end keys",
                                                          "jumpToNextMonth": "Move forward to switch to the next month.",
                                                          "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                          "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                          "keyboardShortcuts": "Keyboard Shortcuts",
                                                          "leftArrowRightArrow": "Right and left arrow keys",
                                                          "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                          "moveFocusByOneMonth": "Switch months.",
                                                          "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                          "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                          "openThisPanel": "Open this panel.",
                                                          "pageUpPageDown": "page up and page down keys",
                                                          "questionMark": "Question mark",
                                                          "returnFocusToInput": "Return to the date input field.",
                                                          "selectFocusedDate": "Select the date in focus.",
                                                          "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                          "upArrowDownArrow": "up and down arrow keys",
                                                        }
                                                      }
                                                      readOnly={false}
                                                      regular={false}
                                                      required={false}
                                                      screenReaderMessage=""
                                                      showCaret={true}
                                                      showClearDates={false}
                                                      showDefaultInputIcon={false}
                                                      small={false}
                                                      startDate="Sep 10, 2021"
                                                      startDateId="campaign-activities_123_1_Response Count_startDate"
                                                      startDatePlaceholderText="Start Date"
                                                      styles={
                                                        Object {
                                                          "DateRangePickerInput": "DateRangePickerInput",
                                                          "DateRangePickerInput__block": "DateRangePickerInput__block",
                                                          "DateRangePickerInput__disabled": "DateRangePickerInput__disabled",
                                                          "DateRangePickerInput__rtl": "DateRangePickerInput__rtl",
                                                          "DateRangePickerInput__showClearDates": "DateRangePickerInput__showClearDates",
                                                          "DateRangePickerInput__withBorder": "DateRangePickerInput__withBorder",
                                                          "DateRangePickerInput_arrow": "DateRangePickerInput_arrow",
                                                          "DateRangePickerInput_arrow_svg": "DateRangePickerInput_arrow_svg",
                                                          "DateRangePickerInput_calendarIcon": "DateRangePickerInput_calendarIcon",
                                                          "DateRangePickerInput_calendarIcon_svg": "DateRangePickerInput_calendarIcon_svg",
                                                          "DateRangePickerInput_clearDates": "DateRangePickerInput_clearDates",
                                                          "DateRangePickerInput_clearDates__hide": "DateRangePickerInput_clearDates__hide",
                                                          "DateRangePickerInput_clearDates__small": "DateRangePickerInput_clearDates__small",
                                                          "DateRangePickerInput_clearDates_default": "DateRangePickerInput_clearDates_default",
                                                          "DateRangePickerInput_clearDates_svg": "DateRangePickerInput_clearDates_svg",
                                                          "DateRangePickerInput_clearDates_svg__small": "DateRangePickerInput_clearDates_svg__small",
                                                        }
                                                      }
                                                      theme={
                                                        Object {
                                                          "reactDates": Object {
                                                            "border": Object {
                                                              "input": Object {
                                                                "border": 0,
                                                                "borderBottom": "2px solid transparent",
                                                                "borderBottomFocused": "2px solid #008489",
                                                                "borderFocused": 0,
                                                                "borderLeft": 0,
                                                                "borderLeftFocused": 0,
                                                                "borderRadius": 0,
                                                                "borderRight": 0,
                                                                "borderRightFocused": 0,
                                                                "borderTop": 0,
                                                                "borderTopFocused": 0,
                                                                "outlineFocused": 0,
                                                              },
                                                              "pickerInput": Object {
                                                                "borderRadius": 2,
                                                                "borderStyle": "solid",
                                                                "borderWidth": 1,
                                                              },
                                                            },
                                                            "color": Object {
                                                              "background": "#fff",
                                                              "backgroundDark": "#f2f2f2",
                                                              "backgroundFocused": "#fff",
                                                              "blocked_calendar": Object {
                                                                "backgroundColor": "#cacccd",
                                                                "backgroundColor_active": "#cacccd",
                                                                "backgroundColor_hover": "#cacccd",
                                                                "borderColor": "#cacccd",
                                                                "borderColor_active": "#cacccd",
                                                                "borderColor_hover": "#cacccd",
                                                                "color": "#82888a",
                                                                "color_active": "#82888a",
                                                                "color_hover": "#82888a",
                                                              },
                                                              "blocked_out_of_range": Object {
                                                                "backgroundColor": "#fff",
                                                                "backgroundColor_active": "#fff",
                                                                "backgroundColor_hover": "#fff",
                                                                "borderColor": "#e4e7e7",
                                                                "borderColor_active": "#e4e7e7",
                                                                "borderColor_hover": "#e4e7e7",
                                                                "color": "#cacccd",
                                                                "color_active": "#cacccd",
                                                                "color_hover": "#cacccd",
                                                              },
                                                              "border": "rgb(219, 219, 219)",
                                                              "core": Object {
                                                                "border": "#dbdbdb",
                                                                "borderBright": "#f4f5f5",
                                                                "borderLight": "#e4e7e7",
                                                                "borderLighter": "#eceeee",
                                                                "borderMedium": "#c4c4c4",
                                                                "gray": "#484848",
                                                                "grayLight": "#82888a",
                                                                "grayLighter": "#cacccd",
                                                                "grayLightest": "#f2f2f2",
                                                                "primary": "#00a699",
                                                                "primaryShade_1": "#33dacd",
                                                                "primaryShade_2": "#66e2da",
                                                                "primaryShade_3": "#80e8e0",
                                                                "primaryShade_4": "#b2f1ec",
                                                                "primary_dark": "#008489",
                                                                "secondary": "#007a87",
                                                                "white": "#fff",
                                                                "yellow": "#ffe8bc",
                                                                "yellow_dark": "#ffce71",
                                                              },
                                                              "disabled": "#f2f2f2",
                                                              "highlighted": Object {
                                                                "backgroundColor": "#ffe8bc",
                                                                "backgroundColor_active": "#ffce71",
                                                                "backgroundColor_hover": "#ffce71",
                                                                "color": "#484848",
                                                                "color_active": "#484848",
                                                                "color_hover": "#484848",
                                                              },
                                                              "hoveredSpan": Object {
                                                                "backgroundColor": "#b2f1ec",
                                                                "backgroundColor_active": "#80e8e0",
                                                                "backgroundColor_hover": "#b2f1ec",
                                                                "borderColor": "#80e8e0",
                                                                "borderColor_active": "#80e8e0",
                                                                "borderColor_hover": "#80e8e0",
                                                                "color": "#007a87",
                                                                "color_active": "#007a87",
                                                                "color_hover": "#007a87",
                                                              },
                                                              "minimumNights": Object {
                                                                "backgroundColor": "#fff",
                                                                "backgroundColor_active": "#fff",
                                                                "backgroundColor_hover": "#fff",
                                                                "borderColor": "#eceeee",
                                                                "color": "#cacccd",
                                                                "color_active": "#cacccd",
                                                                "color_hover": "#cacccd",
                                                              },
                                                              "outside": Object {
                                                                "backgroundColor": "#fff",
                                                                "backgroundColor_active": "#fff",
                                                                "backgroundColor_hover": "#fff",
                                                                "color": "#484848",
                                                                "color_active": "#484848",
                                                                "color_hover": "#484848",
                                                              },
                                                              "placeholderText": "#757575",
                                                              "selected": Object {
                                                                "backgroundColor": "#00a699",
                                                                "backgroundColor_active": "#00a699",
                                                                "backgroundColor_hover": "#00a699",
                                                                "borderColor": "#00a699",
                                                                "borderColor_active": "#00a699",
                                                                "borderColor_hover": "#00a699",
                                                                "color": "#fff",
                                                                "color_active": "#fff",
                                                                "color_hover": "#fff",
                                                              },
                                                              "selectedSpan": Object {
                                                                "backgroundColor": "#66e2da",
                                                                "backgroundColor_active": "#33dacd",
                                                                "backgroundColor_hover": "#33dacd",
                                                                "borderColor": "#33dacd",
                                                                "borderColor_active": "#00a699",
                                                                "borderColor_hover": "#00a699",
                                                                "color": "#fff",
                                                                "color_active": "#fff",
                                                                "color_hover": "#fff",
                                                              },
                                                              "text": "#484848",
                                                              "textDisabled": "#dbdbdb",
                                                              "textFocused": "#007a87",
                                                            },
                                                            "font": Object {
                                                              "captionSize": 18,
                                                              "input": Object {
                                                                "letterSpacing_small": "0.2px",
                                                                "lineHeight": "24px",
                                                                "lineHeight_small": "18px",
                                                                "size": 19,
                                                                "size_small": 15,
                                                                "styleDisabled": "italic",
                                                              },
                                                              "size": 14,
                                                            },
                                                            "noScrollBarOnVerticalScrollable": false,
                                                            "sizing": Object {
                                                              "arrowWidth": 24,
                                                              "inputWidth": 130,
                                                              "inputWidth_small": 97,
                                                            },
                                                            "spacing": Object {
                                                              "captionPaddingBottom": 37,
                                                              "captionPaddingTop": 22,
                                                              "dayPickerHorizontalPadding": 9,
                                                              "displayTextPaddingBottom": 9,
                                                              "displayTextPaddingBottom_small": 5,
                                                              "displayTextPaddingHorizontal": undefined,
                                                              "displayTextPaddingHorizontal_small": undefined,
                                                              "displayTextPaddingLeft": 11,
                                                              "displayTextPaddingLeft_small": 7,
                                                              "displayTextPaddingRight": 11,
                                                              "displayTextPaddingRight_small": 7,
                                                              "displayTextPaddingTop": 11,
                                                              "displayTextPaddingTop_small": 7,
                                                              "displayTextPaddingVertical": undefined,
                                                              "displayTextPaddingVertical_small": undefined,
                                                              "inputPadding": 0,
                                                            },
                                                            "zIndex": 0,
                                                          },
                                                        }
                                                      }
                                                      verticalSpacing={22}
                                                    >
                                                      <div
                                                        className="DateRangePickerInput DateRangePickerInput_1 DateRangePickerInput__withBorder DateRangePickerInput__withBorder_2"
                                                      >
                                                        <withStyles(DateInput)
                                                          block={false}
                                                          disabled={false}
                                                          displayValue="Sep 10, 2021"
                                                          focused={false}
                                                          id="campaign-activities_123_1_Response Count_startDate"
                                                          isFocused={false}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          onKeyDownArrowDown={[Function]}
                                                          onKeyDownQuestionMark={[Function]}
                                                          onKeyDownShiftTab={[Function]}
                                                          onKeyDownTab={[Function]}
                                                          openDirection="down"
                                                          placeholder="Start Date"
                                                          readOnly={false}
                                                          regular={false}
                                                          required={false}
                                                          screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                          showCaret={true}
                                                          small={false}
                                                          verticalSpacing={22}
                                                        >
                                                          <DateInput
                                                            block={false}
                                                            css={[Function]}
                                                            disabled={false}
                                                            displayValue="Sep 10, 2021"
                                                            focused={false}
                                                            id="campaign-activities_123_1_Response Count_startDate"
                                                            isFocused={false}
                                                            onChange={[Function]}
                                                            onFocus={[Function]}
                                                            onKeyDownArrowDown={[Function]}
                                                            onKeyDownQuestionMark={[Function]}
                                                            onKeyDownShiftTab={[Function]}
                                                            onKeyDownTab={[Function]}
                                                            openDirection="down"
                                                            placeholder="Start Date"
                                                            readOnly={false}
                                                            regular={false}
                                                            required={false}
                                                            screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                            showCaret={true}
                                                            small={false}
                                                            styles={
                                                              Object {
                                                                "DateInput": "DateInput",
                                                                "DateInput__block": "DateInput__block",
                                                                "DateInput__disabled": "DateInput__disabled",
                                                                "DateInput__small": "DateInput__small",
                                                                "DateInput_fang": "DateInput_fang",
                                                                "DateInput_fangShape": "DateInput_fangShape",
                                                                "DateInput_fangStroke": "DateInput_fangStroke",
                                                                "DateInput_input": "DateInput_input",
                                                                "DateInput_input__disabled": "DateInput_input__disabled",
                                                                "DateInput_input__focused": "DateInput_input__focused",
                                                                "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                                "DateInput_input__regular": "DateInput_input__regular",
                                                                "DateInput_input__small": "DateInput_input__small",
                                                                "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                              }
                                                            }
                                                            theme={
                                                              Object {
                                                                "reactDates": Object {
                                                                  "border": Object {
                                                                    "input": Object {
                                                                      "border": 0,
                                                                      "borderBottom": "2px solid transparent",
                                                                      "borderBottomFocused": "2px solid #008489",
                                                                      "borderFocused": 0,
                                                                      "borderLeft": 0,
                                                                      "borderLeftFocused": 0,
                                                                      "borderRadius": 0,
                                                                      "borderRight": 0,
                                                                      "borderRightFocused": 0,
                                                                      "borderTop": 0,
                                                                      "borderTopFocused": 0,
                                                                      "outlineFocused": 0,
                                                                    },
                                                                    "pickerInput": Object {
                                                                      "borderRadius": 2,
                                                                      "borderStyle": "solid",
                                                                      "borderWidth": 1,
                                                                    },
                                                                  },
                                                                  "color": Object {
                                                                    "background": "#fff",
                                                                    "backgroundDark": "#f2f2f2",
                                                                    "backgroundFocused": "#fff",
                                                                    "blocked_calendar": Object {
                                                                      "backgroundColor": "#cacccd",
                                                                      "backgroundColor_active": "#cacccd",
                                                                      "backgroundColor_hover": "#cacccd",
                                                                      "borderColor": "#cacccd",
                                                                      "borderColor_active": "#cacccd",
                                                                      "borderColor_hover": "#cacccd",
                                                                      "color": "#82888a",
                                                                      "color_active": "#82888a",
                                                                      "color_hover": "#82888a",
                                                                    },
                                                                    "blocked_out_of_range": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "borderColor": "#e4e7e7",
                                                                      "borderColor_active": "#e4e7e7",
                                                                      "borderColor_hover": "#e4e7e7",
                                                                      "color": "#cacccd",
                                                                      "color_active": "#cacccd",
                                                                      "color_hover": "#cacccd",
                                                                    },
                                                                    "border": "rgb(219, 219, 219)",
                                                                    "core": Object {
                                                                      "border": "#dbdbdb",
                                                                      "borderBright": "#f4f5f5",
                                                                      "borderLight": "#e4e7e7",
                                                                      "borderLighter": "#eceeee",
                                                                      "borderMedium": "#c4c4c4",
                                                                      "gray": "#484848",
                                                                      "grayLight": "#82888a",
                                                                      "grayLighter": "#cacccd",
                                                                      "grayLightest": "#f2f2f2",
                                                                      "primary": "#00a699",
                                                                      "primaryShade_1": "#33dacd",
                                                                      "primaryShade_2": "#66e2da",
                                                                      "primaryShade_3": "#80e8e0",
                                                                      "primaryShade_4": "#b2f1ec",
                                                                      "primary_dark": "#008489",
                                                                      "secondary": "#007a87",
                                                                      "white": "#fff",
                                                                      "yellow": "#ffe8bc",
                                                                      "yellow_dark": "#ffce71",
                                                                    },
                                                                    "disabled": "#f2f2f2",
                                                                    "highlighted": Object {
                                                                      "backgroundColor": "#ffe8bc",
                                                                      "backgroundColor_active": "#ffce71",
                                                                      "backgroundColor_hover": "#ffce71",
                                                                      "color": "#484848",
                                                                      "color_active": "#484848",
                                                                      "color_hover": "#484848",
                                                                    },
                                                                    "hoveredSpan": Object {
                                                                      "backgroundColor": "#b2f1ec",
                                                                      "backgroundColor_active": "#80e8e0",
                                                                      "backgroundColor_hover": "#b2f1ec",
                                                                      "borderColor": "#80e8e0",
                                                                      "borderColor_active": "#80e8e0",
                                                                      "borderColor_hover": "#80e8e0",
                                                                      "color": "#007a87",
                                                                      "color_active": "#007a87",
                                                                      "color_hover": "#007a87",
                                                                    },
                                                                    "minimumNights": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "borderColor": "#eceeee",
                                                                      "color": "#cacccd",
                                                                      "color_active": "#cacccd",
                                                                      "color_hover": "#cacccd",
                                                                    },
                                                                    "outside": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "color": "#484848",
                                                                      "color_active": "#484848",
                                                                      "color_hover": "#484848",
                                                                    },
                                                                    "placeholderText": "#757575",
                                                                    "selected": Object {
                                                                      "backgroundColor": "#00a699",
                                                                      "backgroundColor_active": "#00a699",
                                                                      "backgroundColor_hover": "#00a699",
                                                                      "borderColor": "#00a699",
                                                                      "borderColor_active": "#00a699",
                                                                      "borderColor_hover": "#00a699",
                                                                      "color": "#fff",
                                                                      "color_active": "#fff",
                                                                      "color_hover": "#fff",
                                                                    },
                                                                    "selectedSpan": Object {
                                                                      "backgroundColor": "#66e2da",
                                                                      "backgroundColor_active": "#33dacd",
                                                                      "backgroundColor_hover": "#33dacd",
                                                                      "borderColor": "#33dacd",
                                                                      "borderColor_active": "#00a699",
                                                                      "borderColor_hover": "#00a699",
                                                                      "color": "#fff",
                                                                      "color_active": "#fff",
                                                                      "color_hover": "#fff",
                                                                    },
                                                                    "text": "#484848",
                                                                    "textDisabled": "#dbdbdb",
                                                                    "textFocused": "#007a87",
                                                                  },
                                                                  "font": Object {
                                                                    "captionSize": 18,
                                                                    "input": Object {
                                                                      "letterSpacing_small": "0.2px",
                                                                      "lineHeight": "24px",
                                                                      "lineHeight_small": "18px",
                                                                      "size": 19,
                                                                      "size_small": 15,
                                                                      "styleDisabled": "italic",
                                                                    },
                                                                    "size": 14,
                                                                  },
                                                                  "noScrollBarOnVerticalScrollable": false,
                                                                  "sizing": Object {
                                                                    "arrowWidth": 24,
                                                                    "inputWidth": 130,
                                                                    "inputWidth_small": 97,
                                                                  },
                                                                  "spacing": Object {
                                                                    "captionPaddingBottom": 37,
                                                                    "captionPaddingTop": 22,
                                                                    "dayPickerHorizontalPadding": 9,
                                                                    "displayTextPaddingBottom": 9,
                                                                    "displayTextPaddingBottom_small": 5,
                                                                    "displayTextPaddingHorizontal": undefined,
                                                                    "displayTextPaddingHorizontal_small": undefined,
                                                                    "displayTextPaddingLeft": 11,
                                                                    "displayTextPaddingLeft_small": 7,
                                                                    "displayTextPaddingRight": 11,
                                                                    "displayTextPaddingRight_small": 7,
                                                                    "displayTextPaddingTop": 11,
                                                                    "displayTextPaddingTop_small": 7,
                                                                    "displayTextPaddingVertical": undefined,
                                                                    "displayTextPaddingVertical_small": undefined,
                                                                    "inputPadding": 0,
                                                                  },
                                                                  "zIndex": 0,
                                                                },
                                                              }
                                                            }
                                                            verticalSpacing={22}
                                                          >
                                                            <div
                                                              className="DateInput DateInput_1"
                                                            >
                                                              <input
                                                                aria-describedby="DateInput__screen-reader-message-campaign-activities_123_1_Response Count_startDate"
                                                                aria-label="Start Date"
                                                                autoComplete="off"
                                                                className="DateInput_input DateInput_input_1"
                                                                disabled={false}
                                                                id="campaign-activities_123_1_Response Count_startDate"
                                                                name="campaign-activities_123_1_Response Count_startDate"
                                                                onChange={[Function]}
                                                                onFocus={[Function]}
                                                                onKeyDown={[Function]}
                                                                placeholder="Start Date"
                                                                readOnly={false}
                                                                required={false}
                                                                type="text"
                                                                value="Sep 10, 2021"
                                                              />
                                                              <p
                                                                className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                                id="DateInput__screen-reader-message-campaign-activities_123_1_Response Count_startDate"
                                                              >
                                                                Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                              </p>
                                                            </div>
                                                          </DateInput>
                                                        </withStyles(DateInput)>
                                                        <div
                                                          aria-hidden="true"
                                                          className="DateRangePickerInput_arrow DateRangePickerInput_arrow_1"
                                                          role="presentation"
                                                        >
                                                          <RightArrow
                                                            className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                            focusable="false"
                                                            viewBox="0 0 1000 1000"
                                                          >
                                                            <svg
                                                              className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                              focusable="false"
                                                              viewBox="0 0 1000 1000"
                                                            >
                                                              <path
                                                                d="M694 242l249 250c12 11 12 21 1 32L694 773c-5 5-10 7-16 7s-11-2-16-7c-11-11-11-21 0-32l210-210H68c-13 0-23-10-23-23s10-23 23-23h806L662 275c-21-22 11-54 32-33z"
                                                              />
                                                            </svg>
                                                          </RightArrow>
                                                        </div>
                                                        <withStyles(DateInput)
                                                          block={false}
                                                          disabled={false}
                                                          displayValue="Sep 11, 2021"
                                                          focused={false}
                                                          id="campaign-activities_123_1_Response Count_endDate"
                                                          isFocused={false}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          onKeyDownArrowDown={[Function]}
                                                          onKeyDownQuestionMark={[Function]}
                                                          onKeyDownShiftTab={[Function]}
                                                          onKeyDownTab={[Function]}
                                                          openDirection="down"
                                                          placeholder="End Date"
                                                          readOnly={false}
                                                          regular={false}
                                                          required={false}
                                                          screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                          showCaret={true}
                                                          small={false}
                                                          verticalSpacing={22}
                                                        >
                                                          <DateInput
                                                            block={false}
                                                            css={[Function]}
                                                            disabled={false}
                                                            displayValue="Sep 11, 2021"
                                                            focused={false}
                                                            id="campaign-activities_123_1_Response Count_endDate"
                                                            isFocused={false}
                                                            onChange={[Function]}
                                                            onFocus={[Function]}
                                                            onKeyDownArrowDown={[Function]}
                                                            onKeyDownQuestionMark={[Function]}
                                                            onKeyDownShiftTab={[Function]}
                                                            onKeyDownTab={[Function]}
                                                            openDirection="down"
                                                            placeholder="End Date"
                                                            readOnly={false}
                                                            regular={false}
                                                            required={false}
                                                            screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                            showCaret={true}
                                                            small={false}
                                                            styles={
                                                              Object {
                                                                "DateInput": "DateInput",
                                                                "DateInput__block": "DateInput__block",
                                                                "DateInput__disabled": "DateInput__disabled",
                                                                "DateInput__small": "DateInput__small",
                                                                "DateInput_fang": "DateInput_fang",
                                                                "DateInput_fangShape": "DateInput_fangShape",
                                                                "DateInput_fangStroke": "DateInput_fangStroke",
                                                                "DateInput_input": "DateInput_input",
                                                                "DateInput_input__disabled": "DateInput_input__disabled",
                                                                "DateInput_input__focused": "DateInput_input__focused",
                                                                "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                                "DateInput_input__regular": "DateInput_input__regular",
                                                                "DateInput_input__small": "DateInput_input__small",
                                                                "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                              }
                                                            }
                                                            theme={
                                                              Object {
                                                                "reactDates": Object {
                                                                  "border": Object {
                                                                    "input": Object {
                                                                      "border": 0,
                                                                      "borderBottom": "2px solid transparent",
                                                                      "borderBottomFocused": "2px solid #008489",
                                                                      "borderFocused": 0,
                                                                      "borderLeft": 0,
                                                                      "borderLeftFocused": 0,
                                                                      "borderRadius": 0,
                                                                      "borderRight": 0,
                                                                      "borderRightFocused": 0,
                                                                      "borderTop": 0,
                                                                      "borderTopFocused": 0,
                                                                      "outlineFocused": 0,
                                                                    },
                                                                    "pickerInput": Object {
                                                                      "borderRadius": 2,
                                                                      "borderStyle": "solid",
                                                                      "borderWidth": 1,
                                                                    },
                                                                  },
                                                                  "color": Object {
                                                                    "background": "#fff",
                                                                    "backgroundDark": "#f2f2f2",
                                                                    "backgroundFocused": "#fff",
                                                                    "blocked_calendar": Object {
                                                                      "backgroundColor": "#cacccd",
                                                                      "backgroundColor_active": "#cacccd",
                                                                      "backgroundColor_hover": "#cacccd",
                                                                      "borderColor": "#cacccd",
                                                                      "borderColor_active": "#cacccd",
                                                                      "borderColor_hover": "#cacccd",
                                                                      "color": "#82888a",
                                                                      "color_active": "#82888a",
                                                                      "color_hover": "#82888a",
                                                                    },
                                                                    "blocked_out_of_range": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "borderColor": "#e4e7e7",
                                                                      "borderColor_active": "#e4e7e7",
                                                                      "borderColor_hover": "#e4e7e7",
                                                                      "color": "#cacccd",
                                                                      "color_active": "#cacccd",
                                                                      "color_hover": "#cacccd",
                                                                    },
                                                                    "border": "rgb(219, 219, 219)",
                                                                    "core": Object {
                                                                      "border": "#dbdbdb",
                                                                      "borderBright": "#f4f5f5",
                                                                      "borderLight": "#e4e7e7",
                                                                      "borderLighter": "#eceeee",
                                                                      "borderMedium": "#c4c4c4",
                                                                      "gray": "#484848",
                                                                      "grayLight": "#82888a",
                                                                      "grayLighter": "#cacccd",
                                                                      "grayLightest": "#f2f2f2",
                                                                      "primary": "#00a699",
                                                                      "primaryShade_1": "#33dacd",
                                                                      "primaryShade_2": "#66e2da",
                                                                      "primaryShade_3": "#80e8e0",
                                                                      "primaryShade_4": "#b2f1ec",
                                                                      "primary_dark": "#008489",
                                                                      "secondary": "#007a87",
                                                                      "white": "#fff",
                                                                      "yellow": "#ffe8bc",
                                                                      "yellow_dark": "#ffce71",
                                                                    },
                                                                    "disabled": "#f2f2f2",
                                                                    "highlighted": Object {
                                                                      "backgroundColor": "#ffe8bc",
                                                                      "backgroundColor_active": "#ffce71",
                                                                      "backgroundColor_hover": "#ffce71",
                                                                      "color": "#484848",
                                                                      "color_active": "#484848",
                                                                      "color_hover": "#484848",
                                                                    },
                                                                    "hoveredSpan": Object {
                                                                      "backgroundColor": "#b2f1ec",
                                                                      "backgroundColor_active": "#80e8e0",
                                                                      "backgroundColor_hover": "#b2f1ec",
                                                                      "borderColor": "#80e8e0",
                                                                      "borderColor_active": "#80e8e0",
                                                                      "borderColor_hover": "#80e8e0",
                                                                      "color": "#007a87",
                                                                      "color_active": "#007a87",
                                                                      "color_hover": "#007a87",
                                                                    },
                                                                    "minimumNights": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "borderColor": "#eceeee",
                                                                      "color": "#cacccd",
                                                                      "color_active": "#cacccd",
                                                                      "color_hover": "#cacccd",
                                                                    },
                                                                    "outside": Object {
                                                                      "backgroundColor": "#fff",
                                                                      "backgroundColor_active": "#fff",
                                                                      "backgroundColor_hover": "#fff",
                                                                      "color": "#484848",
                                                                      "color_active": "#484848",
                                                                      "color_hover": "#484848",
                                                                    },
                                                                    "placeholderText": "#757575",
                                                                    "selected": Object {
                                                                      "backgroundColor": "#00a699",
                                                                      "backgroundColor_active": "#00a699",
                                                                      "backgroundColor_hover": "#00a699",
                                                                      "borderColor": "#00a699",
                                                                      "borderColor_active": "#00a699",
                                                                      "borderColor_hover": "#00a699",
                                                                      "color": "#fff",
                                                                      "color_active": "#fff",
                                                                      "color_hover": "#fff",
                                                                    },
                                                                    "selectedSpan": Object {
                                                                      "backgroundColor": "#66e2da",
                                                                      "backgroundColor_active": "#33dacd",
                                                                      "backgroundColor_hover": "#33dacd",
                                                                      "borderColor": "#33dacd",
                                                                      "borderColor_active": "#00a699",
                                                                      "borderColor_hover": "#00a699",
                                                                      "color": "#fff",
                                                                      "color_active": "#fff",
                                                                      "color_hover": "#fff",
                                                                    },
                                                                    "text": "#484848",
                                                                    "textDisabled": "#dbdbdb",
                                                                    "textFocused": "#007a87",
                                                                  },
                                                                  "font": Object {
                                                                    "captionSize": 18,
                                                                    "input": Object {
                                                                      "letterSpacing_small": "0.2px",
                                                                      "lineHeight": "24px",
                                                                      "lineHeight_small": "18px",
                                                                      "size": 19,
                                                                      "size_small": 15,
                                                                      "styleDisabled": "italic",
                                                                    },
                                                                    "size": 14,
                                                                  },
                                                                  "noScrollBarOnVerticalScrollable": false,
                                                                  "sizing": Object {
                                                                    "arrowWidth": 24,
                                                                    "inputWidth": 130,
                                                                    "inputWidth_small": 97,
                                                                  },
                                                                  "spacing": Object {
                                                                    "captionPaddingBottom": 37,
                                                                    "captionPaddingTop": 22,
                                                                    "dayPickerHorizontalPadding": 9,
                                                                    "displayTextPaddingBottom": 9,
                                                                    "displayTextPaddingBottom_small": 5,
                                                                    "displayTextPaddingHorizontal": undefined,
                                                                    "displayTextPaddingHorizontal_small": undefined,
                                                                    "displayTextPaddingLeft": 11,
                                                                    "displayTextPaddingLeft_small": 7,
                                                                    "displayTextPaddingRight": 11,
                                                                    "displayTextPaddingRight_small": 7,
                                                                    "displayTextPaddingTop": 11,
                                                                    "displayTextPaddingTop_small": 7,
                                                                    "displayTextPaddingVertical": undefined,
                                                                    "displayTextPaddingVertical_small": undefined,
                                                                    "inputPadding": 0,
                                                                  },
                                                                  "zIndex": 0,
                                                                },
                                                              }
                                                            }
                                                            verticalSpacing={22}
                                                          >
                                                            <div
                                                              className="DateInput DateInput_1"
                                                            >
                                                              <input
                                                                aria-describedby="DateInput__screen-reader-message-campaign-activities_123_1_Response Count_endDate"
                                                                aria-label="End Date"
                                                                autoComplete="off"
                                                                className="DateInput_input DateInput_input_1"
                                                                disabled={false}
                                                                id="campaign-activities_123_1_Response Count_endDate"
                                                                name="campaign-activities_123_1_Response Count_endDate"
                                                                onChange={[Function]}
                                                                onFocus={[Function]}
                                                                onKeyDown={[Function]}
                                                                placeholder="End Date"
                                                                readOnly={false}
                                                                required={false}
                                                                type="text"
                                                                value="Sep 11, 2021"
                                                              />
                                                              <p
                                                                className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                                id="DateInput__screen-reader-message-campaign-activities_123_1_Response Count_endDate"
                                                              >
                                                                Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                              </p>
                                                            </div>
                                                          </DateInput>
                                                        </withStyles(DateInput)>
                                                      </div>
                                                    </DateRangePickerInput>
                                                  </withStyles(DateRangePickerInput)>
                                                </DateRangePickerInputController>
                                              </div>
                                            </OutsideClickHandler>
                                          </div>
                                        </DateRangePicker>
                                      </withStyles(DateRangePicker)>
                                      <div
                                        className="date-range__value"
                                      >
                                        <span
                                          className="svg-inline--fa fa fa-calendar-alt cursor-pointer f-14"
                                          onClick={[Function]}
                                          style={
                                            Object {
                                              "color": "#006DEE",
                                            }
                                          }
                                        />
                                        <div
                                          className="date-value"
                                        >
                                          <span
                                            className="f-12 align-self-center"
                                          >
                                            Sep 10, 2021
                                          </span>
                                          <span
                                            className="f-12"
                                          >
                                             - 
                                          </span>
                                          <span
                                            className="f-12 align-self-center"
                                          >
                                            Sep 11, 2021
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </DateRangePickerComponent>
                                </Connect(DateRangePickerComponent)>
                              </div>
                              <FunnelReport
                                data={
                                  Array [
                                    Object {
                                      "clipPathValue": 100,
                                      "color": "rgb(40, 126, 254)",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Sent",
                                      "value": 1000,
                                    },
                                    Object {
                                      "clipPathValue": 75,
                                      "color": "rgb(113, 172, 254)",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Delivered",
                                      "value": 800,
                                    },
                                    Object {
                                      "clipPathValue": 50,
                                      "color": "rgb(185, 217, 254)",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Read",
                                      "value": 500,
                                    },
                                  ]
                                }
                                maxValue={100}
                                numberFormat="INDIAN_NUMBER_FORMAT"
                              >
                                <div
                                  className="funnel-report"
                                >
                                  <div
                                    className="funnel-container"
                                  >
                                    <div
                                      className="funnel-section"
                                      key="Sent__0"
                                      style={
                                        Object {
                                          "height": "33.333333333333336%",
                                        }
                                      }
                                    >
                                      <div
                                        className="metric-info"
                                      >
                                        <span
                                          className="metric-label"
                                        >
                                          Sent
                                        </span>
                                        <TooltipInitialiseContainer>
                                          <div
                                            className=""
                                            onClick={[Function]}
                                          >
                                            <span
                                              className="metric-value"
                                              data-toggle="tooltip"
                                              title="1000"
                                            >
                                              1,000
                                            </span>
                                          </div>
                                        </TooltipInitialiseContainer>
                                      </div>
                                      <div
                                        className="clip-path"
                                        style={
                                          Object {
                                            "backgroundColor": "rgb(40, 126, 254)",
                                            "clipPath": "polygon(0% 0, 12.5% 100%, 87.5% 100%, 100% 0)",
                                          }
                                        }
                                      />
                                    </div>
                                    <div
                                      className="funnel-section"
                                      key="Delivered__1"
                                      style={
                                        Object {
                                          "height": "33.333333333333336%",
                                        }
                                      }
                                    >
                                      <div
                                        className="metric-info"
                                      >
                                        <span
                                          className="metric-label"
                                        >
                                          Delivered
                                        </span>
                                        <TooltipInitialiseContainer>
                                          <div
                                            className=""
                                            onClick={[Function]}
                                          >
                                            <span
                                              className="metric-value"
                                              data-toggle="tooltip"
                                              title="800"
                                            >
                                              800
                                            </span>
                                          </div>
                                        </TooltipInitialiseContainer>
                                      </div>
                                      <div
                                        className="clip-path"
                                        style={
                                          Object {
                                            "backgroundColor": "rgb(113, 172, 254)",
                                            "clipPath": "polygon(12.5% 0, 25% 100%, 75% 100%, 87.5% 0)",
                                          }
                                        }
                                      />
                                    </div>
                                    <div
                                      className="funnel-section"
                                      key="Read__2"
                                      style={
                                        Object {
                                          "height": "33.333333333333336%",
                                        }
                                      }
                                    >
                                      <div
                                        className="metric-info"
                                      >
                                        <span
                                          className="metric-label"
                                        >
                                          Read
                                        </span>
                                        <TooltipInitialiseContainer>
                                          <div
                                            className=""
                                            onClick={[Function]}
                                          >
                                            <span
                                              className="metric-value"
                                              data-toggle="tooltip"
                                              title="500"
                                            >
                                              500
                                            </span>
                                          </div>
                                        </TooltipInitialiseContainer>
                                      </div>
                                      <div
                                        className="clip-path"
                                        style={
                                          Object {
                                            "backgroundColor": "rgb(185, 217, 254)",
                                            "clipPath": "polygon(25% 0, 37.5% 100%, 62.5% 100%, 75% 0)",
                                          }
                                        }
                                      />
                                    </div>
                                  </div>
                                </div>
                              </FunnelReport>
                              <div
                                className="failed-count"
                              >
                                <span>
                                  Failed: 
                                </span>
                                <TooltipInitialiseContainer>
                                  <div
                                    className=""
                                    onClick={[Function]}
                                  >
                                    <strong
                                      data-toggle="tooltip"
                                      title="200"
                                    >
                                      200
                                    </strong>
                                  </div>
                                </TooltipInitialiseContainer>
                              </div>
                            </div>
                          </CampaignActivityFunnelChart>
                        </Component>
                      </WithAbortController>
                    </div>
                    <div
                      className="col-6"
                    >
                      <Connect(BarChartReport)
                        currencyId={400}
                        dateRange={
                          Object {
                            "endDate": "2021-09-11T04:04:23.835Z",
                            "startDate": "2021-09-10T04:04:23.835Z",
                          }
                        }
                        entity="campaign-activities"
                        reportData={
                          Array [
                            Object {
                              "color": "#006DEE",
                              "dimension": Array [],
                              "id": null,
                              "name": "Budget",
                              "value": 100000,
                            },
                            Object {
                              "color": "#00780E",
                              "dimension": Array [],
                              "id": null,
                              "name": "Actual",
                              "value": 110000,
                            },
                          ]
                        }
                        type="Budget vs Actual Expense"
                      >
                        <BarChartReport
                          currencies={
                            Array [
                              Object {
                                "displayName": "Indian Rupee",
                                "id": 13,
                                "name": "INR",
                              },
                            ]
                          }
                          currencyId={400}
                          dateRange={
                            Object {
                              "endDate": "2021-09-11T04:04:23.835Z",
                              "startDate": "2021-09-10T04:04:23.835Z",
                            }
                          }
                          dispatch={[Function]}
                          entity="campaign-activities"
                          numberFormat="INDIAN_NUMBER_FORMAT"
                          reportData={
                            Array [
                              Object {
                                "color": "#006DEE",
                                "dimension": Array [],
                                "id": null,
                                "name": "Budget",
                                "value": 100000,
                              },
                              Object {
                                "color": "#00780E",
                                "dimension": Array [],
                                "id": null,
                                "name": "Actual",
                                "value": 110000,
                              },
                            ]
                          }
                          type="Budget vs Actual Expense"
                        >
                          <div
                            className="bar-chart__report"
                          >
                            <div
                              className="d-flex justify-content-between"
                            >
                              <div
                                className="f-14"
                              >
                                Budget vs Actual Expense
                              </div>
                              <Connect(DateRangePickerComponent)
                                defaultDateRange={
                                  Object {
                                    "endDate": "2021-09-11T04:04:23.835Z",
                                    "startDate": "2021-09-10T04:04:23.835Z",
                                  }
                                }
                                isDateRangeEditable={false}
                                uniqueKey="campaign-activities_Budget vs Actual Expense"
                                updateDateRange={[Function]}
                              >
                                <DateRangePickerComponent
                                  dateFormat="MMM D, YYYY [at] h:mm a"
                                  defaultDateRange={
                                    Object {
                                      "endDate": "2021-09-11T04:04:23.835Z",
                                      "startDate": "2021-09-10T04:04:23.835Z",
                                    }
                                  }
                                  dispatch={[Function]}
                                  isDateRangeEditable={false}
                                  timezone="Asia/Calcutta"
                                  uniqueKey="campaign-activities_Budget vs Actual Expense"
                                  updateDateRange={[Function]}
                                >
                                  <div
                                    className="date-range__picker"
                                  >
                                    <withStyles(DateRangePicker)
                                      anchorDirection="left"
                                      appendToBody={false}
                                      block={false}
                                      calendarInfoPosition="bottom"
                                      customArrowIcon={null}
                                      customCloseIcon={null}
                                      customInputIcon={null}
                                      daySize={24}
                                      disableScroll={false}
                                      disabled={false}
                                      displayFormat="MMM D, YYYY"
                                      enableOutsideDays={false}
                                      endDate={"2021-09-11T04:04:23.835Z"}
                                      endDateId="campaign-activities_Budget vs Actual Expense_endDate"
                                      endDatePlaceholderText="End Date"
                                      firstDayOfWeek={null}
                                      focusedInput={null}
                                      hideKeyboardShortcutsPanel={false}
                                      horizontalMargin={0}
                                      initialVisibleMonth={null}
                                      inputIconPosition="before"
                                      isDayBlocked={[Function]}
                                      isDayHighlighted={[Function]}
                                      isOutsideRange={[Function]}
                                      isRTL={false}
                                      keepFocusOnInput={false}
                                      keepOpenOnDateSelect={true}
                                      minimumNights={0}
                                      monthFormat="MMMM YYYY"
                                      navNext={
                                        <i
                                          className="svg-inline--fa fa fa-angle-right"
                                        />
                                      }
                                      navPrev={
                                        <i
                                          className="svg-inline--fa fa fa-angle-left left-button"
                                        />
                                      }
                                      noBorder={false}
                                      numberOfMonths={2}
                                      onClose={[Function]}
                                      onDatesChange={[Function]}
                                      onFocusChange={[Function]}
                                      onNextMonthClick={[Function]}
                                      onPrevMonthClick={[Function]}
                                      openDirection="down"
                                      orientation="horizontal"
                                      phrases={
                                        Object {
                                          "calendarLabel": "Calendar",
                                          "chooseAvailableEndDate": [Function],
                                          "chooseAvailableStartDate": [Function],
                                          "clearDates": "Clear Dates",
                                          "closeDatePicker": "Close",
                                          "dateIsSelected": [Function],
                                          "dateIsSelectedAsEndDate": [Function],
                                          "dateIsSelectedAsStartDate": [Function],
                                          "dateIsUnavailable": [Function],
                                          "enterKey": "Enter key",
                                          "escape": "Escape key",
                                          "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                          "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                          "homeEnd": "Home and end keys",
                                          "jumpToNextMonth": "Move forward to switch to the next month.",
                                          "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                          "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                          "keyboardShortcuts": "Keyboard Shortcuts",
                                          "leftArrowRightArrow": "Right and left arrow keys",
                                          "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                          "moveFocusByOneMonth": "Switch months.",
                                          "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                          "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                          "openThisPanel": "Open this panel.",
                                          "pageUpPageDown": "page up and page down keys",
                                          "questionMark": "Question mark",
                                          "returnFocusToInput": "Return to the date input field.",
                                          "selectFocusedDate": "Select the date in focus.",
                                          "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                          "upArrowDownArrow": "up and down arrow keys",
                                        }
                                      }
                                      readOnly={false}
                                      regular={false}
                                      renderCalendarInfo={[Function]}
                                      renderDayContents={null}
                                      renderMonthElement={[Function]}
                                      renderMonthText={null}
                                      reopenPickerOnClearDates={false}
                                      required={false}
                                      screenReaderInputMessage=""
                                      showClearDates={false}
                                      showDefaultInputIcon={false}
                                      small={false}
                                      startDate={"2021-09-10T04:04:23.835Z"}
                                      startDateId="campaign-activities_Budget vs Actual Expense_startDate"
                                      startDatePlaceholderText="Start Date"
                                      verticalHeight={null}
                                      verticalSpacing={22}
                                      weekDayFormat="dd"
                                      withFullScreenPortal={false}
                                      withPortal={false}
                                    >
                                      <DateRangePicker
                                        anchorDirection="left"
                                        appendToBody={false}
                                        block={false}
                                        calendarInfoPosition="bottom"
                                        css={[Function]}
                                        customArrowIcon={null}
                                        customCloseIcon={null}
                                        customInputIcon={null}
                                        daySize={24}
                                        disableScroll={false}
                                        disabled={false}
                                        displayFormat="MMM D, YYYY"
                                        enableOutsideDays={false}
                                        endDate={"2021-09-11T04:04:23.835Z"}
                                        endDateId="campaign-activities_Budget vs Actual Expense_endDate"
                                        endDatePlaceholderText="End Date"
                                        firstDayOfWeek={null}
                                        focusedInput={null}
                                        hideKeyboardShortcutsPanel={false}
                                        horizontalMargin={0}
                                        initialVisibleMonth={null}
                                        inputIconPosition="before"
                                        isDayBlocked={[Function]}
                                        isDayHighlighted={[Function]}
                                        isOutsideRange={[Function]}
                                        isRTL={false}
                                        keepFocusOnInput={false}
                                        keepOpenOnDateSelect={true}
                                        minimumNights={0}
                                        monthFormat="MMMM YYYY"
                                        navNext={
                                          <i
                                            className="svg-inline--fa fa fa-angle-right"
                                          />
                                        }
                                        navPrev={
                                          <i
                                            className="svg-inline--fa fa fa-angle-left left-button"
                                          />
                                        }
                                        noBorder={false}
                                        numberOfMonths={2}
                                        onClose={[Function]}
                                        onDatesChange={[Function]}
                                        onFocusChange={[Function]}
                                        onNextMonthClick={[Function]}
                                        onPrevMonthClick={[Function]}
                                        openDirection="down"
                                        orientation="horizontal"
                                        phrases={
                                          Object {
                                            "calendarLabel": "Calendar",
                                            "chooseAvailableEndDate": [Function],
                                            "chooseAvailableStartDate": [Function],
                                            "clearDates": "Clear Dates",
                                            "closeDatePicker": "Close",
                                            "dateIsSelected": [Function],
                                            "dateIsSelectedAsEndDate": [Function],
                                            "dateIsSelectedAsStartDate": [Function],
                                            "dateIsUnavailable": [Function],
                                            "enterKey": "Enter key",
                                            "escape": "Escape key",
                                            "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                            "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                            "homeEnd": "Home and end keys",
                                            "jumpToNextMonth": "Move forward to switch to the next month.",
                                            "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                            "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                            "keyboardShortcuts": "Keyboard Shortcuts",
                                            "leftArrowRightArrow": "Right and left arrow keys",
                                            "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                            "moveFocusByOneMonth": "Switch months.",
                                            "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                            "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                            "openThisPanel": "Open this panel.",
                                            "pageUpPageDown": "page up and page down keys",
                                            "questionMark": "Question mark",
                                            "returnFocusToInput": "Return to the date input field.",
                                            "selectFocusedDate": "Select the date in focus.",
                                            "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                            "upArrowDownArrow": "up and down arrow keys",
                                          }
                                        }
                                        readOnly={false}
                                        regular={false}
                                        renderCalendarInfo={[Function]}
                                        renderDayContents={null}
                                        renderMonthElement={[Function]}
                                        renderMonthText={null}
                                        reopenPickerOnClearDates={false}
                                        required={false}
                                        screenReaderInputMessage=""
                                        showClearDates={false}
                                        showDefaultInputIcon={false}
                                        small={false}
                                        startDate={"2021-09-10T04:04:23.835Z"}
                                        startDateId="campaign-activities_Budget vs Actual Expense_startDate"
                                        startDatePlaceholderText="Start Date"
                                        styles={
                                          Object {
                                            "DateRangePicker": "DateRangePicker",
                                            "DateRangePicker__block": "DateRangePicker__block",
                                            "DateRangePicker_closeButton": "DateRangePicker_closeButton",
                                            "DateRangePicker_closeButton_svg": "DateRangePicker_closeButton_svg",
                                            "DateRangePicker_picker": "DateRangePicker_picker",
                                            "DateRangePicker_picker__directionLeft": "DateRangePicker_picker__directionLeft",
                                            "DateRangePicker_picker__directionRight": "DateRangePicker_picker__directionRight",
                                            "DateRangePicker_picker__fullScreenPortal": "DateRangePicker_picker__fullScreenPortal",
                                            "DateRangePicker_picker__portal": "DateRangePicker_picker__portal",
                                            "DateRangePicker_picker__rtl": "DateRangePicker_picker__rtl",
                                          }
                                        }
                                        theme={
                                          Object {
                                            "reactDates": Object {
                                              "border": Object {
                                                "input": Object {
                                                  "border": 0,
                                                  "borderBottom": "2px solid transparent",
                                                  "borderBottomFocused": "2px solid #008489",
                                                  "borderFocused": 0,
                                                  "borderLeft": 0,
                                                  "borderLeftFocused": 0,
                                                  "borderRadius": 0,
                                                  "borderRight": 0,
                                                  "borderRightFocused": 0,
                                                  "borderTop": 0,
                                                  "borderTopFocused": 0,
                                                  "outlineFocused": 0,
                                                },
                                                "pickerInput": Object {
                                                  "borderRadius": 2,
                                                  "borderStyle": "solid",
                                                  "borderWidth": 1,
                                                },
                                              },
                                              "color": Object {
                                                "background": "#fff",
                                                "backgroundDark": "#f2f2f2",
                                                "backgroundFocused": "#fff",
                                                "blocked_calendar": Object {
                                                  "backgroundColor": "#cacccd",
                                                  "backgroundColor_active": "#cacccd",
                                                  "backgroundColor_hover": "#cacccd",
                                                  "borderColor": "#cacccd",
                                                  "borderColor_active": "#cacccd",
                                                  "borderColor_hover": "#cacccd",
                                                  "color": "#82888a",
                                                  "color_active": "#82888a",
                                                  "color_hover": "#82888a",
                                                },
                                                "blocked_out_of_range": Object {
                                                  "backgroundColor": "#fff",
                                                  "backgroundColor_active": "#fff",
                                                  "backgroundColor_hover": "#fff",
                                                  "borderColor": "#e4e7e7",
                                                  "borderColor_active": "#e4e7e7",
                                                  "borderColor_hover": "#e4e7e7",
                                                  "color": "#cacccd",
                                                  "color_active": "#cacccd",
                                                  "color_hover": "#cacccd",
                                                },
                                                "border": "rgb(219, 219, 219)",
                                                "core": Object {
                                                  "border": "#dbdbdb",
                                                  "borderBright": "#f4f5f5",
                                                  "borderLight": "#e4e7e7",
                                                  "borderLighter": "#eceeee",
                                                  "borderMedium": "#c4c4c4",
                                                  "gray": "#484848",
                                                  "grayLight": "#82888a",
                                                  "grayLighter": "#cacccd",
                                                  "grayLightest": "#f2f2f2",
                                                  "primary": "#00a699",
                                                  "primaryShade_1": "#33dacd",
                                                  "primaryShade_2": "#66e2da",
                                                  "primaryShade_3": "#80e8e0",
                                                  "primaryShade_4": "#b2f1ec",
                                                  "primary_dark": "#008489",
                                                  "secondary": "#007a87",
                                                  "white": "#fff",
                                                  "yellow": "#ffe8bc",
                                                  "yellow_dark": "#ffce71",
                                                },
                                                "disabled": "#f2f2f2",
                                                "highlighted": Object {
                                                  "backgroundColor": "#ffe8bc",
                                                  "backgroundColor_active": "#ffce71",
                                                  "backgroundColor_hover": "#ffce71",
                                                  "color": "#484848",
                                                  "color_active": "#484848",
                                                  "color_hover": "#484848",
                                                },
                                                "hoveredSpan": Object {
                                                  "backgroundColor": "#b2f1ec",
                                                  "backgroundColor_active": "#80e8e0",
                                                  "backgroundColor_hover": "#b2f1ec",
                                                  "borderColor": "#80e8e0",
                                                  "borderColor_active": "#80e8e0",
                                                  "borderColor_hover": "#80e8e0",
                                                  "color": "#007a87",
                                                  "color_active": "#007a87",
                                                  "color_hover": "#007a87",
                                                },
                                                "minimumNights": Object {
                                                  "backgroundColor": "#fff",
                                                  "backgroundColor_active": "#fff",
                                                  "backgroundColor_hover": "#fff",
                                                  "borderColor": "#eceeee",
                                                  "color": "#cacccd",
                                                  "color_active": "#cacccd",
                                                  "color_hover": "#cacccd",
                                                },
                                                "outside": Object {
                                                  "backgroundColor": "#fff",
                                                  "backgroundColor_active": "#fff",
                                                  "backgroundColor_hover": "#fff",
                                                  "color": "#484848",
                                                  "color_active": "#484848",
                                                  "color_hover": "#484848",
                                                },
                                                "placeholderText": "#757575",
                                                "selected": Object {
                                                  "backgroundColor": "#00a699",
                                                  "backgroundColor_active": "#00a699",
                                                  "backgroundColor_hover": "#00a699",
                                                  "borderColor": "#00a699",
                                                  "borderColor_active": "#00a699",
                                                  "borderColor_hover": "#00a699",
                                                  "color": "#fff",
                                                  "color_active": "#fff",
                                                  "color_hover": "#fff",
                                                },
                                                "selectedSpan": Object {
                                                  "backgroundColor": "#66e2da",
                                                  "backgroundColor_active": "#33dacd",
                                                  "backgroundColor_hover": "#33dacd",
                                                  "borderColor": "#33dacd",
                                                  "borderColor_active": "#00a699",
                                                  "borderColor_hover": "#00a699",
                                                  "color": "#fff",
                                                  "color_active": "#fff",
                                                  "color_hover": "#fff",
                                                },
                                                "text": "#484848",
                                                "textDisabled": "#dbdbdb",
                                                "textFocused": "#007a87",
                                              },
                                              "font": Object {
                                                "captionSize": 18,
                                                "input": Object {
                                                  "letterSpacing_small": "0.2px",
                                                  "lineHeight": "24px",
                                                  "lineHeight_small": "18px",
                                                  "size": 19,
                                                  "size_small": 15,
                                                  "styleDisabled": "italic",
                                                },
                                                "size": 14,
                                              },
                                              "noScrollBarOnVerticalScrollable": false,
                                              "sizing": Object {
                                                "arrowWidth": 24,
                                                "inputWidth": 130,
                                                "inputWidth_small": 97,
                                              },
                                              "spacing": Object {
                                                "captionPaddingBottom": 37,
                                                "captionPaddingTop": 22,
                                                "dayPickerHorizontalPadding": 9,
                                                "displayTextPaddingBottom": 9,
                                                "displayTextPaddingBottom_small": 5,
                                                "displayTextPaddingHorizontal": undefined,
                                                "displayTextPaddingHorizontal_small": undefined,
                                                "displayTextPaddingLeft": 11,
                                                "displayTextPaddingLeft_small": 7,
                                                "displayTextPaddingRight": 11,
                                                "displayTextPaddingRight_small": 7,
                                                "displayTextPaddingTop": 11,
                                                "displayTextPaddingTop_small": 7,
                                                "displayTextPaddingVertical": undefined,
                                                "displayTextPaddingVertical_small": undefined,
                                                "inputPadding": 0,
                                              },
                                              "zIndex": 0,
                                            },
                                          }
                                        }
                                        verticalHeight={null}
                                        verticalSpacing={22}
                                        weekDayFormat="dd"
                                        withFullScreenPortal={false}
                                        withPortal={false}
                                      >
                                        <div
                                          className="DateRangePicker DateRangePicker_1"
                                        >
                                          <OutsideClickHandler
                                            disabled={false}
                                            display="block"
                                            onOutsideClick={[Function]}
                                            useCapture={true}
                                          >
                                            <div>
                                              <DateRangePickerInputController
                                                block={false}
                                                customArrowIcon={null}
                                                customCloseIcon={null}
                                                customInputIcon={null}
                                                disabled={false}
                                                displayFormat="MMM D, YYYY"
                                                endDate={"2021-09-11T04:04:23.835Z"}
                                                endDateId="campaign-activities_Budget vs Actual Expense_endDate"
                                                endDatePlaceholderText="End Date"
                                                inputIconPosition="before"
                                                isEndDateFocused={false}
                                                isFocused={false}
                                                isOutsideRange={[Function]}
                                                isRTL={false}
                                                isStartDateFocused={false}
                                                keepOpenOnDateSelect={true}
                                                minimumNights={0}
                                                noBorder={false}
                                                onClose={[Function]}
                                                onDatesChange={[Function]}
                                                onFocusChange={[Function]}
                                                onKeyDownArrowDown={[Function]}
                                                onKeyDownQuestionMark={[Function]}
                                                openDirection="down"
                                                phrases={
                                                  Object {
                                                    "calendarLabel": "Calendar",
                                                    "chooseAvailableEndDate": [Function],
                                                    "chooseAvailableStartDate": [Function],
                                                    "clearDates": "Clear Dates",
                                                    "closeDatePicker": "Close",
                                                    "dateIsSelected": [Function],
                                                    "dateIsSelectedAsEndDate": [Function],
                                                    "dateIsSelectedAsStartDate": [Function],
                                                    "dateIsUnavailable": [Function],
                                                    "enterKey": "Enter key",
                                                    "escape": "Escape key",
                                                    "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                    "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                    "homeEnd": "Home and end keys",
                                                    "jumpToNextMonth": "Move forward to switch to the next month.",
                                                    "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                    "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                    "keyboardShortcuts": "Keyboard Shortcuts",
                                                    "leftArrowRightArrow": "Right and left arrow keys",
                                                    "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                    "moveFocusByOneMonth": "Switch months.",
                                                    "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                    "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                    "openThisPanel": "Open this panel.",
                                                    "pageUpPageDown": "page up and page down keys",
                                                    "questionMark": "Question mark",
                                                    "returnFocusToInput": "Return to the date input field.",
                                                    "selectFocusedDate": "Select the date in focus.",
                                                    "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                    "upArrowDownArrow": "up and down arrow keys",
                                                  }
                                                }
                                                readOnly={false}
                                                regular={false}
                                                reopenPickerOnClearDates={false}
                                                required={false}
                                                screenReaderMessage=""
                                                showCaret={true}
                                                showClearDates={false}
                                                showDefaultInputIcon={false}
                                                small={false}
                                                startDate={"2021-09-10T04:04:23.835Z"}
                                                startDateId="campaign-activities_Budget vs Actual Expense_startDate"
                                                startDatePlaceholderText="Start Date"
                                                verticalSpacing={22}
                                                withFullScreenPortal={false}
                                              >
                                                <withStyles(DateRangePickerInput)
                                                  block={false}
                                                  customArrowIcon={null}
                                                  customCloseIcon={null}
                                                  customInputIcon={null}
                                                  disabled={false}
                                                  endDate="Sep 11, 2021"
                                                  endDateId="campaign-activities_Budget vs Actual Expense_endDate"
                                                  endDatePlaceholderText="End Date"
                                                  inputIconPosition="before"
                                                  isEndDateFocused={false}
                                                  isFocused={false}
                                                  isRTL={false}
                                                  isStartDateFocused={false}
                                                  noBorder={false}
                                                  onClearDates={[Function]}
                                                  onEndDateChange={[Function]}
                                                  onEndDateFocus={[Function]}
                                                  onEndDateTab={[Function]}
                                                  onKeyDownArrowDown={[Function]}
                                                  onKeyDownQuestionMark={[Function]}
                                                  onStartDateChange={[Function]}
                                                  onStartDateFocus={[Function]}
                                                  onStartDateShiftTab={[Function]}
                                                  openDirection="down"
                                                  phrases={
                                                    Object {
                                                      "calendarLabel": "Calendar",
                                                      "chooseAvailableEndDate": [Function],
                                                      "chooseAvailableStartDate": [Function],
                                                      "clearDates": "Clear Dates",
                                                      "closeDatePicker": "Close",
                                                      "dateIsSelected": [Function],
                                                      "dateIsSelectedAsEndDate": [Function],
                                                      "dateIsSelectedAsStartDate": [Function],
                                                      "dateIsUnavailable": [Function],
                                                      "enterKey": "Enter key",
                                                      "escape": "Escape key",
                                                      "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                      "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                      "homeEnd": "Home and end keys",
                                                      "jumpToNextMonth": "Move forward to switch to the next month.",
                                                      "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                      "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                      "keyboardShortcuts": "Keyboard Shortcuts",
                                                      "leftArrowRightArrow": "Right and left arrow keys",
                                                      "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                      "moveFocusByOneMonth": "Switch months.",
                                                      "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                      "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                      "openThisPanel": "Open this panel.",
                                                      "pageUpPageDown": "page up and page down keys",
                                                      "questionMark": "Question mark",
                                                      "returnFocusToInput": "Return to the date input field.",
                                                      "selectFocusedDate": "Select the date in focus.",
                                                      "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                      "upArrowDownArrow": "up and down arrow keys",
                                                    }
                                                  }
                                                  readOnly={false}
                                                  regular={false}
                                                  required={false}
                                                  screenReaderMessage=""
                                                  showCaret={true}
                                                  showClearDates={false}
                                                  showDefaultInputIcon={false}
                                                  small={false}
                                                  startDate="Sep 10, 2021"
                                                  startDateId="campaign-activities_Budget vs Actual Expense_startDate"
                                                  startDatePlaceholderText="Start Date"
                                                  verticalSpacing={22}
                                                >
                                                  <DateRangePickerInput
                                                    block={false}
                                                    css={[Function]}
                                                    customArrowIcon={null}
                                                    customCloseIcon={null}
                                                    customInputIcon={null}
                                                    disabled={false}
                                                    endDate="Sep 11, 2021"
                                                    endDateId="campaign-activities_Budget vs Actual Expense_endDate"
                                                    endDatePlaceholderText="End Date"
                                                    inputIconPosition="before"
                                                    isEndDateFocused={false}
                                                    isFocused={false}
                                                    isRTL={false}
                                                    isStartDateFocused={false}
                                                    noBorder={false}
                                                    onClearDates={[Function]}
                                                    onEndDateChange={[Function]}
                                                    onEndDateFocus={[Function]}
                                                    onEndDateTab={[Function]}
                                                    onKeyDownArrowDown={[Function]}
                                                    onKeyDownQuestionMark={[Function]}
                                                    onStartDateChange={[Function]}
                                                    onStartDateFocus={[Function]}
                                                    onStartDateShiftTab={[Function]}
                                                    openDirection="down"
                                                    phrases={
                                                      Object {
                                                        "calendarLabel": "Calendar",
                                                        "chooseAvailableEndDate": [Function],
                                                        "chooseAvailableStartDate": [Function],
                                                        "clearDates": "Clear Dates",
                                                        "closeDatePicker": "Close",
                                                        "dateIsSelected": [Function],
                                                        "dateIsSelectedAsEndDate": [Function],
                                                        "dateIsSelectedAsStartDate": [Function],
                                                        "dateIsUnavailable": [Function],
                                                        "enterKey": "Enter key",
                                                        "escape": "Escape key",
                                                        "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                        "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                        "homeEnd": "Home and end keys",
                                                        "jumpToNextMonth": "Move forward to switch to the next month.",
                                                        "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                        "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                        "keyboardShortcuts": "Keyboard Shortcuts",
                                                        "leftArrowRightArrow": "Right and left arrow keys",
                                                        "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                        "moveFocusByOneMonth": "Switch months.",
                                                        "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                        "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                        "openThisPanel": "Open this panel.",
                                                        "pageUpPageDown": "page up and page down keys",
                                                        "questionMark": "Question mark",
                                                        "returnFocusToInput": "Return to the date input field.",
                                                        "selectFocusedDate": "Select the date in focus.",
                                                        "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                        "upArrowDownArrow": "up and down arrow keys",
                                                      }
                                                    }
                                                    readOnly={false}
                                                    regular={false}
                                                    required={false}
                                                    screenReaderMessage=""
                                                    showCaret={true}
                                                    showClearDates={false}
                                                    showDefaultInputIcon={false}
                                                    small={false}
                                                    startDate="Sep 10, 2021"
                                                    startDateId="campaign-activities_Budget vs Actual Expense_startDate"
                                                    startDatePlaceholderText="Start Date"
                                                    styles={
                                                      Object {
                                                        "DateRangePickerInput": "DateRangePickerInput",
                                                        "DateRangePickerInput__block": "DateRangePickerInput__block",
                                                        "DateRangePickerInput__disabled": "DateRangePickerInput__disabled",
                                                        "DateRangePickerInput__rtl": "DateRangePickerInput__rtl",
                                                        "DateRangePickerInput__showClearDates": "DateRangePickerInput__showClearDates",
                                                        "DateRangePickerInput__withBorder": "DateRangePickerInput__withBorder",
                                                        "DateRangePickerInput_arrow": "DateRangePickerInput_arrow",
                                                        "DateRangePickerInput_arrow_svg": "DateRangePickerInput_arrow_svg",
                                                        "DateRangePickerInput_calendarIcon": "DateRangePickerInput_calendarIcon",
                                                        "DateRangePickerInput_calendarIcon_svg": "DateRangePickerInput_calendarIcon_svg",
                                                        "DateRangePickerInput_clearDates": "DateRangePickerInput_clearDates",
                                                        "DateRangePickerInput_clearDates__hide": "DateRangePickerInput_clearDates__hide",
                                                        "DateRangePickerInput_clearDates__small": "DateRangePickerInput_clearDates__small",
                                                        "DateRangePickerInput_clearDates_default": "DateRangePickerInput_clearDates_default",
                                                        "DateRangePickerInput_clearDates_svg": "DateRangePickerInput_clearDates_svg",
                                                        "DateRangePickerInput_clearDates_svg__small": "DateRangePickerInput_clearDates_svg__small",
                                                      }
                                                    }
                                                    theme={
                                                      Object {
                                                        "reactDates": Object {
                                                          "border": Object {
                                                            "input": Object {
                                                              "border": 0,
                                                              "borderBottom": "2px solid transparent",
                                                              "borderBottomFocused": "2px solid #008489",
                                                              "borderFocused": 0,
                                                              "borderLeft": 0,
                                                              "borderLeftFocused": 0,
                                                              "borderRadius": 0,
                                                              "borderRight": 0,
                                                              "borderRightFocused": 0,
                                                              "borderTop": 0,
                                                              "borderTopFocused": 0,
                                                              "outlineFocused": 0,
                                                            },
                                                            "pickerInput": Object {
                                                              "borderRadius": 2,
                                                              "borderStyle": "solid",
                                                              "borderWidth": 1,
                                                            },
                                                          },
                                                          "color": Object {
                                                            "background": "#fff",
                                                            "backgroundDark": "#f2f2f2",
                                                            "backgroundFocused": "#fff",
                                                            "blocked_calendar": Object {
                                                              "backgroundColor": "#cacccd",
                                                              "backgroundColor_active": "#cacccd",
                                                              "backgroundColor_hover": "#cacccd",
                                                              "borderColor": "#cacccd",
                                                              "borderColor_active": "#cacccd",
                                                              "borderColor_hover": "#cacccd",
                                                              "color": "#82888a",
                                                              "color_active": "#82888a",
                                                              "color_hover": "#82888a",
                                                            },
                                                            "blocked_out_of_range": Object {
                                                              "backgroundColor": "#fff",
                                                              "backgroundColor_active": "#fff",
                                                              "backgroundColor_hover": "#fff",
                                                              "borderColor": "#e4e7e7",
                                                              "borderColor_active": "#e4e7e7",
                                                              "borderColor_hover": "#e4e7e7",
                                                              "color": "#cacccd",
                                                              "color_active": "#cacccd",
                                                              "color_hover": "#cacccd",
                                                            },
                                                            "border": "rgb(219, 219, 219)",
                                                            "core": Object {
                                                              "border": "#dbdbdb",
                                                              "borderBright": "#f4f5f5",
                                                              "borderLight": "#e4e7e7",
                                                              "borderLighter": "#eceeee",
                                                              "borderMedium": "#c4c4c4",
                                                              "gray": "#484848",
                                                              "grayLight": "#82888a",
                                                              "grayLighter": "#cacccd",
                                                              "grayLightest": "#f2f2f2",
                                                              "primary": "#00a699",
                                                              "primaryShade_1": "#33dacd",
                                                              "primaryShade_2": "#66e2da",
                                                              "primaryShade_3": "#80e8e0",
                                                              "primaryShade_4": "#b2f1ec",
                                                              "primary_dark": "#008489",
                                                              "secondary": "#007a87",
                                                              "white": "#fff",
                                                              "yellow": "#ffe8bc",
                                                              "yellow_dark": "#ffce71",
                                                            },
                                                            "disabled": "#f2f2f2",
                                                            "highlighted": Object {
                                                              "backgroundColor": "#ffe8bc",
                                                              "backgroundColor_active": "#ffce71",
                                                              "backgroundColor_hover": "#ffce71",
                                                              "color": "#484848",
                                                              "color_active": "#484848",
                                                              "color_hover": "#484848",
                                                            },
                                                            "hoveredSpan": Object {
                                                              "backgroundColor": "#b2f1ec",
                                                              "backgroundColor_active": "#80e8e0",
                                                              "backgroundColor_hover": "#b2f1ec",
                                                              "borderColor": "#80e8e0",
                                                              "borderColor_active": "#80e8e0",
                                                              "borderColor_hover": "#80e8e0",
                                                              "color": "#007a87",
                                                              "color_active": "#007a87",
                                                              "color_hover": "#007a87",
                                                            },
                                                            "minimumNights": Object {
                                                              "backgroundColor": "#fff",
                                                              "backgroundColor_active": "#fff",
                                                              "backgroundColor_hover": "#fff",
                                                              "borderColor": "#eceeee",
                                                              "color": "#cacccd",
                                                              "color_active": "#cacccd",
                                                              "color_hover": "#cacccd",
                                                            },
                                                            "outside": Object {
                                                              "backgroundColor": "#fff",
                                                              "backgroundColor_active": "#fff",
                                                              "backgroundColor_hover": "#fff",
                                                              "color": "#484848",
                                                              "color_active": "#484848",
                                                              "color_hover": "#484848",
                                                            },
                                                            "placeholderText": "#757575",
                                                            "selected": Object {
                                                              "backgroundColor": "#00a699",
                                                              "backgroundColor_active": "#00a699",
                                                              "backgroundColor_hover": "#00a699",
                                                              "borderColor": "#00a699",
                                                              "borderColor_active": "#00a699",
                                                              "borderColor_hover": "#00a699",
                                                              "color": "#fff",
                                                              "color_active": "#fff",
                                                              "color_hover": "#fff",
                                                            },
                                                            "selectedSpan": Object {
                                                              "backgroundColor": "#66e2da",
                                                              "backgroundColor_active": "#33dacd",
                                                              "backgroundColor_hover": "#33dacd",
                                                              "borderColor": "#33dacd",
                                                              "borderColor_active": "#00a699",
                                                              "borderColor_hover": "#00a699",
                                                              "color": "#fff",
                                                              "color_active": "#fff",
                                                              "color_hover": "#fff",
                                                            },
                                                            "text": "#484848",
                                                            "textDisabled": "#dbdbdb",
                                                            "textFocused": "#007a87",
                                                          },
                                                          "font": Object {
                                                            "captionSize": 18,
                                                            "input": Object {
                                                              "letterSpacing_small": "0.2px",
                                                              "lineHeight": "24px",
                                                              "lineHeight_small": "18px",
                                                              "size": 19,
                                                              "size_small": 15,
                                                              "styleDisabled": "italic",
                                                            },
                                                            "size": 14,
                                                          },
                                                          "noScrollBarOnVerticalScrollable": false,
                                                          "sizing": Object {
                                                            "arrowWidth": 24,
                                                            "inputWidth": 130,
                                                            "inputWidth_small": 97,
                                                          },
                                                          "spacing": Object {
                                                            "captionPaddingBottom": 37,
                                                            "captionPaddingTop": 22,
                                                            "dayPickerHorizontalPadding": 9,
                                                            "displayTextPaddingBottom": 9,
                                                            "displayTextPaddingBottom_small": 5,
                                                            "displayTextPaddingHorizontal": undefined,
                                                            "displayTextPaddingHorizontal_small": undefined,
                                                            "displayTextPaddingLeft": 11,
                                                            "displayTextPaddingLeft_small": 7,
                                                            "displayTextPaddingRight": 11,
                                                            "displayTextPaddingRight_small": 7,
                                                            "displayTextPaddingTop": 11,
                                                            "displayTextPaddingTop_small": 7,
                                                            "displayTextPaddingVertical": undefined,
                                                            "displayTextPaddingVertical_small": undefined,
                                                            "inputPadding": 0,
                                                          },
                                                          "zIndex": 0,
                                                        },
                                                      }
                                                    }
                                                    verticalSpacing={22}
                                                  >
                                                    <div
                                                      className="DateRangePickerInput DateRangePickerInput_1 DateRangePickerInput__withBorder DateRangePickerInput__withBorder_2"
                                                    >
                                                      <withStyles(DateInput)
                                                        block={false}
                                                        disabled={false}
                                                        displayValue="Sep 10, 2021"
                                                        focused={false}
                                                        id="campaign-activities_Budget vs Actual Expense_startDate"
                                                        isFocused={false}
                                                        onChange={[Function]}
                                                        onFocus={[Function]}
                                                        onKeyDownArrowDown={[Function]}
                                                        onKeyDownQuestionMark={[Function]}
                                                        onKeyDownShiftTab={[Function]}
                                                        onKeyDownTab={[Function]}
                                                        openDirection="down"
                                                        placeholder="Start Date"
                                                        readOnly={false}
                                                        regular={false}
                                                        required={false}
                                                        screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                        showCaret={true}
                                                        small={false}
                                                        verticalSpacing={22}
                                                      >
                                                        <DateInput
                                                          block={false}
                                                          css={[Function]}
                                                          disabled={false}
                                                          displayValue="Sep 10, 2021"
                                                          focused={false}
                                                          id="campaign-activities_Budget vs Actual Expense_startDate"
                                                          isFocused={false}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          onKeyDownArrowDown={[Function]}
                                                          onKeyDownQuestionMark={[Function]}
                                                          onKeyDownShiftTab={[Function]}
                                                          onKeyDownTab={[Function]}
                                                          openDirection="down"
                                                          placeholder="Start Date"
                                                          readOnly={false}
                                                          regular={false}
                                                          required={false}
                                                          screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                          showCaret={true}
                                                          small={false}
                                                          styles={
                                                            Object {
                                                              "DateInput": "DateInput",
                                                              "DateInput__block": "DateInput__block",
                                                              "DateInput__disabled": "DateInput__disabled",
                                                              "DateInput__small": "DateInput__small",
                                                              "DateInput_fang": "DateInput_fang",
                                                              "DateInput_fangShape": "DateInput_fangShape",
                                                              "DateInput_fangStroke": "DateInput_fangStroke",
                                                              "DateInput_input": "DateInput_input",
                                                              "DateInput_input__disabled": "DateInput_input__disabled",
                                                              "DateInput_input__focused": "DateInput_input__focused",
                                                              "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                              "DateInput_input__regular": "DateInput_input__regular",
                                                              "DateInput_input__small": "DateInput_input__small",
                                                              "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                            }
                                                          }
                                                          theme={
                                                            Object {
                                                              "reactDates": Object {
                                                                "border": Object {
                                                                  "input": Object {
                                                                    "border": 0,
                                                                    "borderBottom": "2px solid transparent",
                                                                    "borderBottomFocused": "2px solid #008489",
                                                                    "borderFocused": 0,
                                                                    "borderLeft": 0,
                                                                    "borderLeftFocused": 0,
                                                                    "borderRadius": 0,
                                                                    "borderRight": 0,
                                                                    "borderRightFocused": 0,
                                                                    "borderTop": 0,
                                                                    "borderTopFocused": 0,
                                                                    "outlineFocused": 0,
                                                                  },
                                                                  "pickerInput": Object {
                                                                    "borderRadius": 2,
                                                                    "borderStyle": "solid",
                                                                    "borderWidth": 1,
                                                                  },
                                                                },
                                                                "color": Object {
                                                                  "background": "#fff",
                                                                  "backgroundDark": "#f2f2f2",
                                                                  "backgroundFocused": "#fff",
                                                                  "blocked_calendar": Object {
                                                                    "backgroundColor": "#cacccd",
                                                                    "backgroundColor_active": "#cacccd",
                                                                    "backgroundColor_hover": "#cacccd",
                                                                    "borderColor": "#cacccd",
                                                                    "borderColor_active": "#cacccd",
                                                                    "borderColor_hover": "#cacccd",
                                                                    "color": "#82888a",
                                                                    "color_active": "#82888a",
                                                                    "color_hover": "#82888a",
                                                                  },
                                                                  "blocked_out_of_range": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "borderColor": "#e4e7e7",
                                                                    "borderColor_active": "#e4e7e7",
                                                                    "borderColor_hover": "#e4e7e7",
                                                                    "color": "#cacccd",
                                                                    "color_active": "#cacccd",
                                                                    "color_hover": "#cacccd",
                                                                  },
                                                                  "border": "rgb(219, 219, 219)",
                                                                  "core": Object {
                                                                    "border": "#dbdbdb",
                                                                    "borderBright": "#f4f5f5",
                                                                    "borderLight": "#e4e7e7",
                                                                    "borderLighter": "#eceeee",
                                                                    "borderMedium": "#c4c4c4",
                                                                    "gray": "#484848",
                                                                    "grayLight": "#82888a",
                                                                    "grayLighter": "#cacccd",
                                                                    "grayLightest": "#f2f2f2",
                                                                    "primary": "#00a699",
                                                                    "primaryShade_1": "#33dacd",
                                                                    "primaryShade_2": "#66e2da",
                                                                    "primaryShade_3": "#80e8e0",
                                                                    "primaryShade_4": "#b2f1ec",
                                                                    "primary_dark": "#008489",
                                                                    "secondary": "#007a87",
                                                                    "white": "#fff",
                                                                    "yellow": "#ffe8bc",
                                                                    "yellow_dark": "#ffce71",
                                                                  },
                                                                  "disabled": "#f2f2f2",
                                                                  "highlighted": Object {
                                                                    "backgroundColor": "#ffe8bc",
                                                                    "backgroundColor_active": "#ffce71",
                                                                    "backgroundColor_hover": "#ffce71",
                                                                    "color": "#484848",
                                                                    "color_active": "#484848",
                                                                    "color_hover": "#484848",
                                                                  },
                                                                  "hoveredSpan": Object {
                                                                    "backgroundColor": "#b2f1ec",
                                                                    "backgroundColor_active": "#80e8e0",
                                                                    "backgroundColor_hover": "#b2f1ec",
                                                                    "borderColor": "#80e8e0",
                                                                    "borderColor_active": "#80e8e0",
                                                                    "borderColor_hover": "#80e8e0",
                                                                    "color": "#007a87",
                                                                    "color_active": "#007a87",
                                                                    "color_hover": "#007a87",
                                                                  },
                                                                  "minimumNights": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "borderColor": "#eceeee",
                                                                    "color": "#cacccd",
                                                                    "color_active": "#cacccd",
                                                                    "color_hover": "#cacccd",
                                                                  },
                                                                  "outside": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "color": "#484848",
                                                                    "color_active": "#484848",
                                                                    "color_hover": "#484848",
                                                                  },
                                                                  "placeholderText": "#757575",
                                                                  "selected": Object {
                                                                    "backgroundColor": "#00a699",
                                                                    "backgroundColor_active": "#00a699",
                                                                    "backgroundColor_hover": "#00a699",
                                                                    "borderColor": "#00a699",
                                                                    "borderColor_active": "#00a699",
                                                                    "borderColor_hover": "#00a699",
                                                                    "color": "#fff",
                                                                    "color_active": "#fff",
                                                                    "color_hover": "#fff",
                                                                  },
                                                                  "selectedSpan": Object {
                                                                    "backgroundColor": "#66e2da",
                                                                    "backgroundColor_active": "#33dacd",
                                                                    "backgroundColor_hover": "#33dacd",
                                                                    "borderColor": "#33dacd",
                                                                    "borderColor_active": "#00a699",
                                                                    "borderColor_hover": "#00a699",
                                                                    "color": "#fff",
                                                                    "color_active": "#fff",
                                                                    "color_hover": "#fff",
                                                                  },
                                                                  "text": "#484848",
                                                                  "textDisabled": "#dbdbdb",
                                                                  "textFocused": "#007a87",
                                                                },
                                                                "font": Object {
                                                                  "captionSize": 18,
                                                                  "input": Object {
                                                                    "letterSpacing_small": "0.2px",
                                                                    "lineHeight": "24px",
                                                                    "lineHeight_small": "18px",
                                                                    "size": 19,
                                                                    "size_small": 15,
                                                                    "styleDisabled": "italic",
                                                                  },
                                                                  "size": 14,
                                                                },
                                                                "noScrollBarOnVerticalScrollable": false,
                                                                "sizing": Object {
                                                                  "arrowWidth": 24,
                                                                  "inputWidth": 130,
                                                                  "inputWidth_small": 97,
                                                                },
                                                                "spacing": Object {
                                                                  "captionPaddingBottom": 37,
                                                                  "captionPaddingTop": 22,
                                                                  "dayPickerHorizontalPadding": 9,
                                                                  "displayTextPaddingBottom": 9,
                                                                  "displayTextPaddingBottom_small": 5,
                                                                  "displayTextPaddingHorizontal": undefined,
                                                                  "displayTextPaddingHorizontal_small": undefined,
                                                                  "displayTextPaddingLeft": 11,
                                                                  "displayTextPaddingLeft_small": 7,
                                                                  "displayTextPaddingRight": 11,
                                                                  "displayTextPaddingRight_small": 7,
                                                                  "displayTextPaddingTop": 11,
                                                                  "displayTextPaddingTop_small": 7,
                                                                  "displayTextPaddingVertical": undefined,
                                                                  "displayTextPaddingVertical_small": undefined,
                                                                  "inputPadding": 0,
                                                                },
                                                                "zIndex": 0,
                                                              },
                                                            }
                                                          }
                                                          verticalSpacing={22}
                                                        >
                                                          <div
                                                            className="DateInput DateInput_1"
                                                          >
                                                            <input
                                                              aria-describedby="DateInput__screen-reader-message-campaign-activities_Budget vs Actual Expense_startDate"
                                                              aria-label="Start Date"
                                                              autoComplete="off"
                                                              className="DateInput_input DateInput_input_1"
                                                              disabled={false}
                                                              id="campaign-activities_Budget vs Actual Expense_startDate"
                                                              name="campaign-activities_Budget vs Actual Expense_startDate"
                                                              onChange={[Function]}
                                                              onFocus={[Function]}
                                                              onKeyDown={[Function]}
                                                              placeholder="Start Date"
                                                              readOnly={false}
                                                              required={false}
                                                              type="text"
                                                              value="Sep 10, 2021"
                                                            />
                                                            <p
                                                              className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                              id="DateInput__screen-reader-message-campaign-activities_Budget vs Actual Expense_startDate"
                                                            >
                                                              Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                            </p>
                                                          </div>
                                                        </DateInput>
                                                      </withStyles(DateInput)>
                                                      <div
                                                        aria-hidden="true"
                                                        className="DateRangePickerInput_arrow DateRangePickerInput_arrow_1"
                                                        role="presentation"
                                                      >
                                                        <RightArrow
                                                          className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                          focusable="false"
                                                          viewBox="0 0 1000 1000"
                                                        >
                                                          <svg
                                                            className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                            focusable="false"
                                                            viewBox="0 0 1000 1000"
                                                          >
                                                            <path
                                                              d="M694 242l249 250c12 11 12 21 1 32L694 773c-5 5-10 7-16 7s-11-2-16-7c-11-11-11-21 0-32l210-210H68c-13 0-23-10-23-23s10-23 23-23h806L662 275c-21-22 11-54 32-33z"
                                                            />
                                                          </svg>
                                                        </RightArrow>
                                                      </div>
                                                      <withStyles(DateInput)
                                                        block={false}
                                                        disabled={false}
                                                        displayValue="Sep 11, 2021"
                                                        focused={false}
                                                        id="campaign-activities_Budget vs Actual Expense_endDate"
                                                        isFocused={false}
                                                        onChange={[Function]}
                                                        onFocus={[Function]}
                                                        onKeyDownArrowDown={[Function]}
                                                        onKeyDownQuestionMark={[Function]}
                                                        onKeyDownShiftTab={[Function]}
                                                        onKeyDownTab={[Function]}
                                                        openDirection="down"
                                                        placeholder="End Date"
                                                        readOnly={false}
                                                        regular={false}
                                                        required={false}
                                                        screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                        showCaret={true}
                                                        small={false}
                                                        verticalSpacing={22}
                                                      >
                                                        <DateInput
                                                          block={false}
                                                          css={[Function]}
                                                          disabled={false}
                                                          displayValue="Sep 11, 2021"
                                                          focused={false}
                                                          id="campaign-activities_Budget vs Actual Expense_endDate"
                                                          isFocused={false}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          onKeyDownArrowDown={[Function]}
                                                          onKeyDownQuestionMark={[Function]}
                                                          onKeyDownShiftTab={[Function]}
                                                          onKeyDownTab={[Function]}
                                                          openDirection="down"
                                                          placeholder="End Date"
                                                          readOnly={false}
                                                          regular={false}
                                                          required={false}
                                                          screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                          showCaret={true}
                                                          small={false}
                                                          styles={
                                                            Object {
                                                              "DateInput": "DateInput",
                                                              "DateInput__block": "DateInput__block",
                                                              "DateInput__disabled": "DateInput__disabled",
                                                              "DateInput__small": "DateInput__small",
                                                              "DateInput_fang": "DateInput_fang",
                                                              "DateInput_fangShape": "DateInput_fangShape",
                                                              "DateInput_fangStroke": "DateInput_fangStroke",
                                                              "DateInput_input": "DateInput_input",
                                                              "DateInput_input__disabled": "DateInput_input__disabled",
                                                              "DateInput_input__focused": "DateInput_input__focused",
                                                              "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                              "DateInput_input__regular": "DateInput_input__regular",
                                                              "DateInput_input__small": "DateInput_input__small",
                                                              "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                            }
                                                          }
                                                          theme={
                                                            Object {
                                                              "reactDates": Object {
                                                                "border": Object {
                                                                  "input": Object {
                                                                    "border": 0,
                                                                    "borderBottom": "2px solid transparent",
                                                                    "borderBottomFocused": "2px solid #008489",
                                                                    "borderFocused": 0,
                                                                    "borderLeft": 0,
                                                                    "borderLeftFocused": 0,
                                                                    "borderRadius": 0,
                                                                    "borderRight": 0,
                                                                    "borderRightFocused": 0,
                                                                    "borderTop": 0,
                                                                    "borderTopFocused": 0,
                                                                    "outlineFocused": 0,
                                                                  },
                                                                  "pickerInput": Object {
                                                                    "borderRadius": 2,
                                                                    "borderStyle": "solid",
                                                                    "borderWidth": 1,
                                                                  },
                                                                },
                                                                "color": Object {
                                                                  "background": "#fff",
                                                                  "backgroundDark": "#f2f2f2",
                                                                  "backgroundFocused": "#fff",
                                                                  "blocked_calendar": Object {
                                                                    "backgroundColor": "#cacccd",
                                                                    "backgroundColor_active": "#cacccd",
                                                                    "backgroundColor_hover": "#cacccd",
                                                                    "borderColor": "#cacccd",
                                                                    "borderColor_active": "#cacccd",
                                                                    "borderColor_hover": "#cacccd",
                                                                    "color": "#82888a",
                                                                    "color_active": "#82888a",
                                                                    "color_hover": "#82888a",
                                                                  },
                                                                  "blocked_out_of_range": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "borderColor": "#e4e7e7",
                                                                    "borderColor_active": "#e4e7e7",
                                                                    "borderColor_hover": "#e4e7e7",
                                                                    "color": "#cacccd",
                                                                    "color_active": "#cacccd",
                                                                    "color_hover": "#cacccd",
                                                                  },
                                                                  "border": "rgb(219, 219, 219)",
                                                                  "core": Object {
                                                                    "border": "#dbdbdb",
                                                                    "borderBright": "#f4f5f5",
                                                                    "borderLight": "#e4e7e7",
                                                                    "borderLighter": "#eceeee",
                                                                    "borderMedium": "#c4c4c4",
                                                                    "gray": "#484848",
                                                                    "grayLight": "#82888a",
                                                                    "grayLighter": "#cacccd",
                                                                    "grayLightest": "#f2f2f2",
                                                                    "primary": "#00a699",
                                                                    "primaryShade_1": "#33dacd",
                                                                    "primaryShade_2": "#66e2da",
                                                                    "primaryShade_3": "#80e8e0",
                                                                    "primaryShade_4": "#b2f1ec",
                                                                    "primary_dark": "#008489",
                                                                    "secondary": "#007a87",
                                                                    "white": "#fff",
                                                                    "yellow": "#ffe8bc",
                                                                    "yellow_dark": "#ffce71",
                                                                  },
                                                                  "disabled": "#f2f2f2",
                                                                  "highlighted": Object {
                                                                    "backgroundColor": "#ffe8bc",
                                                                    "backgroundColor_active": "#ffce71",
                                                                    "backgroundColor_hover": "#ffce71",
                                                                    "color": "#484848",
                                                                    "color_active": "#484848",
                                                                    "color_hover": "#484848",
                                                                  },
                                                                  "hoveredSpan": Object {
                                                                    "backgroundColor": "#b2f1ec",
                                                                    "backgroundColor_active": "#80e8e0",
                                                                    "backgroundColor_hover": "#b2f1ec",
                                                                    "borderColor": "#80e8e0",
                                                                    "borderColor_active": "#80e8e0",
                                                                    "borderColor_hover": "#80e8e0",
                                                                    "color": "#007a87",
                                                                    "color_active": "#007a87",
                                                                    "color_hover": "#007a87",
                                                                  },
                                                                  "minimumNights": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "borderColor": "#eceeee",
                                                                    "color": "#cacccd",
                                                                    "color_active": "#cacccd",
                                                                    "color_hover": "#cacccd",
                                                                  },
                                                                  "outside": Object {
                                                                    "backgroundColor": "#fff",
                                                                    "backgroundColor_active": "#fff",
                                                                    "backgroundColor_hover": "#fff",
                                                                    "color": "#484848",
                                                                    "color_active": "#484848",
                                                                    "color_hover": "#484848",
                                                                  },
                                                                  "placeholderText": "#757575",
                                                                  "selected": Object {
                                                                    "backgroundColor": "#00a699",
                                                                    "backgroundColor_active": "#00a699",
                                                                    "backgroundColor_hover": "#00a699",
                                                                    "borderColor": "#00a699",
                                                                    "borderColor_active": "#00a699",
                                                                    "borderColor_hover": "#00a699",
                                                                    "color": "#fff",
                                                                    "color_active": "#fff",
                                                                    "color_hover": "#fff",
                                                                  },
                                                                  "selectedSpan": Object {
                                                                    "backgroundColor": "#66e2da",
                                                                    "backgroundColor_active": "#33dacd",
                                                                    "backgroundColor_hover": "#33dacd",
                                                                    "borderColor": "#33dacd",
                                                                    "borderColor_active": "#00a699",
                                                                    "borderColor_hover": "#00a699",
                                                                    "color": "#fff",
                                                                    "color_active": "#fff",
                                                                    "color_hover": "#fff",
                                                                  },
                                                                  "text": "#484848",
                                                                  "textDisabled": "#dbdbdb",
                                                                  "textFocused": "#007a87",
                                                                },
                                                                "font": Object {
                                                                  "captionSize": 18,
                                                                  "input": Object {
                                                                    "letterSpacing_small": "0.2px",
                                                                    "lineHeight": "24px",
                                                                    "lineHeight_small": "18px",
                                                                    "size": 19,
                                                                    "size_small": 15,
                                                                    "styleDisabled": "italic",
                                                                  },
                                                                  "size": 14,
                                                                },
                                                                "noScrollBarOnVerticalScrollable": false,
                                                                "sizing": Object {
                                                                  "arrowWidth": 24,
                                                                  "inputWidth": 130,
                                                                  "inputWidth_small": 97,
                                                                },
                                                                "spacing": Object {
                                                                  "captionPaddingBottom": 37,
                                                                  "captionPaddingTop": 22,
                                                                  "dayPickerHorizontalPadding": 9,
                                                                  "displayTextPaddingBottom": 9,
                                                                  "displayTextPaddingBottom_small": 5,
                                                                  "displayTextPaddingHorizontal": undefined,
                                                                  "displayTextPaddingHorizontal_small": undefined,
                                                                  "displayTextPaddingLeft": 11,
                                                                  "displayTextPaddingLeft_small": 7,
                                                                  "displayTextPaddingRight": 11,
                                                                  "displayTextPaddingRight_small": 7,
                                                                  "displayTextPaddingTop": 11,
                                                                  "displayTextPaddingTop_small": 7,
                                                                  "displayTextPaddingVertical": undefined,
                                                                  "displayTextPaddingVertical_small": undefined,
                                                                  "inputPadding": 0,
                                                                },
                                                                "zIndex": 0,
                                                              },
                                                            }
                                                          }
                                                          verticalSpacing={22}
                                                        >
                                                          <div
                                                            className="DateInput DateInput_1"
                                                          >
                                                            <input
                                                              aria-describedby="DateInput__screen-reader-message-campaign-activities_Budget vs Actual Expense_endDate"
                                                              aria-label="End Date"
                                                              autoComplete="off"
                                                              className="DateInput_input DateInput_input_1"
                                                              disabled={false}
                                                              id="campaign-activities_Budget vs Actual Expense_endDate"
                                                              name="campaign-activities_Budget vs Actual Expense_endDate"
                                                              onChange={[Function]}
                                                              onFocus={[Function]}
                                                              onKeyDown={[Function]}
                                                              placeholder="End Date"
                                                              readOnly={false}
                                                              required={false}
                                                              type="text"
                                                              value="Sep 11, 2021"
                                                            />
                                                            <p
                                                              className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                              id="DateInput__screen-reader-message-campaign-activities_Budget vs Actual Expense_endDate"
                                                            >
                                                              Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                            </p>
                                                          </div>
                                                        </DateInput>
                                                      </withStyles(DateInput)>
                                                    </div>
                                                  </DateRangePickerInput>
                                                </withStyles(DateRangePickerInput)>
                                              </DateRangePickerInputController>
                                            </div>
                                          </OutsideClickHandler>
                                        </div>
                                      </DateRangePicker>
                                    </withStyles(DateRangePicker)>
                                    <div
                                      className="date-range__value"
                                    >
                                      <span
                                        className="svg-inline--fa fa fa-calendar-alt cursor-pointer f-14"
                                        onClick={[Function]}
                                        style={
                                          Object {
                                            "color": "#2E384D",
                                          }
                                        }
                                      />
                                      <div
                                        className="date-value"
                                      >
                                        <span
                                          className="f-12 align-self-center"
                                        >
                                          Sep 10, 2021
                                        </span>
                                        <span
                                          className="f-12"
                                        >
                                           - 
                                        </span>
                                        <span
                                          className="f-12 align-self-center"
                                        >
                                          Sep 11, 2021
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </DateRangePickerComponent>
                              </Connect(DateRangePickerComponent)>
                            </div>
                            <div
                              className="bar-chart__container"
                            >
                              <ForwardRef
                                minHeight={320}
                              >
                                <ResizeDetector
                                  handleHeight={true}
                                  handleWidth={true}
                                  onResize={[Function]}
                                  refreshRate={0}
                                  targetRef={
                                    Object {
                                      "current": <div
                                        class="recharts-responsive-container"
                                        style="width: 100%; height: 100%; min-width: 0; min-height: 320px;"
                                      />,
                                    }
                                  }
                                >
                                  <div
                                    className="recharts-responsive-container"
                                    style={
                                      Object {
                                        "height": "100%",
                                        "maxHeight": undefined,
                                        "minHeight": 320,
                                        "minWidth": 0,
                                        "width": "100%",
                                      }
                                    }
                                  >
                                    <BarChart
                                      barCategoryGap="30%"
                                      barGap={4}
                                      data={
                                        Array [
                                          Object {
                                            "color": "#006DEE",
                                            "dimension": Array [],
                                            "id": null,
                                            "name": "Budget",
                                            "value": 100000,
                                          },
                                          Object {
                                            "color": "#00780E",
                                            "dimension": Array [],
                                            "id": null,
                                            "name": "Actual",
                                            "value": 110000,
                                          },
                                        ]
                                      }
                                      height={0}
                                      layout="horizontal"
                                      margin={
                                        Object {
                                          "bottom": 5,
                                          "left": 5,
                                          "right": 5,
                                          "top": 5,
                                        }
                                      }
                                      reverseStackOrder={false}
                                      stackOffset="none"
                                      syncMethod="index"
                                      width={0}
                                    />
                                  </div>
                                </ResizeDetector>
                              </ForwardRef>
                            </div>
                          </div>
                        </BarChartReport>
                      </Connect(BarChartReport)>
                    </div>
                  </div>
                  <div
                    className="row"
                  >
                    <div
                      className="col-12"
                    >
                      <WithAbortController
                        activityId={1}
                        campaignId={123}
                        entity="campaign-activities"
                        history={
                          Object {
                            "push": [MockFunction],
                          }
                        }
                        isMultiLineChart={false}
                        key="1"
                        multiDimensionToRender={null}
                        numberFormat="INDIAN_NUMBER_FORMAT"
                        strokeColor="#7856FF"
                        timezone="Asia/Calcutta"
                        type="Overall Engagement"
                      >
                        <Component
                          abortSignal={AbortSignal {}}
                          activityId={1}
                          campaignId={123}
                          entity="campaign-activities"
                          history={
                            Object {
                              "push": [MockFunction],
                            }
                          }
                          isMultiLineChart={false}
                          multiDimensionToRender={null}
                          numberFormat="INDIAN_NUMBER_FORMAT"
                          strokeColor="#7856FF"
                          timezone="Asia/Calcutta"
                          type="Overall Engagement"
                        >
                          <CampaignActivityLineChart
                            abortSignal={AbortSignal {}}
                            activityId={1}
                            campaignId={123}
                            data={
                              Array [
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Sent",
                                  "value": 1000,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Delivered",
                                  "value": 800,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Read",
                                  "value": 500,
                                },
                                Object {
                                  "dimension": Array [],
                                  "id": null,
                                  "name": "Failed",
                                  "value": 200,
                                },
                              ]
                            }
                            entity="campaign-activities"
                            error={null}
                            fetchData={[Function]}
                            history={
                              Object {
                                "push": [MockFunction],
                              }
                            }
                            isMultiLineChart={false}
                            loading={false}
                            multiDimensionToRender={null}
                            numberFormat="INDIAN_NUMBER_FORMAT"
                            strokeColor="#7856FF"
                            timezone="Asia/Calcutta"
                            type="Overall Engagement"
                          >
                            <div
                              className="campaign-activity__line-chart"
                            >
                              <div
                                className="d-flex justify-content-between"
                              >
                                <div
                                  className="f-14"
                                >
                                  Overall Engagement
                                </div>
                                <div
                                  className="d-flex"
                                >
                                  <Connect(DateRangePickerComponent)
                                    defaultDateRange={
                                      Object {
                                        "endDate": "2025-05-04T18:29:59.999Z",
                                        "startDate": "2025-04-27T18:30:00.000Z",
                                      }
                                    }
                                    isDateRangeEditable={true}
                                    uniqueKey="campaign-activities_Overall Engagement"
                                    updateDateRange={[Function]}
                                  >
                                    <DateRangePickerComponent
                                      dateFormat="MMM D, YYYY [at] h:mm a"
                                      defaultDateRange={
                                        Object {
                                          "endDate": "2025-05-04T18:29:59.999Z",
                                          "startDate": "2025-04-27T18:30:00.000Z",
                                        }
                                      }
                                      dispatch={[Function]}
                                      isDateRangeEditable={true}
                                      timezone="Asia/Calcutta"
                                      uniqueKey="campaign-activities_Overall Engagement"
                                      updateDateRange={[Function]}
                                    >
                                      <div
                                        className="date-range__picker"
                                      >
                                        <withStyles(DateRangePicker)
                                          anchorDirection="left"
                                          appendToBody={false}
                                          block={false}
                                          calendarInfoPosition="bottom"
                                          customArrowIcon={null}
                                          customCloseIcon={null}
                                          customInputIcon={null}
                                          daySize={24}
                                          disableScroll={false}
                                          disabled={false}
                                          displayFormat="MMM D, YYYY"
                                          enableOutsideDays={false}
                                          endDate={"2025-05-04T18:29:59.999Z"}
                                          endDateId="campaign-activities_Overall Engagement_endDate"
                                          endDatePlaceholderText="End Date"
                                          firstDayOfWeek={null}
                                          focusedInput={null}
                                          hideKeyboardShortcutsPanel={false}
                                          horizontalMargin={0}
                                          initialVisibleMonth={null}
                                          inputIconPosition="before"
                                          isDayBlocked={[Function]}
                                          isDayHighlighted={[Function]}
                                          isOutsideRange={[Function]}
                                          isRTL={false}
                                          keepFocusOnInput={false}
                                          keepOpenOnDateSelect={true}
                                          minimumNights={0}
                                          monthFormat="MMMM YYYY"
                                          navNext={
                                            <i
                                              className="svg-inline--fa fa fa-angle-right"
                                            />
                                          }
                                          navPrev={
                                            <i
                                              className="svg-inline--fa fa fa-angle-left left-button"
                                            />
                                          }
                                          noBorder={false}
                                          numberOfMonths={2}
                                          onClose={[Function]}
                                          onDatesChange={[Function]}
                                          onFocusChange={[Function]}
                                          onNextMonthClick={[Function]}
                                          onPrevMonthClick={[Function]}
                                          openDirection="down"
                                          orientation="horizontal"
                                          phrases={
                                            Object {
                                              "calendarLabel": "Calendar",
                                              "chooseAvailableEndDate": [Function],
                                              "chooseAvailableStartDate": [Function],
                                              "clearDates": "Clear Dates",
                                              "closeDatePicker": "Close",
                                              "dateIsSelected": [Function],
                                              "dateIsSelectedAsEndDate": [Function],
                                              "dateIsSelectedAsStartDate": [Function],
                                              "dateIsUnavailable": [Function],
                                              "enterKey": "Enter key",
                                              "escape": "Escape key",
                                              "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                              "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                              "homeEnd": "Home and end keys",
                                              "jumpToNextMonth": "Move forward to switch to the next month.",
                                              "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                              "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                              "keyboardShortcuts": "Keyboard Shortcuts",
                                              "leftArrowRightArrow": "Right and left arrow keys",
                                              "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                              "moveFocusByOneMonth": "Switch months.",
                                              "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                              "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                              "openThisPanel": "Open this panel.",
                                              "pageUpPageDown": "page up and page down keys",
                                              "questionMark": "Question mark",
                                              "returnFocusToInput": "Return to the date input field.",
                                              "selectFocusedDate": "Select the date in focus.",
                                              "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                              "upArrowDownArrow": "up and down arrow keys",
                                            }
                                          }
                                          readOnly={false}
                                          regular={false}
                                          renderCalendarInfo={[Function]}
                                          renderDayContents={null}
                                          renderMonthElement={[Function]}
                                          renderMonthText={null}
                                          reopenPickerOnClearDates={false}
                                          required={false}
                                          screenReaderInputMessage=""
                                          showClearDates={false}
                                          showDefaultInputIcon={false}
                                          small={false}
                                          startDate={"2025-04-27T18:30:00.000Z"}
                                          startDateId="campaign-activities_Overall Engagement_startDate"
                                          startDatePlaceholderText="Start Date"
                                          verticalHeight={null}
                                          verticalSpacing={22}
                                          weekDayFormat="dd"
                                          withFullScreenPortal={false}
                                          withPortal={false}
                                        >
                                          <DateRangePicker
                                            anchorDirection="left"
                                            appendToBody={false}
                                            block={false}
                                            calendarInfoPosition="bottom"
                                            css={[Function]}
                                            customArrowIcon={null}
                                            customCloseIcon={null}
                                            customInputIcon={null}
                                            daySize={24}
                                            disableScroll={false}
                                            disabled={false}
                                            displayFormat="MMM D, YYYY"
                                            enableOutsideDays={false}
                                            endDate={"2025-05-04T18:29:59.999Z"}
                                            endDateId="campaign-activities_Overall Engagement_endDate"
                                            endDatePlaceholderText="End Date"
                                            firstDayOfWeek={null}
                                            focusedInput={null}
                                            hideKeyboardShortcutsPanel={false}
                                            horizontalMargin={0}
                                            initialVisibleMonth={null}
                                            inputIconPosition="before"
                                            isDayBlocked={[Function]}
                                            isDayHighlighted={[Function]}
                                            isOutsideRange={[Function]}
                                            isRTL={false}
                                            keepFocusOnInput={false}
                                            keepOpenOnDateSelect={true}
                                            minimumNights={0}
                                            monthFormat="MMMM YYYY"
                                            navNext={
                                              <i
                                                className="svg-inline--fa fa fa-angle-right"
                                              />
                                            }
                                            navPrev={
                                              <i
                                                className="svg-inline--fa fa fa-angle-left left-button"
                                              />
                                            }
                                            noBorder={false}
                                            numberOfMonths={2}
                                            onClose={[Function]}
                                            onDatesChange={[Function]}
                                            onFocusChange={[Function]}
                                            onNextMonthClick={[Function]}
                                            onPrevMonthClick={[Function]}
                                            openDirection="down"
                                            orientation="horizontal"
                                            phrases={
                                              Object {
                                                "calendarLabel": "Calendar",
                                                "chooseAvailableEndDate": [Function],
                                                "chooseAvailableStartDate": [Function],
                                                "clearDates": "Clear Dates",
                                                "closeDatePicker": "Close",
                                                "dateIsSelected": [Function],
                                                "dateIsSelectedAsEndDate": [Function],
                                                "dateIsSelectedAsStartDate": [Function],
                                                "dateIsUnavailable": [Function],
                                                "enterKey": "Enter key",
                                                "escape": "Escape key",
                                                "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                "homeEnd": "Home and end keys",
                                                "jumpToNextMonth": "Move forward to switch to the next month.",
                                                "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                "keyboardShortcuts": "Keyboard Shortcuts",
                                                "leftArrowRightArrow": "Right and left arrow keys",
                                                "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                "moveFocusByOneMonth": "Switch months.",
                                                "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                "openThisPanel": "Open this panel.",
                                                "pageUpPageDown": "page up and page down keys",
                                                "questionMark": "Question mark",
                                                "returnFocusToInput": "Return to the date input field.",
                                                "selectFocusedDate": "Select the date in focus.",
                                                "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                "upArrowDownArrow": "up and down arrow keys",
                                              }
                                            }
                                            readOnly={false}
                                            regular={false}
                                            renderCalendarInfo={[Function]}
                                            renderDayContents={null}
                                            renderMonthElement={[Function]}
                                            renderMonthText={null}
                                            reopenPickerOnClearDates={false}
                                            required={false}
                                            screenReaderInputMessage=""
                                            showClearDates={false}
                                            showDefaultInputIcon={false}
                                            small={false}
                                            startDate={"2025-04-27T18:30:00.000Z"}
                                            startDateId="campaign-activities_Overall Engagement_startDate"
                                            startDatePlaceholderText="Start Date"
                                            styles={
                                              Object {
                                                "DateRangePicker": "DateRangePicker",
                                                "DateRangePicker__block": "DateRangePicker__block",
                                                "DateRangePicker_closeButton": "DateRangePicker_closeButton",
                                                "DateRangePicker_closeButton_svg": "DateRangePicker_closeButton_svg",
                                                "DateRangePicker_picker": "DateRangePicker_picker",
                                                "DateRangePicker_picker__directionLeft": "DateRangePicker_picker__directionLeft",
                                                "DateRangePicker_picker__directionRight": "DateRangePicker_picker__directionRight",
                                                "DateRangePicker_picker__fullScreenPortal": "DateRangePicker_picker__fullScreenPortal",
                                                "DateRangePicker_picker__portal": "DateRangePicker_picker__portal",
                                                "DateRangePicker_picker__rtl": "DateRangePicker_picker__rtl",
                                              }
                                            }
                                            theme={
                                              Object {
                                                "reactDates": Object {
                                                  "border": Object {
                                                    "input": Object {
                                                      "border": 0,
                                                      "borderBottom": "2px solid transparent",
                                                      "borderBottomFocused": "2px solid #008489",
                                                      "borderFocused": 0,
                                                      "borderLeft": 0,
                                                      "borderLeftFocused": 0,
                                                      "borderRadius": 0,
                                                      "borderRight": 0,
                                                      "borderRightFocused": 0,
                                                      "borderTop": 0,
                                                      "borderTopFocused": 0,
                                                      "outlineFocused": 0,
                                                    },
                                                    "pickerInput": Object {
                                                      "borderRadius": 2,
                                                      "borderStyle": "solid",
                                                      "borderWidth": 1,
                                                    },
                                                  },
                                                  "color": Object {
                                                    "background": "#fff",
                                                    "backgroundDark": "#f2f2f2",
                                                    "backgroundFocused": "#fff",
                                                    "blocked_calendar": Object {
                                                      "backgroundColor": "#cacccd",
                                                      "backgroundColor_active": "#cacccd",
                                                      "backgroundColor_hover": "#cacccd",
                                                      "borderColor": "#cacccd",
                                                      "borderColor_active": "#cacccd",
                                                      "borderColor_hover": "#cacccd",
                                                      "color": "#82888a",
                                                      "color_active": "#82888a",
                                                      "color_hover": "#82888a",
                                                    },
                                                    "blocked_out_of_range": Object {
                                                      "backgroundColor": "#fff",
                                                      "backgroundColor_active": "#fff",
                                                      "backgroundColor_hover": "#fff",
                                                      "borderColor": "#e4e7e7",
                                                      "borderColor_active": "#e4e7e7",
                                                      "borderColor_hover": "#e4e7e7",
                                                      "color": "#cacccd",
                                                      "color_active": "#cacccd",
                                                      "color_hover": "#cacccd",
                                                    },
                                                    "border": "rgb(219, 219, 219)",
                                                    "core": Object {
                                                      "border": "#dbdbdb",
                                                      "borderBright": "#f4f5f5",
                                                      "borderLight": "#e4e7e7",
                                                      "borderLighter": "#eceeee",
                                                      "borderMedium": "#c4c4c4",
                                                      "gray": "#484848",
                                                      "grayLight": "#82888a",
                                                      "grayLighter": "#cacccd",
                                                      "grayLightest": "#f2f2f2",
                                                      "primary": "#00a699",
                                                      "primaryShade_1": "#33dacd",
                                                      "primaryShade_2": "#66e2da",
                                                      "primaryShade_3": "#80e8e0",
                                                      "primaryShade_4": "#b2f1ec",
                                                      "primary_dark": "#008489",
                                                      "secondary": "#007a87",
                                                      "white": "#fff",
                                                      "yellow": "#ffe8bc",
                                                      "yellow_dark": "#ffce71",
                                                    },
                                                    "disabled": "#f2f2f2",
                                                    "highlighted": Object {
                                                      "backgroundColor": "#ffe8bc",
                                                      "backgroundColor_active": "#ffce71",
                                                      "backgroundColor_hover": "#ffce71",
                                                      "color": "#484848",
                                                      "color_active": "#484848",
                                                      "color_hover": "#484848",
                                                    },
                                                    "hoveredSpan": Object {
                                                      "backgroundColor": "#b2f1ec",
                                                      "backgroundColor_active": "#80e8e0",
                                                      "backgroundColor_hover": "#b2f1ec",
                                                      "borderColor": "#80e8e0",
                                                      "borderColor_active": "#80e8e0",
                                                      "borderColor_hover": "#80e8e0",
                                                      "color": "#007a87",
                                                      "color_active": "#007a87",
                                                      "color_hover": "#007a87",
                                                    },
                                                    "minimumNights": Object {
                                                      "backgroundColor": "#fff",
                                                      "backgroundColor_active": "#fff",
                                                      "backgroundColor_hover": "#fff",
                                                      "borderColor": "#eceeee",
                                                      "color": "#cacccd",
                                                      "color_active": "#cacccd",
                                                      "color_hover": "#cacccd",
                                                    },
                                                    "outside": Object {
                                                      "backgroundColor": "#fff",
                                                      "backgroundColor_active": "#fff",
                                                      "backgroundColor_hover": "#fff",
                                                      "color": "#484848",
                                                      "color_active": "#484848",
                                                      "color_hover": "#484848",
                                                    },
                                                    "placeholderText": "#757575",
                                                    "selected": Object {
                                                      "backgroundColor": "#00a699",
                                                      "backgroundColor_active": "#00a699",
                                                      "backgroundColor_hover": "#00a699",
                                                      "borderColor": "#00a699",
                                                      "borderColor_active": "#00a699",
                                                      "borderColor_hover": "#00a699",
                                                      "color": "#fff",
                                                      "color_active": "#fff",
                                                      "color_hover": "#fff",
                                                    },
                                                    "selectedSpan": Object {
                                                      "backgroundColor": "#66e2da",
                                                      "backgroundColor_active": "#33dacd",
                                                      "backgroundColor_hover": "#33dacd",
                                                      "borderColor": "#33dacd",
                                                      "borderColor_active": "#00a699",
                                                      "borderColor_hover": "#00a699",
                                                      "color": "#fff",
                                                      "color_active": "#fff",
                                                      "color_hover": "#fff",
                                                    },
                                                    "text": "#484848",
                                                    "textDisabled": "#dbdbdb",
                                                    "textFocused": "#007a87",
                                                  },
                                                  "font": Object {
                                                    "captionSize": 18,
                                                    "input": Object {
                                                      "letterSpacing_small": "0.2px",
                                                      "lineHeight": "24px",
                                                      "lineHeight_small": "18px",
                                                      "size": 19,
                                                      "size_small": 15,
                                                      "styleDisabled": "italic",
                                                    },
                                                    "size": 14,
                                                  },
                                                  "noScrollBarOnVerticalScrollable": false,
                                                  "sizing": Object {
                                                    "arrowWidth": 24,
                                                    "inputWidth": 130,
                                                    "inputWidth_small": 97,
                                                  },
                                                  "spacing": Object {
                                                    "captionPaddingBottom": 37,
                                                    "captionPaddingTop": 22,
                                                    "dayPickerHorizontalPadding": 9,
                                                    "displayTextPaddingBottom": 9,
                                                    "displayTextPaddingBottom_small": 5,
                                                    "displayTextPaddingHorizontal": undefined,
                                                    "displayTextPaddingHorizontal_small": undefined,
                                                    "displayTextPaddingLeft": 11,
                                                    "displayTextPaddingLeft_small": 7,
                                                    "displayTextPaddingRight": 11,
                                                    "displayTextPaddingRight_small": 7,
                                                    "displayTextPaddingTop": 11,
                                                    "displayTextPaddingTop_small": 7,
                                                    "displayTextPaddingVertical": undefined,
                                                    "displayTextPaddingVertical_small": undefined,
                                                    "inputPadding": 0,
                                                  },
                                                  "zIndex": 0,
                                                },
                                              }
                                            }
                                            verticalHeight={null}
                                            verticalSpacing={22}
                                            weekDayFormat="dd"
                                            withFullScreenPortal={false}
                                            withPortal={false}
                                          >
                                            <div
                                              className="DateRangePicker DateRangePicker_1"
                                            >
                                              <OutsideClickHandler
                                                disabled={false}
                                                display="block"
                                                onOutsideClick={[Function]}
                                                useCapture={true}
                                              >
                                                <div>
                                                  <DateRangePickerInputController
                                                    block={false}
                                                    customArrowIcon={null}
                                                    customCloseIcon={null}
                                                    customInputIcon={null}
                                                    disabled={false}
                                                    displayFormat="MMM D, YYYY"
                                                    endDate={"2025-05-04T18:29:59.999Z"}
                                                    endDateId="campaign-activities_Overall Engagement_endDate"
                                                    endDatePlaceholderText="End Date"
                                                    inputIconPosition="before"
                                                    isEndDateFocused={false}
                                                    isFocused={false}
                                                    isOutsideRange={[Function]}
                                                    isRTL={false}
                                                    isStartDateFocused={false}
                                                    keepOpenOnDateSelect={true}
                                                    minimumNights={0}
                                                    noBorder={false}
                                                    onClose={[Function]}
                                                    onDatesChange={[Function]}
                                                    onFocusChange={[Function]}
                                                    onKeyDownArrowDown={[Function]}
                                                    onKeyDownQuestionMark={[Function]}
                                                    openDirection="down"
                                                    phrases={
                                                      Object {
                                                        "calendarLabel": "Calendar",
                                                        "chooseAvailableEndDate": [Function],
                                                        "chooseAvailableStartDate": [Function],
                                                        "clearDates": "Clear Dates",
                                                        "closeDatePicker": "Close",
                                                        "dateIsSelected": [Function],
                                                        "dateIsSelectedAsEndDate": [Function],
                                                        "dateIsSelectedAsStartDate": [Function],
                                                        "dateIsUnavailable": [Function],
                                                        "enterKey": "Enter key",
                                                        "escape": "Escape key",
                                                        "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                        "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                        "homeEnd": "Home and end keys",
                                                        "jumpToNextMonth": "Move forward to switch to the next month.",
                                                        "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                        "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                        "keyboardShortcuts": "Keyboard Shortcuts",
                                                        "leftArrowRightArrow": "Right and left arrow keys",
                                                        "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                        "moveFocusByOneMonth": "Switch months.",
                                                        "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                        "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                        "openThisPanel": "Open this panel.",
                                                        "pageUpPageDown": "page up and page down keys",
                                                        "questionMark": "Question mark",
                                                        "returnFocusToInput": "Return to the date input field.",
                                                        "selectFocusedDate": "Select the date in focus.",
                                                        "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                        "upArrowDownArrow": "up and down arrow keys",
                                                      }
                                                    }
                                                    readOnly={false}
                                                    regular={false}
                                                    reopenPickerOnClearDates={false}
                                                    required={false}
                                                    screenReaderMessage=""
                                                    showCaret={true}
                                                    showClearDates={false}
                                                    showDefaultInputIcon={false}
                                                    small={false}
                                                    startDate={"2025-04-27T18:30:00.000Z"}
                                                    startDateId="campaign-activities_Overall Engagement_startDate"
                                                    startDatePlaceholderText="Start Date"
                                                    verticalSpacing={22}
                                                    withFullScreenPortal={false}
                                                  >
                                                    <withStyles(DateRangePickerInput)
                                                      block={false}
                                                      customArrowIcon={null}
                                                      customCloseIcon={null}
                                                      customInputIcon={null}
                                                      disabled={false}
                                                      endDate="May 4, 2025"
                                                      endDateId="campaign-activities_Overall Engagement_endDate"
                                                      endDatePlaceholderText="End Date"
                                                      inputIconPosition="before"
                                                      isEndDateFocused={false}
                                                      isFocused={false}
                                                      isRTL={false}
                                                      isStartDateFocused={false}
                                                      noBorder={false}
                                                      onClearDates={[Function]}
                                                      onEndDateChange={[Function]}
                                                      onEndDateFocus={[Function]}
                                                      onEndDateTab={[Function]}
                                                      onKeyDownArrowDown={[Function]}
                                                      onKeyDownQuestionMark={[Function]}
                                                      onStartDateChange={[Function]}
                                                      onStartDateFocus={[Function]}
                                                      onStartDateShiftTab={[Function]}
                                                      openDirection="down"
                                                      phrases={
                                                        Object {
                                                          "calendarLabel": "Calendar",
                                                          "chooseAvailableEndDate": [Function],
                                                          "chooseAvailableStartDate": [Function],
                                                          "clearDates": "Clear Dates",
                                                          "closeDatePicker": "Close",
                                                          "dateIsSelected": [Function],
                                                          "dateIsSelectedAsEndDate": [Function],
                                                          "dateIsSelectedAsStartDate": [Function],
                                                          "dateIsUnavailable": [Function],
                                                          "enterKey": "Enter key",
                                                          "escape": "Escape key",
                                                          "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                          "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                          "homeEnd": "Home and end keys",
                                                          "jumpToNextMonth": "Move forward to switch to the next month.",
                                                          "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                          "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                          "keyboardShortcuts": "Keyboard Shortcuts",
                                                          "leftArrowRightArrow": "Right and left arrow keys",
                                                          "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                          "moveFocusByOneMonth": "Switch months.",
                                                          "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                          "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                          "openThisPanel": "Open this panel.",
                                                          "pageUpPageDown": "page up and page down keys",
                                                          "questionMark": "Question mark",
                                                          "returnFocusToInput": "Return to the date input field.",
                                                          "selectFocusedDate": "Select the date in focus.",
                                                          "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                          "upArrowDownArrow": "up and down arrow keys",
                                                        }
                                                      }
                                                      readOnly={false}
                                                      regular={false}
                                                      required={false}
                                                      screenReaderMessage=""
                                                      showCaret={true}
                                                      showClearDates={false}
                                                      showDefaultInputIcon={false}
                                                      small={false}
                                                      startDate="Apr 28, 2025"
                                                      startDateId="campaign-activities_Overall Engagement_startDate"
                                                      startDatePlaceholderText="Start Date"
                                                      verticalSpacing={22}
                                                    >
                                                      <DateRangePickerInput
                                                        block={false}
                                                        css={[Function]}
                                                        customArrowIcon={null}
                                                        customCloseIcon={null}
                                                        customInputIcon={null}
                                                        disabled={false}
                                                        endDate="May 4, 2025"
                                                        endDateId="campaign-activities_Overall Engagement_endDate"
                                                        endDatePlaceholderText="End Date"
                                                        inputIconPosition="before"
                                                        isEndDateFocused={false}
                                                        isFocused={false}
                                                        isRTL={false}
                                                        isStartDateFocused={false}
                                                        noBorder={false}
                                                        onClearDates={[Function]}
                                                        onEndDateChange={[Function]}
                                                        onEndDateFocus={[Function]}
                                                        onEndDateTab={[Function]}
                                                        onKeyDownArrowDown={[Function]}
                                                        onKeyDownQuestionMark={[Function]}
                                                        onStartDateChange={[Function]}
                                                        onStartDateFocus={[Function]}
                                                        onStartDateShiftTab={[Function]}
                                                        openDirection="down"
                                                        phrases={
                                                          Object {
                                                            "calendarLabel": "Calendar",
                                                            "chooseAvailableEndDate": [Function],
                                                            "chooseAvailableStartDate": [Function],
                                                            "clearDates": "Clear Dates",
                                                            "closeDatePicker": "Close",
                                                            "dateIsSelected": [Function],
                                                            "dateIsSelectedAsEndDate": [Function],
                                                            "dateIsSelectedAsStartDate": [Function],
                                                            "dateIsUnavailable": [Function],
                                                            "enterKey": "Enter key",
                                                            "escape": "Escape key",
                                                            "focusStartDate": "Interact with the calendar and add the check-in date for your trip.",
                                                            "hideKeyboardShortcutsPanel": "Close the shortcuts panel.",
                                                            "homeEnd": "Home and end keys",
                                                            "jumpToNextMonth": "Move forward to switch to the next month.",
                                                            "jumpToPrevMonth": "Move backward to switch to the previous month.",
                                                            "keyboardNavigationInstructions": "Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.",
                                                            "keyboardShortcuts": "Keyboard Shortcuts",
                                                            "leftArrowRightArrow": "Right and left arrow keys",
                                                            "moveFocusByOneDay": "Move backward (left) and forward (right) by one day.",
                                                            "moveFocusByOneMonth": "Switch months.",
                                                            "moveFocusByOneWeek": "Move backward (up) and forward (down) by one week.",
                                                            "moveFocustoStartAndEndOfWeek": "Go to the first or last day of a week.",
                                                            "openThisPanel": "Open this panel.",
                                                            "pageUpPageDown": "page up and page down keys",
                                                            "questionMark": "Question mark",
                                                            "returnFocusToInput": "Return to the date input field.",
                                                            "selectFocusedDate": "Select the date in focus.",
                                                            "showKeyboardShortcutsPanel": "Open the keyboard shortcuts panel.",
                                                            "upArrowDownArrow": "up and down arrow keys",
                                                          }
                                                        }
                                                        readOnly={false}
                                                        regular={false}
                                                        required={false}
                                                        screenReaderMessage=""
                                                        showCaret={true}
                                                        showClearDates={false}
                                                        showDefaultInputIcon={false}
                                                        small={false}
                                                        startDate="Apr 28, 2025"
                                                        startDateId="campaign-activities_Overall Engagement_startDate"
                                                        startDatePlaceholderText="Start Date"
                                                        styles={
                                                          Object {
                                                            "DateRangePickerInput": "DateRangePickerInput",
                                                            "DateRangePickerInput__block": "DateRangePickerInput__block",
                                                            "DateRangePickerInput__disabled": "DateRangePickerInput__disabled",
                                                            "DateRangePickerInput__rtl": "DateRangePickerInput__rtl",
                                                            "DateRangePickerInput__showClearDates": "DateRangePickerInput__showClearDates",
                                                            "DateRangePickerInput__withBorder": "DateRangePickerInput__withBorder",
                                                            "DateRangePickerInput_arrow": "DateRangePickerInput_arrow",
                                                            "DateRangePickerInput_arrow_svg": "DateRangePickerInput_arrow_svg",
                                                            "DateRangePickerInput_calendarIcon": "DateRangePickerInput_calendarIcon",
                                                            "DateRangePickerInput_calendarIcon_svg": "DateRangePickerInput_calendarIcon_svg",
                                                            "DateRangePickerInput_clearDates": "DateRangePickerInput_clearDates",
                                                            "DateRangePickerInput_clearDates__hide": "DateRangePickerInput_clearDates__hide",
                                                            "DateRangePickerInput_clearDates__small": "DateRangePickerInput_clearDates__small",
                                                            "DateRangePickerInput_clearDates_default": "DateRangePickerInput_clearDates_default",
                                                            "DateRangePickerInput_clearDates_svg": "DateRangePickerInput_clearDates_svg",
                                                            "DateRangePickerInput_clearDates_svg__small": "DateRangePickerInput_clearDates_svg__small",
                                                          }
                                                        }
                                                        theme={
                                                          Object {
                                                            "reactDates": Object {
                                                              "border": Object {
                                                                "input": Object {
                                                                  "border": 0,
                                                                  "borderBottom": "2px solid transparent",
                                                                  "borderBottomFocused": "2px solid #008489",
                                                                  "borderFocused": 0,
                                                                  "borderLeft": 0,
                                                                  "borderLeftFocused": 0,
                                                                  "borderRadius": 0,
                                                                  "borderRight": 0,
                                                                  "borderRightFocused": 0,
                                                                  "borderTop": 0,
                                                                  "borderTopFocused": 0,
                                                                  "outlineFocused": 0,
                                                                },
                                                                "pickerInput": Object {
                                                                  "borderRadius": 2,
                                                                  "borderStyle": "solid",
                                                                  "borderWidth": 1,
                                                                },
                                                              },
                                                              "color": Object {
                                                                "background": "#fff",
                                                                "backgroundDark": "#f2f2f2",
                                                                "backgroundFocused": "#fff",
                                                                "blocked_calendar": Object {
                                                                  "backgroundColor": "#cacccd",
                                                                  "backgroundColor_active": "#cacccd",
                                                                  "backgroundColor_hover": "#cacccd",
                                                                  "borderColor": "#cacccd",
                                                                  "borderColor_active": "#cacccd",
                                                                  "borderColor_hover": "#cacccd",
                                                                  "color": "#82888a",
                                                                  "color_active": "#82888a",
                                                                  "color_hover": "#82888a",
                                                                },
                                                                "blocked_out_of_range": Object {
                                                                  "backgroundColor": "#fff",
                                                                  "backgroundColor_active": "#fff",
                                                                  "backgroundColor_hover": "#fff",
                                                                  "borderColor": "#e4e7e7",
                                                                  "borderColor_active": "#e4e7e7",
                                                                  "borderColor_hover": "#e4e7e7",
                                                                  "color": "#cacccd",
                                                                  "color_active": "#cacccd",
                                                                  "color_hover": "#cacccd",
                                                                },
                                                                "border": "rgb(219, 219, 219)",
                                                                "core": Object {
                                                                  "border": "#dbdbdb",
                                                                  "borderBright": "#f4f5f5",
                                                                  "borderLight": "#e4e7e7",
                                                                  "borderLighter": "#eceeee",
                                                                  "borderMedium": "#c4c4c4",
                                                                  "gray": "#484848",
                                                                  "grayLight": "#82888a",
                                                                  "grayLighter": "#cacccd",
                                                                  "grayLightest": "#f2f2f2",
                                                                  "primary": "#00a699",
                                                                  "primaryShade_1": "#33dacd",
                                                                  "primaryShade_2": "#66e2da",
                                                                  "primaryShade_3": "#80e8e0",
                                                                  "primaryShade_4": "#b2f1ec",
                                                                  "primary_dark": "#008489",
                                                                  "secondary": "#007a87",
                                                                  "white": "#fff",
                                                                  "yellow": "#ffe8bc",
                                                                  "yellow_dark": "#ffce71",
                                                                },
                                                                "disabled": "#f2f2f2",
                                                                "highlighted": Object {
                                                                  "backgroundColor": "#ffe8bc",
                                                                  "backgroundColor_active": "#ffce71",
                                                                  "backgroundColor_hover": "#ffce71",
                                                                  "color": "#484848",
                                                                  "color_active": "#484848",
                                                                  "color_hover": "#484848",
                                                                },
                                                                "hoveredSpan": Object {
                                                                  "backgroundColor": "#b2f1ec",
                                                                  "backgroundColor_active": "#80e8e0",
                                                                  "backgroundColor_hover": "#b2f1ec",
                                                                  "borderColor": "#80e8e0",
                                                                  "borderColor_active": "#80e8e0",
                                                                  "borderColor_hover": "#80e8e0",
                                                                  "color": "#007a87",
                                                                  "color_active": "#007a87",
                                                                  "color_hover": "#007a87",
                                                                },
                                                                "minimumNights": Object {
                                                                  "backgroundColor": "#fff",
                                                                  "backgroundColor_active": "#fff",
                                                                  "backgroundColor_hover": "#fff",
                                                                  "borderColor": "#eceeee",
                                                                  "color": "#cacccd",
                                                                  "color_active": "#cacccd",
                                                                  "color_hover": "#cacccd",
                                                                },
                                                                "outside": Object {
                                                                  "backgroundColor": "#fff",
                                                                  "backgroundColor_active": "#fff",
                                                                  "backgroundColor_hover": "#fff",
                                                                  "color": "#484848",
                                                                  "color_active": "#484848",
                                                                  "color_hover": "#484848",
                                                                },
                                                                "placeholderText": "#757575",
                                                                "selected": Object {
                                                                  "backgroundColor": "#00a699",
                                                                  "backgroundColor_active": "#00a699",
                                                                  "backgroundColor_hover": "#00a699",
                                                                  "borderColor": "#00a699",
                                                                  "borderColor_active": "#00a699",
                                                                  "borderColor_hover": "#00a699",
                                                                  "color": "#fff",
                                                                  "color_active": "#fff",
                                                                  "color_hover": "#fff",
                                                                },
                                                                "selectedSpan": Object {
                                                                  "backgroundColor": "#66e2da",
                                                                  "backgroundColor_active": "#33dacd",
                                                                  "backgroundColor_hover": "#33dacd",
                                                                  "borderColor": "#33dacd",
                                                                  "borderColor_active": "#00a699",
                                                                  "borderColor_hover": "#00a699",
                                                                  "color": "#fff",
                                                                  "color_active": "#fff",
                                                                  "color_hover": "#fff",
                                                                },
                                                                "text": "#484848",
                                                                "textDisabled": "#dbdbdb",
                                                                "textFocused": "#007a87",
                                                              },
                                                              "font": Object {
                                                                "captionSize": 18,
                                                                "input": Object {
                                                                  "letterSpacing_small": "0.2px",
                                                                  "lineHeight": "24px",
                                                                  "lineHeight_small": "18px",
                                                                  "size": 19,
                                                                  "size_small": 15,
                                                                  "styleDisabled": "italic",
                                                                },
                                                                "size": 14,
                                                              },
                                                              "noScrollBarOnVerticalScrollable": false,
                                                              "sizing": Object {
                                                                "arrowWidth": 24,
                                                                "inputWidth": 130,
                                                                "inputWidth_small": 97,
                                                              },
                                                              "spacing": Object {
                                                                "captionPaddingBottom": 37,
                                                                "captionPaddingTop": 22,
                                                                "dayPickerHorizontalPadding": 9,
                                                                "displayTextPaddingBottom": 9,
                                                                "displayTextPaddingBottom_small": 5,
                                                                "displayTextPaddingHorizontal": undefined,
                                                                "displayTextPaddingHorizontal_small": undefined,
                                                                "displayTextPaddingLeft": 11,
                                                                "displayTextPaddingLeft_small": 7,
                                                                "displayTextPaddingRight": 11,
                                                                "displayTextPaddingRight_small": 7,
                                                                "displayTextPaddingTop": 11,
                                                                "displayTextPaddingTop_small": 7,
                                                                "displayTextPaddingVertical": undefined,
                                                                "displayTextPaddingVertical_small": undefined,
                                                                "inputPadding": 0,
                                                              },
                                                              "zIndex": 0,
                                                            },
                                                          }
                                                        }
                                                        verticalSpacing={22}
                                                      >
                                                        <div
                                                          className="DateRangePickerInput DateRangePickerInput_1 DateRangePickerInput__withBorder DateRangePickerInput__withBorder_2"
                                                        >
                                                          <withStyles(DateInput)
                                                            block={false}
                                                            disabled={false}
                                                            displayValue="Apr 28, 2025"
                                                            focused={false}
                                                            id="campaign-activities_Overall Engagement_startDate"
                                                            isFocused={false}
                                                            onChange={[Function]}
                                                            onFocus={[Function]}
                                                            onKeyDownArrowDown={[Function]}
                                                            onKeyDownQuestionMark={[Function]}
                                                            onKeyDownShiftTab={[Function]}
                                                            onKeyDownTab={[Function]}
                                                            openDirection="down"
                                                            placeholder="Start Date"
                                                            readOnly={false}
                                                            regular={false}
                                                            required={false}
                                                            screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                            showCaret={true}
                                                            small={false}
                                                            verticalSpacing={22}
                                                          >
                                                            <DateInput
                                                              block={false}
                                                              css={[Function]}
                                                              disabled={false}
                                                              displayValue="Apr 28, 2025"
                                                              focused={false}
                                                              id="campaign-activities_Overall Engagement_startDate"
                                                              isFocused={false}
                                                              onChange={[Function]}
                                                              onFocus={[Function]}
                                                              onKeyDownArrowDown={[Function]}
                                                              onKeyDownQuestionMark={[Function]}
                                                              onKeyDownShiftTab={[Function]}
                                                              onKeyDownTab={[Function]}
                                                              openDirection="down"
                                                              placeholder="Start Date"
                                                              readOnly={false}
                                                              regular={false}
                                                              required={false}
                                                              screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                              showCaret={true}
                                                              small={false}
                                                              styles={
                                                                Object {
                                                                  "DateInput": "DateInput",
                                                                  "DateInput__block": "DateInput__block",
                                                                  "DateInput__disabled": "DateInput__disabled",
                                                                  "DateInput__small": "DateInput__small",
                                                                  "DateInput_fang": "DateInput_fang",
                                                                  "DateInput_fangShape": "DateInput_fangShape",
                                                                  "DateInput_fangStroke": "DateInput_fangStroke",
                                                                  "DateInput_input": "DateInput_input",
                                                                  "DateInput_input__disabled": "DateInput_input__disabled",
                                                                  "DateInput_input__focused": "DateInput_input__focused",
                                                                  "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                                  "DateInput_input__regular": "DateInput_input__regular",
                                                                  "DateInput_input__small": "DateInput_input__small",
                                                                  "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                                }
                                                              }
                                                              theme={
                                                                Object {
                                                                  "reactDates": Object {
                                                                    "border": Object {
                                                                      "input": Object {
                                                                        "border": 0,
                                                                        "borderBottom": "2px solid transparent",
                                                                        "borderBottomFocused": "2px solid #008489",
                                                                        "borderFocused": 0,
                                                                        "borderLeft": 0,
                                                                        "borderLeftFocused": 0,
                                                                        "borderRadius": 0,
                                                                        "borderRight": 0,
                                                                        "borderRightFocused": 0,
                                                                        "borderTop": 0,
                                                                        "borderTopFocused": 0,
                                                                        "outlineFocused": 0,
                                                                      },
                                                                      "pickerInput": Object {
                                                                        "borderRadius": 2,
                                                                        "borderStyle": "solid",
                                                                        "borderWidth": 1,
                                                                      },
                                                                    },
                                                                    "color": Object {
                                                                      "background": "#fff",
                                                                      "backgroundDark": "#f2f2f2",
                                                                      "backgroundFocused": "#fff",
                                                                      "blocked_calendar": Object {
                                                                        "backgroundColor": "#cacccd",
                                                                        "backgroundColor_active": "#cacccd",
                                                                        "backgroundColor_hover": "#cacccd",
                                                                        "borderColor": "#cacccd",
                                                                        "borderColor_active": "#cacccd",
                                                                        "borderColor_hover": "#cacccd",
                                                                        "color": "#82888a",
                                                                        "color_active": "#82888a",
                                                                        "color_hover": "#82888a",
                                                                      },
                                                                      "blocked_out_of_range": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "borderColor": "#e4e7e7",
                                                                        "borderColor_active": "#e4e7e7",
                                                                        "borderColor_hover": "#e4e7e7",
                                                                        "color": "#cacccd",
                                                                        "color_active": "#cacccd",
                                                                        "color_hover": "#cacccd",
                                                                      },
                                                                      "border": "rgb(219, 219, 219)",
                                                                      "core": Object {
                                                                        "border": "#dbdbdb",
                                                                        "borderBright": "#f4f5f5",
                                                                        "borderLight": "#e4e7e7",
                                                                        "borderLighter": "#eceeee",
                                                                        "borderMedium": "#c4c4c4",
                                                                        "gray": "#484848",
                                                                        "grayLight": "#82888a",
                                                                        "grayLighter": "#cacccd",
                                                                        "grayLightest": "#f2f2f2",
                                                                        "primary": "#00a699",
                                                                        "primaryShade_1": "#33dacd",
                                                                        "primaryShade_2": "#66e2da",
                                                                        "primaryShade_3": "#80e8e0",
                                                                        "primaryShade_4": "#b2f1ec",
                                                                        "primary_dark": "#008489",
                                                                        "secondary": "#007a87",
                                                                        "white": "#fff",
                                                                        "yellow": "#ffe8bc",
                                                                        "yellow_dark": "#ffce71",
                                                                      },
                                                                      "disabled": "#f2f2f2",
                                                                      "highlighted": Object {
                                                                        "backgroundColor": "#ffe8bc",
                                                                        "backgroundColor_active": "#ffce71",
                                                                        "backgroundColor_hover": "#ffce71",
                                                                        "color": "#484848",
                                                                        "color_active": "#484848",
                                                                        "color_hover": "#484848",
                                                                      },
                                                                      "hoveredSpan": Object {
                                                                        "backgroundColor": "#b2f1ec",
                                                                        "backgroundColor_active": "#80e8e0",
                                                                        "backgroundColor_hover": "#b2f1ec",
                                                                        "borderColor": "#80e8e0",
                                                                        "borderColor_active": "#80e8e0",
                                                                        "borderColor_hover": "#80e8e0",
                                                                        "color": "#007a87",
                                                                        "color_active": "#007a87",
                                                                        "color_hover": "#007a87",
                                                                      },
                                                                      "minimumNights": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "borderColor": "#eceeee",
                                                                        "color": "#cacccd",
                                                                        "color_active": "#cacccd",
                                                                        "color_hover": "#cacccd",
                                                                      },
                                                                      "outside": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "color": "#484848",
                                                                        "color_active": "#484848",
                                                                        "color_hover": "#484848",
                                                                      },
                                                                      "placeholderText": "#757575",
                                                                      "selected": Object {
                                                                        "backgroundColor": "#00a699",
                                                                        "backgroundColor_active": "#00a699",
                                                                        "backgroundColor_hover": "#00a699",
                                                                        "borderColor": "#00a699",
                                                                        "borderColor_active": "#00a699",
                                                                        "borderColor_hover": "#00a699",
                                                                        "color": "#fff",
                                                                        "color_active": "#fff",
                                                                        "color_hover": "#fff",
                                                                      },
                                                                      "selectedSpan": Object {
                                                                        "backgroundColor": "#66e2da",
                                                                        "backgroundColor_active": "#33dacd",
                                                                        "backgroundColor_hover": "#33dacd",
                                                                        "borderColor": "#33dacd",
                                                                        "borderColor_active": "#00a699",
                                                                        "borderColor_hover": "#00a699",
                                                                        "color": "#fff",
                                                                        "color_active": "#fff",
                                                                        "color_hover": "#fff",
                                                                      },
                                                                      "text": "#484848",
                                                                      "textDisabled": "#dbdbdb",
                                                                      "textFocused": "#007a87",
                                                                    },
                                                                    "font": Object {
                                                                      "captionSize": 18,
                                                                      "input": Object {
                                                                        "letterSpacing_small": "0.2px",
                                                                        "lineHeight": "24px",
                                                                        "lineHeight_small": "18px",
                                                                        "size": 19,
                                                                        "size_small": 15,
                                                                        "styleDisabled": "italic",
                                                                      },
                                                                      "size": 14,
                                                                    },
                                                                    "noScrollBarOnVerticalScrollable": false,
                                                                    "sizing": Object {
                                                                      "arrowWidth": 24,
                                                                      "inputWidth": 130,
                                                                      "inputWidth_small": 97,
                                                                    },
                                                                    "spacing": Object {
                                                                      "captionPaddingBottom": 37,
                                                                      "captionPaddingTop": 22,
                                                                      "dayPickerHorizontalPadding": 9,
                                                                      "displayTextPaddingBottom": 9,
                                                                      "displayTextPaddingBottom_small": 5,
                                                                      "displayTextPaddingHorizontal": undefined,
                                                                      "displayTextPaddingHorizontal_small": undefined,
                                                                      "displayTextPaddingLeft": 11,
                                                                      "displayTextPaddingLeft_small": 7,
                                                                      "displayTextPaddingRight": 11,
                                                                      "displayTextPaddingRight_small": 7,
                                                                      "displayTextPaddingTop": 11,
                                                                      "displayTextPaddingTop_small": 7,
                                                                      "displayTextPaddingVertical": undefined,
                                                                      "displayTextPaddingVertical_small": undefined,
                                                                      "inputPadding": 0,
                                                                    },
                                                                    "zIndex": 0,
                                                                  },
                                                                }
                                                              }
                                                              verticalSpacing={22}
                                                            >
                                                              <div
                                                                className="DateInput DateInput_1"
                                                              >
                                                                <input
                                                                  aria-describedby="DateInput__screen-reader-message-campaign-activities_Overall Engagement_startDate"
                                                                  aria-label="Start Date"
                                                                  autoComplete="off"
                                                                  className="DateInput_input DateInput_input_1"
                                                                  disabled={false}
                                                                  id="campaign-activities_Overall Engagement_startDate"
                                                                  name="campaign-activities_Overall Engagement_startDate"
                                                                  onChange={[Function]}
                                                                  onFocus={[Function]}
                                                                  onKeyDown={[Function]}
                                                                  placeholder="Start Date"
                                                                  readOnly={false}
                                                                  required={false}
                                                                  type="text"
                                                                  value="Apr 28, 2025"
                                                                />
                                                                <p
                                                                  className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                                  id="DateInput__screen-reader-message-campaign-activities_Overall Engagement_startDate"
                                                                >
                                                                  Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                                </p>
                                                              </div>
                                                            </DateInput>
                                                          </withStyles(DateInput)>
                                                          <div
                                                            aria-hidden="true"
                                                            className="DateRangePickerInput_arrow DateRangePickerInput_arrow_1"
                                                            role="presentation"
                                                          >
                                                            <RightArrow
                                                              className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                              focusable="false"
                                                              viewBox="0 0 1000 1000"
                                                            >
                                                              <svg
                                                                className="DateRangePickerInput_arrow_svg DateRangePickerInput_arrow_svg_1"
                                                                focusable="false"
                                                                viewBox="0 0 1000 1000"
                                                              >
                                                                <path
                                                                  d="M694 242l249 250c12 11 12 21 1 32L694 773c-5 5-10 7-16 7s-11-2-16-7c-11-11-11-21 0-32l210-210H68c-13 0-23-10-23-23s10-23 23-23h806L662 275c-21-22 11-54 32-33z"
                                                                />
                                                              </svg>
                                                            </RightArrow>
                                                          </div>
                                                          <withStyles(DateInput)
                                                            block={false}
                                                            disabled={false}
                                                            displayValue="May 4, 2025"
                                                            focused={false}
                                                            id="campaign-activities_Overall Engagement_endDate"
                                                            isFocused={false}
                                                            onChange={[Function]}
                                                            onFocus={[Function]}
                                                            onKeyDownArrowDown={[Function]}
                                                            onKeyDownQuestionMark={[Function]}
                                                            onKeyDownShiftTab={[Function]}
                                                            onKeyDownTab={[Function]}
                                                            openDirection="down"
                                                            placeholder="End Date"
                                                            readOnly={false}
                                                            regular={false}
                                                            required={false}
                                                            screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                            showCaret={true}
                                                            small={false}
                                                            verticalSpacing={22}
                                                          >
                                                            <DateInput
                                                              block={false}
                                                              css={[Function]}
                                                              disabled={false}
                                                              displayValue="May 4, 2025"
                                                              focused={false}
                                                              id="campaign-activities_Overall Engagement_endDate"
                                                              isFocused={false}
                                                              onChange={[Function]}
                                                              onFocus={[Function]}
                                                              onKeyDownArrowDown={[Function]}
                                                              onKeyDownQuestionMark={[Function]}
                                                              onKeyDownShiftTab={[Function]}
                                                              onKeyDownTab={[Function]}
                                                              openDirection="down"
                                                              placeholder="End Date"
                                                              readOnly={false}
                                                              regular={false}
                                                              required={false}
                                                              screenReaderMessage="Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates."
                                                              showCaret={true}
                                                              small={false}
                                                              styles={
                                                                Object {
                                                                  "DateInput": "DateInput",
                                                                  "DateInput__block": "DateInput__block",
                                                                  "DateInput__disabled": "DateInput__disabled",
                                                                  "DateInput__small": "DateInput__small",
                                                                  "DateInput_fang": "DateInput_fang",
                                                                  "DateInput_fangShape": "DateInput_fangShape",
                                                                  "DateInput_fangStroke": "DateInput_fangStroke",
                                                                  "DateInput_input": "DateInput_input",
                                                                  "DateInput_input__disabled": "DateInput_input__disabled",
                                                                  "DateInput_input__focused": "DateInput_input__focused",
                                                                  "DateInput_input__readOnly": "DateInput_input__readOnly",
                                                                  "DateInput_input__regular": "DateInput_input__regular",
                                                                  "DateInput_input__small": "DateInput_input__small",
                                                                  "DateInput_screenReaderMessage": "DateInput_screenReaderMessage",
                                                                }
                                                              }
                                                              theme={
                                                                Object {
                                                                  "reactDates": Object {
                                                                    "border": Object {
                                                                      "input": Object {
                                                                        "border": 0,
                                                                        "borderBottom": "2px solid transparent",
                                                                        "borderBottomFocused": "2px solid #008489",
                                                                        "borderFocused": 0,
                                                                        "borderLeft": 0,
                                                                        "borderLeftFocused": 0,
                                                                        "borderRadius": 0,
                                                                        "borderRight": 0,
                                                                        "borderRightFocused": 0,
                                                                        "borderTop": 0,
                                                                        "borderTopFocused": 0,
                                                                        "outlineFocused": 0,
                                                                      },
                                                                      "pickerInput": Object {
                                                                        "borderRadius": 2,
                                                                        "borderStyle": "solid",
                                                                        "borderWidth": 1,
                                                                      },
                                                                    },
                                                                    "color": Object {
                                                                      "background": "#fff",
                                                                      "backgroundDark": "#f2f2f2",
                                                                      "backgroundFocused": "#fff",
                                                                      "blocked_calendar": Object {
                                                                        "backgroundColor": "#cacccd",
                                                                        "backgroundColor_active": "#cacccd",
                                                                        "backgroundColor_hover": "#cacccd",
                                                                        "borderColor": "#cacccd",
                                                                        "borderColor_active": "#cacccd",
                                                                        "borderColor_hover": "#cacccd",
                                                                        "color": "#82888a",
                                                                        "color_active": "#82888a",
                                                                        "color_hover": "#82888a",
                                                                      },
                                                                      "blocked_out_of_range": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "borderColor": "#e4e7e7",
                                                                        "borderColor_active": "#e4e7e7",
                                                                        "borderColor_hover": "#e4e7e7",
                                                                        "color": "#cacccd",
                                                                        "color_active": "#cacccd",
                                                                        "color_hover": "#cacccd",
                                                                      },
                                                                      "border": "rgb(219, 219, 219)",
                                                                      "core": Object {
                                                                        "border": "#dbdbdb",
                                                                        "borderBright": "#f4f5f5",
                                                                        "borderLight": "#e4e7e7",
                                                                        "borderLighter": "#eceeee",
                                                                        "borderMedium": "#c4c4c4",
                                                                        "gray": "#484848",
                                                                        "grayLight": "#82888a",
                                                                        "grayLighter": "#cacccd",
                                                                        "grayLightest": "#f2f2f2",
                                                                        "primary": "#00a699",
                                                                        "primaryShade_1": "#33dacd",
                                                                        "primaryShade_2": "#66e2da",
                                                                        "primaryShade_3": "#80e8e0",
                                                                        "primaryShade_4": "#b2f1ec",
                                                                        "primary_dark": "#008489",
                                                                        "secondary": "#007a87",
                                                                        "white": "#fff",
                                                                        "yellow": "#ffe8bc",
                                                                        "yellow_dark": "#ffce71",
                                                                      },
                                                                      "disabled": "#f2f2f2",
                                                                      "highlighted": Object {
                                                                        "backgroundColor": "#ffe8bc",
                                                                        "backgroundColor_active": "#ffce71",
                                                                        "backgroundColor_hover": "#ffce71",
                                                                        "color": "#484848",
                                                                        "color_active": "#484848",
                                                                        "color_hover": "#484848",
                                                                      },
                                                                      "hoveredSpan": Object {
                                                                        "backgroundColor": "#b2f1ec",
                                                                        "backgroundColor_active": "#80e8e0",
                                                                        "backgroundColor_hover": "#b2f1ec",
                                                                        "borderColor": "#80e8e0",
                                                                        "borderColor_active": "#80e8e0",
                                                                        "borderColor_hover": "#80e8e0",
                                                                        "color": "#007a87",
                                                                        "color_active": "#007a87",
                                                                        "color_hover": "#007a87",
                                                                      },
                                                                      "minimumNights": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "borderColor": "#eceeee",
                                                                        "color": "#cacccd",
                                                                        "color_active": "#cacccd",
                                                                        "color_hover": "#cacccd",
                                                                      },
                                                                      "outside": Object {
                                                                        "backgroundColor": "#fff",
                                                                        "backgroundColor_active": "#fff",
                                                                        "backgroundColor_hover": "#fff",
                                                                        "color": "#484848",
                                                                        "color_active": "#484848",
                                                                        "color_hover": "#484848",
                                                                      },
                                                                      "placeholderText": "#757575",
                                                                      "selected": Object {
                                                                        "backgroundColor": "#00a699",
                                                                        "backgroundColor_active": "#00a699",
                                                                        "backgroundColor_hover": "#00a699",
                                                                        "borderColor": "#00a699",
                                                                        "borderColor_active": "#00a699",
                                                                        "borderColor_hover": "#00a699",
                                                                        "color": "#fff",
                                                                        "color_active": "#fff",
                                                                        "color_hover": "#fff",
                                                                      },
                                                                      "selectedSpan": Object {
                                                                        "backgroundColor": "#66e2da",
                                                                        "backgroundColor_active": "#33dacd",
                                                                        "backgroundColor_hover": "#33dacd",
                                                                        "borderColor": "#33dacd",
                                                                        "borderColor_active": "#00a699",
                                                                        "borderColor_hover": "#00a699",
                                                                        "color": "#fff",
                                                                        "color_active": "#fff",
                                                                        "color_hover": "#fff",
                                                                      },
                                                                      "text": "#484848",
                                                                      "textDisabled": "#dbdbdb",
                                                                      "textFocused": "#007a87",
                                                                    },
                                                                    "font": Object {
                                                                      "captionSize": 18,
                                                                      "input": Object {
                                                                        "letterSpacing_small": "0.2px",
                                                                        "lineHeight": "24px",
                                                                        "lineHeight_small": "18px",
                                                                        "size": 19,
                                                                        "size_small": 15,
                                                                        "styleDisabled": "italic",
                                                                      },
                                                                      "size": 14,
                                                                    },
                                                                    "noScrollBarOnVerticalScrollable": false,
                                                                    "sizing": Object {
                                                                      "arrowWidth": 24,
                                                                      "inputWidth": 130,
                                                                      "inputWidth_small": 97,
                                                                    },
                                                                    "spacing": Object {
                                                                      "captionPaddingBottom": 37,
                                                                      "captionPaddingTop": 22,
                                                                      "dayPickerHorizontalPadding": 9,
                                                                      "displayTextPaddingBottom": 9,
                                                                      "displayTextPaddingBottom_small": 5,
                                                                      "displayTextPaddingHorizontal": undefined,
                                                                      "displayTextPaddingHorizontal_small": undefined,
                                                                      "displayTextPaddingLeft": 11,
                                                                      "displayTextPaddingLeft_small": 7,
                                                                      "displayTextPaddingRight": 11,
                                                                      "displayTextPaddingRight_small": 7,
                                                                      "displayTextPaddingTop": 11,
                                                                      "displayTextPaddingTop_small": 7,
                                                                      "displayTextPaddingVertical": undefined,
                                                                      "displayTextPaddingVertical_small": undefined,
                                                                      "inputPadding": 0,
                                                                    },
                                                                    "zIndex": 0,
                                                                  },
                                                                }
                                                              }
                                                              verticalSpacing={22}
                                                            >
                                                              <div
                                                                className="DateInput DateInput_1"
                                                              >
                                                                <input
                                                                  aria-describedby="DateInput__screen-reader-message-campaign-activities_Overall Engagement_endDate"
                                                                  aria-label="End Date"
                                                                  autoComplete="off"
                                                                  className="DateInput_input DateInput_input_1"
                                                                  disabled={false}
                                                                  id="campaign-activities_Overall Engagement_endDate"
                                                                  name="campaign-activities_Overall Engagement_endDate"
                                                                  onChange={[Function]}
                                                                  onFocus={[Function]}
                                                                  onKeyDown={[Function]}
                                                                  placeholder="End Date"
                                                                  readOnly={false}
                                                                  required={false}
                                                                  type="text"
                                                                  value="May 4, 2025"
                                                                />
                                                                <p
                                                                  className="DateInput_screenReaderMessage DateInput_screenReaderMessage_1"
                                                                  id="DateInput__screen-reader-message-campaign-activities_Overall Engagement_endDate"
                                                                >
                                                                  Press the down arrow key to interact with the calendar and
  select a date. Press the question mark key to get the keyboard shortcuts for changing dates.
                                                                </p>
                                                              </div>
                                                            </DateInput>
                                                          </withStyles(DateInput)>
                                                        </div>
                                                      </DateRangePickerInput>
                                                    </withStyles(DateRangePickerInput)>
                                                  </DateRangePickerInputController>
                                                </div>
                                              </OutsideClickHandler>
                                            </div>
                                          </DateRangePicker>
                                        </withStyles(DateRangePicker)>
                                        <div
                                          className="date-range__value"
                                        >
                                          <span
                                            className="svg-inline--fa fa fa-calendar-alt cursor-pointer f-14"
                                            onClick={[Function]}
                                            style={
                                              Object {
                                                "color": "#006DEE",
                                              }
                                            }
                                          />
                                          <div
                                            className="date-value"
                                          >
                                            <span
                                              className="f-12 align-self-center"
                                            >
                                              Apr 28, 2025
                                            </span>
                                            <span
                                              className="f-12"
                                            >
                                               - 
                                            </span>
                                            <span
                                              className="f-12 align-self-center"
                                            >
                                              May 4, 2025
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </DateRangePickerComponent>
                                  </Connect(DateRangePickerComponent)>
                                  <div
                                    className="cursor-pointer ml-2"
                                    onClick={[Function]}
                                  >
                                    <i
                                      className="far fa-expand"
                                    />
                                  </div>
                                </div>
                              </div>
                              <LineChartReport
                                chartStyles={
                                  Object {
                                    "brush": Object {
                                      "endPoint": 7,
                                      "height": 20,
                                    },
                                    "fontSize": Object {
                                      "axis": "0.8rem",
                                      "yAxisLabel": "0.75rem",
                                    },
                                    "height": 300,
                                    "interval": "auto",
                                    "legendClassName": "f-12",
                                    "minHeight": 320,
                                    "point": Object {
                                      "radius": 4,
                                      "strokeWidth": 1,
                                    },
                                  }
                                }
                                data={
                                  Array [
                                    Object {
                                      "color": "#7856FF",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Sent",
                                      "value": 1000,
                                    },
                                    Object {
                                      "color": "#7856FF",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Delivered",
                                      "value": 800,
                                    },
                                    Object {
                                      "color": "#7856FF",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Read",
                                      "value": 500,
                                    },
                                    Object {
                                      "color": "#7856FF",
                                      "dimension": Array [],
                                      "id": null,
                                      "name": "Failed",
                                      "value": 200,
                                    },
                                  ]
                                }
                                dimensions={Map {}}
                                entity="campaign-activities"
                                isMultiLineChart={false}
                                numberFormat="INDIAN_NUMBER_FORMAT"
                                strokeColor="#7856FF"
                                timezone="Asia/Calcutta"
                                type="Overall Engagement"
                              >
                                <div
                                  className="line-chart__report position-relative"
                                  key="campaign-activities_Overall Engagement"
                                >
                                  <ForwardRef
                                    minHeight={320}
                                  >
                                    <ResizeDetector
                                      handleHeight={true}
                                      handleWidth={true}
                                      onResize={[Function]}
                                      refreshRate={0}
                                      targetRef={
                                        Object {
                                          "current": <div
                                            class="recharts-responsive-container"
                                            style="width: 100%; height: 100%; min-width: 0; min-height: 320px;"
                                          />,
                                        }
                                      }
                                    >
                                      <div
                                        className="recharts-responsive-container"
                                        style={
                                          Object {
                                            "height": "100%",
                                            "maxHeight": undefined,
                                            "minHeight": 320,
                                            "minWidth": 0,
                                            "width": "100%",
                                          }
                                        }
                                      >
                                        <LineChart
                                          barCategoryGap="10%"
                                          barGap={4}
                                          data={
                                            Array [
                                              Object {
                                                "color": "#7856FF",
                                                "dimension": Array [],
                                                "id": null,
                                                "name": "Sent",
                                                "value": 1000,
                                              },
                                              Object {
                                                "color": "#7856FF",
                                                "dimension": Array [],
                                                "id": null,
                                                "name": "Delivered",
                                                "value": 800,
                                              },
                                              Object {
                                                "color": "#7856FF",
                                                "dimension": Array [],
                                                "id": null,
                                                "name": "Read",
                                                "value": 500,
                                              },
                                              Object {
                                                "color": "#7856FF",
                                                "dimension": Array [],
                                                "id": null,
                                                "name": "Failed",
                                                "value": 200,
                                              },
                                            ]
                                          }
                                          height={0}
                                          layout="horizontal"
                                          margin={
                                            Object {
                                              "bottom": 5,
                                              "left": 3,
                                              "right": 1,
                                              "top": 5,
                                            }
                                          }
                                          reverseStackOrder={false}
                                          stackOffset="none"
                                          syncMethod="index"
                                          width={0}
                                        />
                                      </div>
                                    </ResizeDetector>
                                  </ForwardRef>
                                </div>
                              </LineChartReport>
                            </div>
                          </CampaignActivityLineChart>
                        </Component>
                      </WithAbortController>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className="w-35"
              >
                <Connect(CampaignRelatedFormValues)
                  entity="campaign-activities"
                  formValues={
                    Object {
                      "actualExpense": Object {
                        "currencyId": 400,
                        "value": 110000,
                      },
                      "bulkJobId": null,
                      "campaign": Object {
                        "id": 123,
                        "name": "campaign name",
                      },
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "createdAt": "2021-09-10T04:04:23.835Z",
                      "createdBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "endDate": "2021-09-11T04:04:23.835Z",
                      "endedAt": "2021-09-10T04:04:23.835Z",
                      "endedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "entity": Object {
                        "disabled": false,
                        "displayName": "Lead",
                        "id": "LEAD",
                        "name": "LEAD",
                        "systemDefault": true,
                      },
                      "estimatedBudget": Object {
                        "currencyId": 400,
                        "value": 100000,
                      },
                      "filters": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "id": 1,
                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                      "lastPausedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "name": "Kylas Activity",
                      "recordActions": Object {
                        "delete": true,
                        "read": true,
                        "readAll": true,
                        "update": true,
                        "updateAll": true,
                        "write": true,
                      },
                      "recordsGenerated": 0,
                      "sentTo": Object {
                        "disabled": false,
                        "displayName": "Primary Phone number",
                        "id": "PRIMARY_PHONE_NUMBER",
                        "name": "PRIMARY_PHONE_NUMBER",
                        "systemDefault": true,
                      },
                      "startDate": "2021-09-10T04:04:23.835Z",
                      "startedAt": "2021-09-10T04:04:23.835Z",
                      "startedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "status": "IN_PROGRESS",
                      "totalEngagement": 0,
                      "type": Object {
                        "disabled": false,
                        "displayName": "WhatsApp",
                        "id": "WHATSAPP",
                        "name": "WHATSAPP",
                        "systemDefault": true,
                      },
                      "updatedAt": "2021-09-10T04:04:23.835Z",
                      "updatedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "utmCampaign": "utm campaign",
                      "utmContent": "content",
                      "utmMedium": "medium",
                      "utmSource": "google",
                      "utmTerm": "term",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    }
                  }
                >
                  <CampaignRelatedFormValues
                    dateFormat="MMM D, YYYY [at] h:mm a"
                    dispatch={[Function]}
                    entity="campaign-activities"
                    entityLabelMap={
                      Object {
                        "COMPANY": Object {
                          "displayName": "Company",
                          "displayNamePlural": "Companies",
                        },
                        "CONTACT": Object {
                          "displayName": "Student",
                          "displayNamePlural": "Contacts",
                        },
                        "DEAL": Object {
                          "displayName": "Deal",
                          "displayNamePlural": "Deals",
                        },
                        "LEAD": Object {
                          "displayName": "Teacher",
                          "displayNamePlural": "Teachers",
                        },
                        "TASK": Object {
                          "displayName": "Task",
                          "displayNamePlural": "Tasks",
                        },
                        "TEAM": Object {
                          "displayName": "Team",
                          "displayNamePlural": "Teams",
                        },
                        "USER": Object {
                          "displayName": "User",
                          "displayNamePlural": "Users",
                        },
                      }
                    }
                    formValues={
                      Object {
                        "actualExpense": Object {
                          "currencyId": 400,
                          "value": 110000,
                        },
                        "bulkJobId": null,
                        "campaign": Object {
                          "id": 123,
                          "name": "campaign name",
                        },
                        "connectedAccount": Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        },
                        "createdAt": "2021-09-10T04:04:23.835Z",
                        "createdBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "endDate": "2021-09-11T04:04:23.835Z",
                        "endedAt": "2021-09-10T04:04:23.835Z",
                        "endedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "entity": Object {
                          "disabled": false,
                          "displayName": "Lead",
                          "id": "LEAD",
                          "name": "LEAD",
                          "systemDefault": true,
                        },
                        "estimatedBudget": Object {
                          "currencyId": 400,
                          "value": 100000,
                        },
                        "filters": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "id": 1,
                        "lastPausedAt": "2021-09-10T04:04:23.835Z",
                        "lastPausedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "name": "Kylas Activity",
                        "recordActions": Object {
                          "delete": true,
                          "read": true,
                          "readAll": true,
                          "update": true,
                          "updateAll": true,
                          "write": true,
                        },
                        "recordsGenerated": 0,
                        "sentTo": Object {
                          "disabled": false,
                          "displayName": "Primary Phone number",
                          "id": "PRIMARY_PHONE_NUMBER",
                          "name": "PRIMARY_PHONE_NUMBER",
                          "systemDefault": true,
                        },
                        "startDate": "2021-09-10T04:04:23.835Z",
                        "startedAt": "2021-09-10T04:04:23.835Z",
                        "startedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "status": "IN_PROGRESS",
                        "totalEngagement": 0,
                        "type": Object {
                          "disabled": false,
                          "displayName": "WhatsApp",
                          "id": "WHATSAPP",
                          "name": "WHATSAPP",
                          "systemDefault": true,
                        },
                        "updatedAt": "2021-09-10T04:04:23.835Z",
                        "updatedBy": Object {
                          "id": 3788,
                          "name": "Andrew Strauss",
                        },
                        "utmCampaign": "utm campaign",
                        "utmContent": "content",
                        "utmMedium": "medium",
                        "utmSource": "google",
                        "utmTerm": "term",
                        "whatsappTemplate": Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        },
                      }
                    }
                    timezone="Asia/Calcutta"
                  >
                    <div
                      className="campaign-related__form-values h-100"
                    >
                      <div
                        className="section"
                        key="0"
                      >
                        <div
                          className="section-name"
                        >
                          Activity Details
                        </div>
                        <div
                          className="row"
                        >
                          <div
                            className="col-6"
                            key="0"
                          >
                            <div
                              className="label"
                            >
                              ID
                            </div>
                            <div
                              className="f-14"
                            >
                              1
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="1"
                          >
                            <div
                              className="label"
                            >
                              Activity Name
                            </div>
                            <div
                              className="f-14"
                            >
                              Kylas Activity
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="2"
                          >
                            <div
                              className="label"
                            >
                              Activity Type
                            </div>
                            <div
                              className="f-14"
                            >
                              WhatsApp
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="3"
                          >
                            <div
                              className="label"
                            >
                              Status
                            </div>
                            <div
                              className="d-flex justify-content-end"
                            >
                              <div
                                className="campaign-related__status"
                                style={
                                  Object {
                                    "backgroundColor": "#FEF6E5",
                                    "border": "1px solid #B67C00",
                                    "color": "#B67C00",
                                  }
                                }
                              >
                                In Progress
                              </div>
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="4"
                          >
                            <div
                              className="label"
                            >
                              Start Date
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="5"
                          >
                            <div
                              className="label"
                            >
                              End Date
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 11, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="6"
                          >
                            <div
                              className="label"
                            >
                              Entity
                            </div>
                            <div
                              className="f-14"
                            >
                              Teacher
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="7"
                          >
                            <div
                              className="label"
                            >
                              Sent To
                            </div>
                            <div
                              className="f-14"
                            >
                              Primary Phone number
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="8"
                          >
                            <div
                              className="label"
                            >
                              Whatsapp Account
                            </div>
                            <div
                              className="f-14"
                            >
                              Whatsapp Business Account
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="9"
                          >
                            <div
                              className="label"
                            >
                              Template Name
                            </div>
                            <div
                              className="f-14"
                            >
                              <Connect(WhatsAppTemplatePreviewMode)
                                connectedAccount={
                                  Object {
                                    "id": 1,
                                    "name": "Whatsapp Business Account",
                                  }
                                }
                                referrer="Campaign Details"
                                template={
                                  Object {
                                    "id": 47,
                                    "name": "Welcome to Kylas",
                                  }
                                }
                              >
                                <WhatsAppTemplatePreviewMode
                                  connectedAccount={
                                    Object {
                                      "id": 1,
                                      "name": "Whatsapp Business Account",
                                    }
                                  }
                                  referrer="Campaign Details"
                                  template={
                                    Object {
                                      "id": 47,
                                      "name": "Welcome to Kylas",
                                    }
                                  }
                                >
                                  <div
                                    className="whatsapp-template__preview-mode"
                                  >
                                    <span
                                      className="preview-cta "
                                      onClick={[Function]}
                                    >
                                      Welcome to Kylas
                                    </span>
                                  </div>
                                </WhatsAppTemplatePreviewMode>
                              </Connect(WhatsAppTemplatePreviewMode)>
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="10"
                          >
                            <div
                              className="label"
                            >
                              Created At
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="11"
                          >
                            <div
                              className="label"
                            >
                              Created By
                            </div>
                            <div
                              className="f-14"
                            >
                              Andrew Strauss
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="12"
                          >
                            <div
                              className="label"
                            >
                              Updated At
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="13"
                          >
                            <div
                              className="label"
                            >
                              Updated By
                            </div>
                            <div
                              className="f-14"
                            >
                              Andrew Strauss
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="14"
                          >
                            <div
                              className="label"
                            >
                              Started At
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="15"
                          >
                            <div
                              className="label"
                            >
                              Started By
                            </div>
                            <div
                              className="f-14"
                            >
                              Andrew Strauss
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="16"
                          >
                            <div
                              className="label"
                            >
                              Last Paused At
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="17"
                          >
                            <div
                              className="label"
                            >
                              Last Paused By
                            </div>
                            <div
                              className="f-14"
                            >
                              Andrew Strauss
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="18"
                          >
                            <div
                              className="label"
                            >
                              Last Resumed At
                            </div>
                            <div
                              className="f-14"
                            >
                              -
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="19"
                          >
                            <div
                              className="label"
                            >
                              Last Resumed By
                            </div>
                            <div
                              className="f-14"
                            >
                              -
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="20"
                          >
                            <div
                              className="label"
                            >
                              Ended At
                            </div>
                            <div
                              className="f-14"
                            >
                              Sep 10, 2021 at 9:34 am
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="21"
                          >
                            <div
                              className="label"
                            >
                              Ended By
                            </div>
                            <div
                              className="f-14"
                            >
                              Andrew Strauss
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        className="section"
                        key="1"
                      >
                        <div
                          className="section-name"
                        >
                          UTM Details
                        </div>
                        <div
                          className="row"
                        >
                          <div
                            className="col-6"
                            key="0"
                          >
                            <div
                              className="label"
                            >
                              UTM Campaign
                            </div>
                            <div
                              className="f-14"
                            >
                              utm campaign
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="1"
                          >
                            <div
                              className="label"
                            >
                              UTM Source
                            </div>
                            <div
                              className="f-14"
                            >
                              google
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="2"
                          >
                            <div
                              className="label"
                            >
                              UTM Medium
                            </div>
                            <div
                              className="f-14"
                            >
                              medium
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="3"
                          >
                            <div
                              className="label"
                            >
                              UTM Content
                            </div>
                            <div
                              className="f-14"
                            >
                              content
                            </div>
                          </div>
                          <div
                            className="col-6"
                            key="4"
                          >
                            <div
                              className="label"
                            >
                              UTM Term
                            </div>
                            <div
                              className="f-14"
                            >
                              term
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CampaignRelatedFormValues>
                </Connect(CampaignRelatedFormValues)>
              </div>
            </div>
          </div>
        </CampaignActivityOverview>
      </Connect(CampaignActivityOverview)>
    </Router>
  </BrowserRouter>
</Provider>
`;
