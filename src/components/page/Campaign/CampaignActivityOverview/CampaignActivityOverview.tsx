import * as React from 'react';
import { connect } from 'react-redux';
import { toastr } from 'react-redux-toastr';

import * as CampaignActivityStart from '../../../../assets/images/campaign-start.svg';

import { StateInterface } from '../../../../store/store';
import { EntityLabelMap } from '../../login/models/login';
import { PermissionAction } from '../../../../utils/models/PermissionModel';
import { CampaignActionType, CampaignActivity, CampaignActivityStatus, CampaignAnalyticsType } from '../model';

import { isBlank } from '../../../../utils/globalUtil';
import { entities, ReferrerFlow } from '../../../../utils/constants';
import { showErrorToast } from '../../../../middlewares/errorToastr';
import { getInitialFormLayoutV3Values } from '../../formLayout/utils';
import { DELETE, isActionAllowed, UPDATE } from '../../../../utils/permissionUtil';
import { capitalizeLabel, getEntityLabelPlural, routeToEntity } from '../../../../utils/entityUtils';
import { getAvailableActionsForCampaignActivityAsPerStatus, getCampaignActionName, getConfirmModalPropsAsPerCampaignActionType, isCampaignActivityDisabled, shouldShowRecipientStatus } from '../utils';

import { deleteCampaignActivity, runCampaignActivity } from '../service';

import { DropdownIcon, EditIcon } from '../../../shared/Icons/GlobalIcons';
import ConfirmModal from '../../../shared/Modal/ConfirmModal';
import MultiActionModal from '../../../shared/Input/MultiActionModal';
import BarChartReport from '../../../shared/BarChartReport/BarChartReport';
import CampaignRelatedFormValues from '../CampaignRelatedFormValues/CampaignRelatedFormValues';
import CampaignActivitySummary from '../CampaignAnalytics/CampaignActivitySummary/CampaignActivitySummary';
import CampaignActivityLineChart from '../CampaignAnalytics/CampaignActivityLineChart/CampaignActivityLineChart';
import CampaignActivityFunnelChart from '../CampaignAnalytics/CampaignActivityFunnelChart/CampaignActivityFunnelChart';

import './_campaign-activity-overview.scss';

interface StoreProps {
  timezone: string;
  numberFormat: string;
  entityLabelMap: EntityLabelMap;
}

interface OwnProps {
  history: any;
  activity: CampaignActivity;
  recordActions: PermissionAction;
  fetchData: () => void;
}

type Props = StoreProps & OwnProps;

export const CampaignActivityOverview: React.FC<Props> = ({ history, activity, fetchData, timezone, numberFormat, recordActions, entityLabelMap }) => {
  const [showModalFor, toggleModalFor] = React.useState<string>(null);
  const {
    id,
    name,
    status,
    entity,
    endDate,
    campaign,
    bulkJobId,
    startDate,
    actualExpense,
    estimatedBudget,
    totalEngagement,
    recordsGenerated
  } = activity;

  const getMultiActionModalOptions = () => {
    const options = [];
    const canDeleteCampaignActivity = isActionAllowed([DELETE], recordActions);
    const canEditCampaignActivity = isActionAllowed([UPDATE], recordActions);

    if(canEditCampaignActivity) {
      const requiredActions = getAvailableActionsForCampaignActivityAsPerStatus(status);

      requiredActions.forEach((action: CampaignActionType) => {
        options.push({
          label: (action === CampaignActionType.COMPLETE) ? 'Mark as complete' : capitalizeLabel(action),
          action: () => {
            if(status === CampaignActivityStatus.PROCESSED) {
              runParticularCampaignActivity(action);

              return;
            }

            toggleModalFor(action);
          }
        });
      });
    }

    if(canDeleteCampaignActivity) {
      const shouldDisabled = isCampaignActivityDisabled(status);

      options.push({
        label: 'Delete',
        isDisabled: shouldDisabled,
        action: () => toggleModalFor('delete_campaign_activity'),
        tooltip: shouldDisabled ? 'This activity has already been started and cannot be deleted' : ''
      });
    }

    return options;
  };

  const deleteDraftCampaignActivity = () => {
    deleteCampaignActivity(id, campaign.id, history)
      .then(() => {
        fetchData();
        toastr.success('Success', `${capitalizeLabel(routeToEntity(entities.CAMPAIGN_ACTIVITIES))} Deleted`);
      })
      .catch(err => showErrorToast(err));
  };

  const runParticularCampaignActivity = (action: CampaignActionType) => {
    runCampaignActivity(id, campaign.id, action, history)
      .then(() => {
        fetchData();
        toastr.success('Success', `Activity ${capitalizeLabel(getCampaignActionName(action))}`);
      })
      .catch(err => showErrorToast(err));
  };

  const renderModalPopup = () => {
    if(
      [
        'delete_campaign_activity',
        CampaignActionType.START,
        CampaignActionType.PAUSE,
        CampaignActionType.RESUME,
        CampaignActionType.COMPLETE
      ].includes(showModalFor)
    ) {
      const { title, message, confirmBtn } = getConfirmModalPropsAsPerCampaignActionType(entities.CAMPAIGN_ACTIVITIES, name, showModalFor);

      return (
        <ConfirmModal
          show
          title={title}
          message={message}
          confirmBtnLabel={confirmBtn.label}
          onCancel={() => toggleModalFor(null)}
          confirmBtnClass={confirmBtn.className}
          onConfirm={() => {
            toggleModalFor(null);

            if(showModalFor === 'delete_campaign_activity') {
              deleteDraftCampaignActivity();
              return;
            }

            runParticularCampaignActivity(showModalFor as CampaignActionType);
          }}
        />
      );
    }
  };

  return (
    <div className="campaign-activity__overview">
      <div className="d-flex justify-content-between">
        <strong className="align-self-center">Activity Overview</strong>

        <div className="actions">
          { shouldShowRecipientStatus(status) &&
            <a
              className="recipient-status__link link-primary"
              onClick={(e) => {
                e.stopPropagation();

                const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
                const url = `/sales/campaigns/activities/${id}/recipient-status/list?name=${encodeURIComponent(name)}`;

                shouldOpenInNewTab ? window.open(url, '_blank') : history.push(url);
              }}
            >
              View Recipient Status
            </a>
          }

          { !isBlank(bulkJobId) &&
            <a
              className="bulk-job__link link-primary"
              onClick={(e) => {
                e.stopPropagation();

                const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
                const url = `/setup/data-management/bulk-jobs/list?id=${bulkJobId}`;

                shouldOpenInNewTab ? window.open(url, '_blank') : history.push(url);
              }}
            >
              View Bulk Job
            </a>
          }

          { isActionAllowed([UPDATE], recordActions) &&
            <div
              className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
              onClick={() => history.push(`/sales/${entities.CAMPAIGNS}/view/${campaign.id}`, { activityId: id, referrer: ReferrerFlow.CAMPAIGN_DETAILS })}
            >
              <EditIcon />
            </div>
          }

          { (getMultiActionModalOptions().length > 0) &&
            <MultiActionModal
              icon={<DropdownIcon />}
              className="btn-down-arrow btn-primary"
              options={getMultiActionModalOptions()}
            />
          }
        </div>
      </div>

      <div className="d-flex" style={{ gap: '1.5rem', marginTop: '1.25rem' }}>
        <div className="campaign-activity__report w-65">
          { !isCampaignActivityDisabled(status) ?
              <div className="campaign-activity__not-started">
                <div className="d-flex flex-column align-items-center m-auto">
                  <img className="empty__report-data" src={`${CampaignActivityStart}`} alt="Start Activity" />

                  <div className="mt-1">Your activity is still in draft mode</div>

                  <div className="mt-2 add-instruction">To begin tracking performance and viewing analytics, start the activity or complete its setup</div>
                </div>
              </div>
            :
              <div className="campaign-activity__analytics">
                <CampaignActivitySummary
                  actualExpense={actualExpense}
                  estimatedBudget={estimatedBudget}
                  totalEngagement={totalEngagement}
                  recordsGenerated={recordsGenerated}
                  entityName={getEntityLabelPlural(entityLabelMap, entity)}
                />

                <div className="row">
                  <div className="col-6">
                    <CampaignActivityFunnelChart
                      key={id}
                      activityId={id}
                      history={history}
                      endDate={endDate}
                      timezone={timezone}
                      startDate={startDate}
                      campaignId={campaign.id}
                      numberFormat={numberFormat}
                      entity={entities.CAMPAIGN_ACTIVITIES}
                      type={CampaignAnalyticsType.RESPONSE_COUNT}
                    />
                  </div>

                  <div className="col-6">
                    <BarChartReport
                      dateRange={{ startDate, endDate }}
                      entity={entities.CAMPAIGN_ACTIVITIES}
                      currencyId={estimatedBudget.currencyId}
                      type={CampaignAnalyticsType.BUDGET_VS_ACTUAL_EXPENSE}
                      reportData={[
                        {
                          id: null,
                          name: 'Budget',
                          dimension: [],
                          color: '#006DEE',
                          value: estimatedBudget.value
                        },
                        {
                          id: null,
                          name: 'Actual',
                          dimension: [],
                          color: '#00780E',
                          value: actualExpense.value
                        }
                      ]}
                    />
                  </div>
                </div>

                <div className="row">
                  <div className="col-12">
                    <CampaignActivityLineChart
                      key={id}
                      activityId={id}
                      history={history}
                      timezone={timezone}
                      strokeColor="#7856FF"
                      isMultiLineChart={false}
                      campaignId={campaign.id}
                      numberFormat={numberFormat}
                      multiDimensionToRender={null}
                      entity={entities.CAMPAIGN_ACTIVITIES}
                      type={CampaignAnalyticsType.OVERALL_ENGAGEMENT}
                      />
                  </div>
                </div>
              </div>
          }
        </div>

        <div className="w-35">
          <CampaignRelatedFormValues entity={entities.CAMPAIGN_ACTIVITIES} formValues={getInitialFormLayoutV3Values(entities.CAMPAIGN_ACTIVITIES, activity)}  />
        </div>
      </div>

      { !isBlank(showModalFor) && renderModalPopup() }
    </div>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  entityLabelMap: state.loginForm.entityLabelMap,
  timezone: state.loginForm.userPreferences.timezone,
  numberFormat: state.loginForm.userPreferences.numberFormat
});

export default connect(mapStateToProps, null)(CampaignActivityOverview);
