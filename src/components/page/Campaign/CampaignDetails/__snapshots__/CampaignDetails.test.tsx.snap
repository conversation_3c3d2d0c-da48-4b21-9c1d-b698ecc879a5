// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignDetails component should render component 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <Connect(WithAbortController)
        abortSignal={AbortSignal {}}
        data={
          Object {
            "activities": Array [
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 1,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 2,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
            ],
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "description": "Kylas Campaign for Diwali festival",
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "id": 1,
            "name": "Kylas Campaign",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
        error={null}
        fetchData={[MockFunction]}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        loading={false}
        match={
          Object {
            "params": Object {
              "campaignId": "1",
            },
          }
        }
      >
        <WithAbortController
          abortSignal={AbortSignal {}}
          data={
            Object {
              "activities": Array [
                Object {
                  "actualExpense": Object {
                    "currencyId": 400,
                    "value": 110000,
                  },
                  "bulkJobId": null,
                  "campaign": Object {
                    "id": 123,
                    "name": "campaign name",
                  },
                  "createdAt": "2021-09-10T04:04:23.835Z",
                  "createdBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "endedAt": "2021-09-10T04:04:23.835Z",
                  "endedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "entity": "LEAD",
                  "estimatedBudget": Object {
                    "currencyId": 400,
                    "value": 100000,
                  },
                  "filters": Object {
                    "jsonRule": Object {
                      "condition": "AND",
                      "rules": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "valid": true,
                    },
                  },
                  "id": 1,
                  "lastPausedAt": "2021-09-10T04:04:23.835Z",
                  "lastPausedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "name": "Kylas Activity",
                  "payload": Object {
                    "connectedAccount": Object {
                      "id": 1,
                      "name": "Whatsapp Business Account",
                    },
                    "sentTo": "PRIMARY_PHONE_NUMBER",
                    "type": "WHATSAPP",
                    "whatsappTemplate": Object {
                      "id": 47,
                      "name": "Welcome to Kylas",
                    },
                  },
                  "recordActions": Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  },
                  "recordsGenerated": 0,
                  "startDate": "2021-09-10T04:04:23.835Z",
                  "startedAt": "2021-09-10T04:04:23.835Z",
                  "startedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "status": "DRAFT",
                  "totalEngagement": 0,
                  "updatedAt": "2021-09-10T04:04:23.835Z",
                  "updatedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "utmCampaign": "utm campaign",
                  "utmContent": "content",
                  "utmMedium": "medium",
                  "utmSource": "google",
                  "utmTerm": "term",
                },
                Object {
                  "actualExpense": Object {
                    "currencyId": 400,
                    "value": 110000,
                  },
                  "bulkJobId": null,
                  "campaign": Object {
                    "id": 123,
                    "name": "campaign name",
                  },
                  "createdAt": "2021-09-10T04:04:23.835Z",
                  "createdBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "endedAt": "2021-09-10T04:04:23.835Z",
                  "endedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "entity": "LEAD",
                  "estimatedBudget": Object {
                    "currencyId": 400,
                    "value": 100000,
                  },
                  "filters": Object {
                    "jsonRule": Object {
                      "condition": "AND",
                      "rules": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "valid": true,
                    },
                  },
                  "id": 2,
                  "lastPausedAt": "2021-09-10T04:04:23.835Z",
                  "lastPausedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "name": "Kylas Activity",
                  "payload": Object {
                    "connectedAccount": Object {
                      "id": 1,
                      "name": "Whatsapp Business Account",
                    },
                    "sentTo": "PRIMARY_PHONE_NUMBER",
                    "type": "WHATSAPP",
                    "whatsappTemplate": Object {
                      "id": 47,
                      "name": "Welcome to Kylas",
                    },
                  },
                  "recordActions": Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  },
                  "recordsGenerated": 0,
                  "startDate": "2021-09-10T04:04:23.835Z",
                  "startedAt": "2021-09-10T04:04:23.835Z",
                  "startedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "status": "DRAFT",
                  "totalEngagement": 0,
                  "updatedAt": "2021-09-10T04:04:23.835Z",
                  "updatedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "utmCampaign": "utm campaign",
                  "utmContent": "content",
                  "utmMedium": "medium",
                  "utmSource": "google",
                  "utmTerm": "term",
                },
              ],
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "description": "Kylas Campaign for Diwali festival",
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "id": 1,
              "name": "Kylas Campaign",
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "DRAFT",
              "totalEngagement": 0,
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
            }
          }
          dispatch={[Function]}
          error={null}
          fetchData={[MockFunction]}
          history={
            Object {
              "push": [MockFunction],
            }
          }
          isTenantUser={true}
          loading={false}
          match={
            Object {
              "params": Object {
                "campaignId": "1",
              },
            }
          }
          planName="embark"
        >
          <Component
            abortSignal={AbortSignal {}}
            data={
              Object {
                "activities": Array [
                  Object {
                    "actualExpense": Object {
                      "currencyId": 400,
                      "value": 110000,
                    },
                    "bulkJobId": null,
                    "campaign": Object {
                      "id": 123,
                      "name": "campaign name",
                    },
                    "createdAt": "2021-09-10T04:04:23.835Z",
                    "createdBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "endDate": "2021-09-11T04:04:23.835Z",
                    "endedAt": "2021-09-10T04:04:23.835Z",
                    "endedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "entity": "LEAD",
                    "estimatedBudget": Object {
                      "currencyId": 400,
                      "value": 100000,
                    },
                    "filters": Object {
                      "jsonRule": Object {
                        "condition": "AND",
                        "rules": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "valid": true,
                      },
                    },
                    "id": 1,
                    "lastPausedAt": "2021-09-10T04:04:23.835Z",
                    "lastPausedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "name": "Kylas Activity",
                    "payload": Object {
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "sentTo": "PRIMARY_PHONE_NUMBER",
                      "type": "WHATSAPP",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    },
                    "recordActions": Object {
                      "delete": true,
                      "read": true,
                      "readAll": true,
                      "update": true,
                      "updateAll": true,
                      "write": true,
                    },
                    "recordsGenerated": 0,
                    "startDate": "2021-09-10T04:04:23.835Z",
                    "startedAt": "2021-09-10T04:04:23.835Z",
                    "startedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "status": "DRAFT",
                    "totalEngagement": 0,
                    "updatedAt": "2021-09-10T04:04:23.835Z",
                    "updatedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "utmCampaign": "utm campaign",
                    "utmContent": "content",
                    "utmMedium": "medium",
                    "utmSource": "google",
                    "utmTerm": "term",
                  },
                  Object {
                    "actualExpense": Object {
                      "currencyId": 400,
                      "value": 110000,
                    },
                    "bulkJobId": null,
                    "campaign": Object {
                      "id": 123,
                      "name": "campaign name",
                    },
                    "createdAt": "2021-09-10T04:04:23.835Z",
                    "createdBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "endDate": "2021-09-11T04:04:23.835Z",
                    "endedAt": "2021-09-10T04:04:23.835Z",
                    "endedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "entity": "LEAD",
                    "estimatedBudget": Object {
                      "currencyId": 400,
                      "value": 100000,
                    },
                    "filters": Object {
                      "jsonRule": Object {
                        "condition": "AND",
                        "rules": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "valid": true,
                      },
                    },
                    "id": 2,
                    "lastPausedAt": "2021-09-10T04:04:23.835Z",
                    "lastPausedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "name": "Kylas Activity",
                    "payload": Object {
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "sentTo": "PRIMARY_PHONE_NUMBER",
                      "type": "WHATSAPP",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    },
                    "recordActions": Object {
                      "delete": true,
                      "read": true,
                      "readAll": true,
                      "update": true,
                      "updateAll": true,
                      "write": true,
                    },
                    "recordsGenerated": 0,
                    "startDate": "2021-09-10T04:04:23.835Z",
                    "startedAt": "2021-09-10T04:04:23.835Z",
                    "startedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "status": "DRAFT",
                    "totalEngagement": 0,
                    "updatedAt": "2021-09-10T04:04:23.835Z",
                    "updatedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "utmCampaign": "utm campaign",
                    "utmContent": "content",
                    "utmMedium": "medium",
                    "utmSource": "google",
                    "utmTerm": "term",
                  },
                ],
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "description": "Kylas Campaign for Diwali festival",
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "id": 1,
                "name": "Kylas Campaign",
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              }
            }
            dispatch={[Function]}
            error={null}
            fetchData={[MockFunction]}
            history={
              Object {
                "push": [MockFunction],
              }
            }
            isTenantUser={true}
            loading={false}
            match={
              Object {
                "params": Object {
                  "campaignId": "1",
                },
              }
            }
            planName="embark"
          >
            <CampaignDetails
              abortSignal={AbortSignal {}}
              data={
                Object {
                  "activities": Array [
                    Object {
                      "actualExpense": Object {
                        "currencyId": 400,
                        "value": 110000,
                      },
                      "bulkJobId": null,
                      "campaign": Object {
                        "id": 123,
                        "name": "campaign name",
                      },
                      "createdAt": "2021-09-10T04:04:23.835Z",
                      "createdBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "endDate": "2021-09-11T04:04:23.835Z",
                      "endedAt": "2021-09-10T04:04:23.835Z",
                      "endedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "entity": "LEAD",
                      "estimatedBudget": Object {
                        "currencyId": 400,
                        "value": 100000,
                      },
                      "filters": Object {
                        "jsonRule": Object {
                          "condition": "AND",
                          "rules": Array [
                            Object {
                              "field": "id",
                              "id": "id",
                              "operator": "in",
                              "type": "long",
                              "value": "553092,553052",
                            },
                          ],
                          "valid": true,
                        },
                      },
                      "id": 1,
                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                      "lastPausedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "name": "Kylas Activity",
                      "payload": Object {
                        "connectedAccount": Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        },
                        "sentTo": "PRIMARY_PHONE_NUMBER",
                        "type": "WHATSAPP",
                        "whatsappTemplate": Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        },
                      },
                      "recordActions": Object {
                        "delete": true,
                        "read": true,
                        "readAll": true,
                        "update": true,
                        "updateAll": true,
                        "write": true,
                      },
                      "recordsGenerated": 0,
                      "startDate": "2021-09-10T04:04:23.835Z",
                      "startedAt": "2021-09-10T04:04:23.835Z",
                      "startedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "status": "DRAFT",
                      "totalEngagement": 0,
                      "updatedAt": "2021-09-10T04:04:23.835Z",
                      "updatedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "utmCampaign": "utm campaign",
                      "utmContent": "content",
                      "utmMedium": "medium",
                      "utmSource": "google",
                      "utmTerm": "term",
                    },
                    Object {
                      "actualExpense": Object {
                        "currencyId": 400,
                        "value": 110000,
                      },
                      "bulkJobId": null,
                      "campaign": Object {
                        "id": 123,
                        "name": "campaign name",
                      },
                      "createdAt": "2021-09-10T04:04:23.835Z",
                      "createdBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "endDate": "2021-09-11T04:04:23.835Z",
                      "endedAt": "2021-09-10T04:04:23.835Z",
                      "endedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "entity": "LEAD",
                      "estimatedBudget": Object {
                        "currencyId": 400,
                        "value": 100000,
                      },
                      "filters": Object {
                        "jsonRule": Object {
                          "condition": "AND",
                          "rules": Array [
                            Object {
                              "field": "id",
                              "id": "id",
                              "operator": "in",
                              "type": "long",
                              "value": "553092,553052",
                            },
                          ],
                          "valid": true,
                        },
                      },
                      "id": 2,
                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                      "lastPausedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "name": "Kylas Activity",
                      "payload": Object {
                        "connectedAccount": Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        },
                        "sentTo": "PRIMARY_PHONE_NUMBER",
                        "type": "WHATSAPP",
                        "whatsappTemplate": Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        },
                      },
                      "recordActions": Object {
                        "delete": true,
                        "read": true,
                        "readAll": true,
                        "update": true,
                        "updateAll": true,
                        "write": true,
                      },
                      "recordsGenerated": 0,
                      "startDate": "2021-09-10T04:04:23.835Z",
                      "startedAt": "2021-09-10T04:04:23.835Z",
                      "startedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "status": "DRAFT",
                      "totalEngagement": 0,
                      "updatedAt": "2021-09-10T04:04:23.835Z",
                      "updatedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "utmCampaign": "utm campaign",
                      "utmContent": "content",
                      "utmMedium": "medium",
                      "utmSource": "google",
                      "utmTerm": "term",
                    },
                  ],
                  "actualExpense": Object {
                    "currencyId": 400,
                    "value": 110000,
                  },
                  "createdAt": "2021-09-10T04:04:23.835Z",
                  "createdBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "description": "Kylas Campaign for Diwali festival",
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "endedAt": "2021-09-10T04:04:23.835Z",
                  "endedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "estimatedBudget": Object {
                    "currencyId": 400,
                    "value": 100000,
                  },
                  "id": 1,
                  "name": "Kylas Campaign",
                  "recordActions": Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  },
                  "recordsGenerated": 0,
                  "startDate": "2021-09-10T04:04:23.835Z",
                  "startedAt": "2021-09-10T04:04:23.835Z",
                  "startedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "status": "DRAFT",
                  "totalEngagement": 0,
                  "updatedAt": "2021-09-10T04:04:23.835Z",
                  "updatedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "utmCampaign": "utm campaign",
                  "utmContent": "content",
                  "utmMedium": "medium",
                  "utmSource": "google",
                  "utmTerm": "term",
                }
              }
              dispatch={[Function]}
              error={null}
              fetchData={[Function]}
              history={
                Object {
                  "push": [MockFunction],
                }
              }
              isTenantUser={true}
              loading={false}
              match={
                Object {
                  "params": Object {
                    "campaignId": "1",
                  },
                }
              }
              planName="embark"
            >
              <Connect(withRouter(SalesLayout))>
                <withRouter(SalesLayout)
                  loginForm={
                    Object {
                      "currentUserId": 123,
                      "currentUserName": "sant",
                      "entityLabelMap": Object {
                        "COMPANY": Object {
                          "displayName": "Company",
                          "displayNamePlural": "Companies",
                        },
                        "CONTACT": Object {
                          "displayName": "Student",
                          "displayNamePlural": "Contacts",
                        },
                        "DEAL": Object {
                          "displayName": "Deal",
                          "displayNamePlural": "Deals",
                        },
                        "LEAD": Object {
                          "displayName": "Teacher",
                          "displayNamePlural": "Teachers",
                        },
                        "TASK": Object {
                          "displayName": "Task",
                          "displayNamePlural": "Tasks",
                        },
                        "TEAM": Object {
                          "displayName": "Team",
                          "displayNamePlural": "Teams",
                        },
                        "USER": Object {
                          "displayName": "User",
                          "displayNamePlural": "Users",
                        },
                      },
                      "isLogin": true,
                      "loading": false,
                      "token": "invalid",
                      "userPreferences": Object {
                        "dateFormat": "MMM D, YYYY [at] h:mm a",
                        "numberFormat": "INDIAN_NUMBER_FORMAT",
                        "timezone": "Asia/Calcutta",
                      },
                    }
                  }
                >
                  <Route>
                    <SalesLayout
                      history={
                        Object {
                          "action": "POP",
                          "block": [Function],
                          "createHref": [Function],
                          "go": [Function],
                          "goBack": [Function],
                          "goForward": [Function],
                          "length": 1,
                          "listen": [Function],
                          "location": Object {
                            "hash": "",
                            "pathname": "/",
                            "search": "",
                            "state": undefined,
                          },
                          "push": [Function],
                          "replace": [Function],
                        }
                      }
                      location={
                        Object {
                          "hash": "",
                          "pathname": "/",
                          "search": "",
                          "state": undefined,
                        }
                      }
                      loginForm={
                        Object {
                          "currentUserId": 123,
                          "currentUserName": "sant",
                          "entityLabelMap": Object {
                            "COMPANY": Object {
                              "displayName": "Company",
                              "displayNamePlural": "Companies",
                            },
                            "CONTACT": Object {
                              "displayName": "Student",
                              "displayNamePlural": "Contacts",
                            },
                            "DEAL": Object {
                              "displayName": "Deal",
                              "displayNamePlural": "Deals",
                            },
                            "LEAD": Object {
                              "displayName": "Teacher",
                              "displayNamePlural": "Teachers",
                            },
                            "TASK": Object {
                              "displayName": "Task",
                              "displayNamePlural": "Tasks",
                            },
                            "TEAM": Object {
                              "displayName": "Team",
                              "displayNamePlural": "Teams",
                            },
                            "USER": Object {
                              "displayName": "User",
                              "displayNamePlural": "Users",
                            },
                          },
                          "isLogin": true,
                          "loading": false,
                          "token": "invalid",
                          "userPreferences": Object {
                            "dateFormat": "MMM D, YYYY [at] h:mm a",
                            "numberFormat": "INDIAN_NUMBER_FORMAT",
                            "timezone": "Asia/Calcutta",
                          },
                        }
                      }
                      match={
                        Object {
                          "isExact": true,
                          "params": Object {},
                          "path": "/",
                          "url": "/",
                        }
                      }
                    >
                      <div
                        className="d-flex flex-column h-100"
                      >
                        <Connect(VerticalNavbar)
                          history={
                            Object {
                              "action": "POP",
                              "block": [Function],
                              "createHref": [Function],
                              "go": [Function],
                              "goBack": [Function],
                              "goForward": [Function],
                              "length": 1,
                              "listen": [Function],
                              "location": Object {
                                "hash": "",
                                "pathname": "/",
                                "search": "",
                                "state": undefined,
                              },
                              "push": [Function],
                              "replace": [Function],
                            }
                          }
                        >
                          <VerticalNavbar
                            headerList={
                              Object {
                                "Headers": Object {
                                  "Marketplace": Array [
                                    Object {
                                      "displayName": "All Apps",
                                      "items": Array [],
                                      "name": "all-apps",
                                      "pluralDisplayName": "All Apps",
                                      "route": "/marketplace",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Installed Apps",
                                      "items": Array [],
                                      "name": "installed-apps",
                                      "pluralDisplayName": "Installed Apps",
                                      "route": "/marketplace/installed-apps/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Manage My Apps",
                                      "items": Array [],
                                      "name": "manage-my-apps",
                                      "pluralDisplayName": "Manage My Apps",
                                      "route": "/marketplace/manage-my-apps/list",
                                      "type": "link",
                                    },
                                  ],
                                  "Sales": Array [
                                    Object {
                                      "displayName": "Dashboard",
                                      "items": Array [],
                                      "name": "sales-dashboard",
                                      "pluralDisplayName": "Dashboard",
                                      "route": "/sales/home",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Lead",
                                      "items": Array [],
                                      "name": "lead",
                                      "pluralDisplayName": "Leads",
                                      "route": "/sales/leads/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Deal",
                                      "items": Array [],
                                      "name": "deal",
                                      "pluralDisplayName": "Deals",
                                      "route": "/sales/deals/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Contact",
                                      "items": Array [],
                                      "name": "contact",
                                      "pluralDisplayName": "Contacts",
                                      "route": "/sales/contacts/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Company",
                                      "items": Array [],
                                      "name": "company",
                                      "pluralDisplayName": "Companies",
                                      "route": "/sales/companies/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Task",
                                      "items": Array [],
                                      "name": "task",
                                      "pluralDisplayName": "Tasks",
                                      "route": "/sales/tasks/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Meeting",
                                      "items": Array [],
                                      "name": "meeting",
                                      "pluralDisplayName": "meetings",
                                      "route": "/sales/meetings/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Communication",
                                      "items": Array [
                                        Object {
                                          "displayName": "Call Log",
                                          "items": Array [],
                                          "name": "call",
                                          "pluralDisplayName": "Call Logs",
                                          "route": "/sales/calls/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Email",
                                          "items": Array [],
                                          "name": "email",
                                          "pluralDisplayName": "Emails",
                                          "route": "/sales/emails/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "WhatsApp",
                                          "items": Array [],
                                          "name": "whatsapp",
                                          "pluralDisplayName": "WhatsApp",
                                          "route": "/sales/whatsapp/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "communication",
                                      "pluralDisplayName": "Communication",
                                      "route": "/sales/communication/list",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Campaigns",
                                      "items": Array [],
                                      "name": "campaign",
                                      "pluralDisplayName": "Campaigns",
                                      "route": "/sales/campaigns/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Reports",
                                      "items": Array [],
                                      "name": "report",
                                      "pluralDisplayName": "reports",
                                      "route": "/sales/reports/list",
                                      "type": "link",
                                    },
                                  ],
                                  "Setup": Array [
                                    Object {
                                      "displayName": "Settings Dashboard",
                                      "items": Array [],
                                      "name": "setup-dashboard",
                                      "pluralDisplayName": "Settings Dashboard",
                                      "route": "/setup",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Account Settings",
                                      "items": Array [
                                        Object {
                                          "displayName": "General",
                                          "items": Array [],
                                          "name": "general",
                                          "pluralDisplayName": "General",
                                          "route": "/setup/account-management/general",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "My Profile",
                                          "items": Array [],
                                          "name": "myProfile",
                                          "pluralDisplayName": "My Profile",
                                          "route": "/setup/account-management/me",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Currencies",
                                          "items": Array [],
                                          "name": "currencies",
                                          "pluralDisplayName": "Currencies",
                                          "route": "/setup/account-management/currencies/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Security",
                                          "items": Array [],
                                          "name": "security",
                                          "pluralDisplayName": "Securities",
                                          "route": "/setup/account-management/security/ip-configurations/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "account-management",
                                      "pluralDisplayName": "Account Settings",
                                      "route": "/setup/account-management/list",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Field Sales",
                                      "items": Array [
                                        Object {
                                          "displayName": "Manage Executives",
                                          "items": Array [],
                                          "name": "manage-executives",
                                          "pluralDisplayName": "Manage Executives",
                                          "route": "/setup/field-sales/executives/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "field-sales",
                                      "pluralDisplayName": "Field Sales",
                                      "route": "/setup/field-sales/executives/list",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Import Data",
                                      "items": Array [],
                                      "name": "data-management",
                                      "pluralDisplayName": "Import Data",
                                      "route": "/setup/data-management/imports/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "Products & Services",
                                      "items": Array [],
                                      "name": "products-service",
                                      "pluralDisplayName": "Products & Services",
                                      "route": "/setup/products-services/list",
                                      "type": "link",
                                    },
                                    Object {
                                      "displayName": "User Settings",
                                      "items": Array [
                                        Object {
                                          "displayName": "Manage Users",
                                          "items": Array [],
                                          "name": "user",
                                          "pluralDisplayName": "Manage Users",
                                          "route": "/setup/users/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Manage Teams",
                                          "items": Array [],
                                          "name": "team",
                                          "pluralDisplayName": "Manage Teams",
                                          "route": "/setup/teams/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "User Permission",
                                          "items": Array [],
                                          "name": "profile",
                                          "pluralDisplayName": "User Permissions",
                                          "route": "/setup/profiles/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Data Sharing",
                                          "items": Array [],
                                          "name": "sharing",
                                          "pluralDisplayName": "Data Sharing",
                                          "route": "/setup/data-management/sharing/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "user",
                                      "pluralDisplayName": "User Settings",
                                      "route": "/setup/users/list",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Customization",
                                      "items": Array [
                                        Object {
                                          "displayName": "Rename Lead",
                                          "items": Array [],
                                          "name": "leads-setting",
                                          "pluralDisplayName": "Rename Leads",
                                          "route": "/setup/leads/settings",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Rename Contact",
                                          "items": Array [],
                                          "name": "contacts-setting",
                                          "pluralDisplayName": "Rename Contacts",
                                          "route": "/setup/contacts/settings",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Lead Fields & Forms",
                                          "items": Array [],
                                          "name": "leads-field",
                                          "pluralDisplayName": "Lead Fields & Forms",
                                          "route": "/setup/fields/leads/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Contact Fields & Forms",
                                          "items": Array [],
                                          "name": "contacts-field",
                                          "pluralDisplayName": "Contact Fields & Forms",
                                          "route": "/setup/fields/contacts/list",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Pipeline",
                                          "items": Array [],
                                          "name": "pipeline",
                                          "pluralDisplayName": "Pipelines",
                                          "route": "/setup/leads/pipelines/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "customization",
                                      "pluralDisplayName": "Customizations",
                                      "route": "/setup/customizations/settings",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Integration",
                                      "items": Array [
                                        Object {
                                          "displayName": "Lead Capture Forms",
                                          "items": Array [],
                                          "name": "lead-capture-form",
                                          "pluralDisplayName": "Lead Capture Forms",
                                          "route": "/setup/integrations/lead-capture-forms/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "integration",
                                      "pluralDisplayName": "Integrations",
                                      "route": "/setup/integrations/settings",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Automation",
                                      "items": Array [
                                        Object {
                                          "displayName": "Workflows",
                                          "items": Array [],
                                          "name": "workflow",
                                          "pluralDisplayName": "Workflows",
                                          "route": "/setup/workflow-automation/workflows/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "automation",
                                      "pluralDisplayName": "Automations",
                                      "route": "/setup/workflow-automation/settings",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "AI Features",
                                      "items": Array [
                                        Object {
                                          "displayName": "Email Assistant",
                                          "items": Array [],
                                          "name": "email-assistant",
                                          "pluralDisplayName": "Email Assistant",
                                          "route": "/setup/ai/email-assistant",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "ai",
                                      "pluralDisplayName": "AI Features",
                                      "route": "/setup/ai",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Communication",
                                      "items": Array [
                                        Object {
                                          "displayName": "Email Settings",
                                          "items": Array [],
                                          "name": "email-setting",
                                          "pluralDisplayName": "Email Settings",
                                          "route": "/setup/communication/email-settings",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Email Template",
                                          "items": Array [],
                                          "name": "email-template",
                                          "pluralDisplayName": "Email Templates",
                                          "route": "/setup/communication/email-templates/list",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "communication",
                                      "pluralDisplayName": "Communications",
                                      "route": "/setup/communication/email-settings",
                                      "type": "dropdown",
                                    },
                                    Object {
                                      "displayName": "Billing",
                                      "items": Array [
                                        Object {
                                          "displayName": "Usage Statistics",
                                          "items": Array [],
                                          "name": "usage-statistic",
                                          "pluralDisplayName": "Usage Statistics",
                                          "route": "/setup/billing/usage-statistics",
                                          "type": "link",
                                        },
                                        Object {
                                          "displayName": "Subscription Details",
                                          "items": Array [],
                                          "name": "subscription-detail",
                                          "pluralDisplayName": "Subscription Details",
                                          "route": "/setup/billing/subscription-details",
                                          "type": "link",
                                        },
                                      ],
                                      "name": "billing",
                                      "pluralDisplayName": "Billings",
                                      "route": "/setup/billing/usage-statistics",
                                      "type": "dropdown",
                                    },
                                  ],
                                },
                              }
                            }
                            history={
                              Object {
                                "action": "POP",
                                "block": [Function],
                                "createHref": [Function],
                                "go": [Function],
                                "goBack": [Function],
                                "goForward": [Function],
                                "length": 1,
                                "listen": [Function],
                                "location": Object {
                                  "hash": "",
                                  "pathname": "/",
                                  "search": "",
                                  "state": undefined,
                                },
                                "push": [Function],
                                "replace": [Function],
                              }
                            }
                            isTenantUser={true}
                            openedMarketplaceActions={Object {}}
                            planName="embark"
                            profilePermissions={
                              Array [
                                Object {
                                  "action": Object {
                                    "read": true,
                                    "update": true,
                                    "write": true,
                                  },
                                  "name": "deal",
                                },
                                Object {
                                  "action": Object {
                                    "read": true,
                                    "write": true,
                                  },
                                  "name": "contact",
                                },
                                Object {
                                  "action": Object {
                                    "read": true,
                                    "write": true,
                                  },
                                  "name": "company",
                                },
                              ]
                            }
                            resetSelectedPageAction={[Function]}
                            tenantId={123}
                            tenantUsage={
                              Object {
                                "addons": Array [
                                  Object {
                                    "displayName": "Lead Custom Fields",
                                    "id": "lead-custom-fields",
                                    "totalCost": 75000,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "Contact Custom Fields",
                                    "id": "contact-custom-fields",
                                    "totalCost": 75000,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "Email Templates",
                                    "id": "email-templates",
                                    "totalCost": 75000,
                                    "totalCount": 10,
                                  },
                                  Object {
                                    "displayName": "Workflows",
                                    "id": "workflows",
                                    "totalCost": 7000,
                                    "totalCount": 10,
                                  },
                                  Object {
                                    "displayName": "File Storage",
                                    "id": "storage",
                                    "totalCost": 75000,
                                    "totalCount": 25,
                                  },
                                  Object {
                                    "displayName": "Records Count",
                                    "id": "records-count",
                                    "totalCost": 75000,
                                    "totalCount": 20000,
                                  },
                                  Object {
                                    "displayName": "Email Tracking",
                                    "id": "email-tracking",
                                    "totalCost": 75000,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "Forex",
                                    "id": "forex",
                                    "totalCost": 2500,
                                    "totalCount": 5,
                                  },
                                  Object {
                                    "displayName": "Security features",
                                    "id": "security",
                                    "totalCost": 2500,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "Field Sales",
                                    "id": "field-sales-live-tracking",
                                    "totalCost": 1000,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "WhatsApp Business",
                                    "id": "WhatsApp-Business",
                                    "totalCost": 3498,
                                    "totalCount": 1,
                                  },
                                  Object {
                                    "displayName": "WhatsApp Credits",
                                    "id": "WhatsApp-Credits",
                                    "totalCost": 2000,
                                    "totalCount": 2000,
                                  },
                                  Object {
                                    "displayName": "Campaigns",
                                    "id": "campaigns",
                                    "totalCost": 10000,
                                    "totalCount": 10,
                                  },
                                ],
                                "customFieldCounts": Array [
                                  Object {
                                    "count": 3,
                                    "entity": "LEAD_CUSTOM_FIELD",
                                  },
                                  Object {
                                    "count": 4,
                                    "entity": "CONTACT_CUSTOM_FIELD",
                                  },
                                  Object {
                                    "count": 4,
                                    "entity": "DEAL_CUSTOM_FIELD",
                                  },
                                  Object {
                                    "count": 2,
                                    "entity": "COMPANY_CUSTOM_FIELD",
                                  },
                                  Object {
                                    "count": 3,
                                    "entity": "MEETING_CUSTOM_FIELD",
                                  },
                                ],
                                "customFields": Object {
                                  "total": 23,
                                  "used": 13,
                                },
                                "customLayoutCounts": Array [
                                  Object {
                                    "count": 1,
                                    "entity": "DEAL_CUSTOM_LAYOUTS",
                                  },
                                ],
                                "emailTemplates": Object {
                                  "total": 30,
                                  "used": 5,
                                },
                                "emailTracking": Object {
                                  "total": 1,
                                  "used": 0,
                                },
                                "entityCounts": Array [
                                  Object {
                                    "count": 10,
                                    "entity": "LEAD",
                                  },
                                  Object {
                                    "count": 20,
                                    "entity": "DEAL",
                                  },
                                  Object {
                                    "count": 10,
                                    "entity": "CONTACT",
                                  },
                                  Object {
                                    "count": 15,
                                    "entity": "COMPANY",
                                  },
                                  Object {
                                    "count": 10,
                                    "entity": "TASK",
                                  },
                                  Object {
                                    "count": 20,
                                    "entity": "NOTE",
                                  },
                                  Object {
                                    "count": 5,
                                    "entity": "EMAIL",
                                  },
                                  Object {
                                    "count": 7,
                                    "entity": "MEETING",
                                  },
                                ],
                                "fieldSales": Object {
                                  "total": 1,
                                  "used": 0,
                                },
                                "forex": Object {
                                  "total": 5,
                                  "used": 0,
                                },
                                "layouts": Object {
                                  "total": 15,
                                  "used": 1,
                                },
                                "plan": Object {
                                  "id": "elevate-annual",
                                  "name": "Premium Plan",
                                },
                                "records": Object {
                                  "total": 4000,
                                  "used": 40,
                                },
                                "renewalAt": "2020-10-06T05:00:00.000+0000",
                                "storage": Object {
                                  "total": 100,
                                  "used": 82,
                                },
                                "storageCounts": Array [
                                  Object {
                                    "count": 158694,
                                    "entity": "STORAGE_DOCUMENT",
                                  },
                                  Object {
                                    "count": 5876573,
                                    "entity": "STORAGE_IMPORT",
                                  },
                                  Object {
                                    "count": 4566,
                                    "entity": "STORAGE_EMAIL_ATTACHMENT",
                                  },
                                  Object {
                                    "count": 4251,
                                    "entity": "STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT",
                                  },
                                ],
                                "trialAddons": Array [
                                  Object {
                                    "active": true,
                                    "displayName": "Field Sales",
                                    "endAt": "2024-01-25T23:59:59.265Z",
                                    "startAt": "2023-12-27T04:21:02.265Z",
                                    "totalCost": 0,
                                    "totalCount": 1,
                                  },
                                ],
                                "updatedAt": "2020-10-06T05:00:00.000+0000",
                                "whatsappBusiness": Object {
                                  "total": 1,
                                  "used": 0,
                                },
                                "whatsappCredits": Object {
                                  "total": 2000,
                                  "used": 1000,
                                },
                                "workflows": Object {
                                  "total": 50,
                                  "used": 22,
                                },
                              }
                            }
                          >
                            <aside
                              className="left-navbar"
                            >
                              <nav
                                className="left-navbar__Setup"
                              >
                                <ul
                                  className="left-navbar__side-nav"
                                >
                                  <li
                                    className="left-navbar__side-nav__item"
                                    key="all-apps"
                                    onClick={[Function]}
                                  >
                                    <NavIcon
                                      iconColor="#fff"
                                      name="all-apps"
                                    >
                                      <AllAppsIcon
                                        color="#fff"
                                        key="all apps"
                                      >
                                        <svg
                                          height={14}
                                          viewBox="0 0 16 14"
                                          width={16}
                                        >
                                          <path
                                            d="M4.667,32.75v2.5a.75.75,0,0,1-.75.75H.75A.75.75,0,0,1,0,35.25v-2.5A.75.75,0,0,1,.75,32H3.917a.75.75,0,0,1,.75.75Zm5.667,7.5v-2.5a.75.75,0,0,0-.75-.75H6.417a.75.75,0,0,0-.75.75v2.5a.75.75,0,0,0,.75.75H9.583A.75.75,0,0,0,10.333,40.25Zm1-7.5v2.5a.75.75,0,0,0,.75.75H15.25a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75H12.083A.75.75,0,0,0,11.333,32.75Zm-1,2.5v-2.5a.75.75,0,0,0-.75-.75H6.417a.75.75,0,0,0-.75.75v2.5a.75.75,0,0,0,.75.75H9.583A.75.75,0,0,0,10.333,35.25ZM3.917,37H.75a.75.75,0,0,0-.75.75v2.5A.75.75,0,0,0,.75,41H3.917a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75ZM0,42.75v2.5A.75.75,0,0,0,.75,46H3.917a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75H.75A.75.75,0,0,0,0,42.75ZM12.083,41H15.25a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75H12.083a.75.75,0,0,0-.75.75v2.5A.75.75,0,0,0,12.083,41Zm0,5H15.25a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75H12.083a.75.75,0,0,0-.75.75v2.5A.75.75,0,0,0,12.083,46ZM5.667,42.75v2.5a.75.75,0,0,0,.75.75H9.583a.75.75,0,0,0,.75-.75v-2.5a.75.75,0,0,0-.75-.75H6.417A.75.75,0,0,0,5.667,42.75Z"
                                            fill="#fff"
                                            transform="translate(0 -32)"
                                          />
                                        </svg>
                                      </AllAppsIcon>
                                    </NavIcon>
                                    <div
                                      className="left-navbar__side-nav__item-section"
                                      id="all-appsSection"
                                    >
                                      All Apps
                                    </div>
                                  </li>
                                  <li
                                    className="left-navbar__side-nav__item"
                                    key="installed-apps"
                                    onClick={[Function]}
                                  >
                                    <NavIcon
                                      iconColor="#fff"
                                      name="installed-apps"
                                    >
                                      <InstalledAppsIcon
                                        color="#fff"
                                        key="installed apps"
                                      >
                                        <svg
                                          height={16}
                                          viewBox="0 0 16 16"
                                          width={16}
                                        >
                                          <path
                                            d="M24,16a8,8,0,1,1-8-8A8,8,0,0,1,24,16Zm-8.925,4.236L21.01,14.3a.516.516,0,0,0,0-.73l-.73-.73a.516.516,0,0,0-.73,0L14.71,17.681l-2.26-2.26a.516.516,0,0,0-.73,0l-.73.73a.516.516,0,0,0,0,.73l3.355,3.355a.516.516,0,0,0,.73,0Z"
                                            fill="#fff"
                                            transform="translate(-8 -8)"
                                          />
                                        </svg>
                                      </InstalledAppsIcon>
                                    </NavIcon>
                                    <div
                                      className="left-navbar__side-nav__item-section"
                                      id="installed-appsSection"
                                    >
                                      Installed Apps
                                    </div>
                                  </li>
                                  <li
                                    className="left-navbar__side-nav__item"
                                    key="manage-my-apps"
                                    onClick={[Function]}
                                  >
                                    <NavIcon
                                      iconColor="#fff"
                                      name="manage-my-apps"
                                    >
                                      <ManageMyAppsIcon
                                        color="#fff"
                                        key="manage my apps"
                                      >
                                        <svg
                                          height={16.001}
                                          viewBox="0 0 16 16.001"
                                          width={16}
                                        >
                                          <path
                                            d="M15.61,12.365,11.951,8.706a2.32,2.32,0,0,0-2.669-.434L5.951,4.94V3l-4-3-2,2,3,4H4.892L8.223,9.331A2.325,2.325,0,0,0,8.657,12l3.659,3.659a1.162,1.162,0,0,0,1.647,0l1.647-1.647a1.167,1.167,0,0,0,0-1.647ZM10.317,7.031A3.288,3.288,0,0,1,12.657,8l.606.606a4.423,4.423,0,0,0,1.369-.922,4.494,4.494,0,0,0,1.184-4.272.374.374,0,0,0-.628-.172L12.863,5.565l-2.122-.353L10.388,3.09,12.713.766a.377.377,0,0,0-.178-.631A4.5,4.5,0,0,0,8.267,1.319,4.428,4.428,0,0,0,6.979,4.556L9.545,7.121a3.4,3.4,0,0,1,.772-.091ZM7.07,9.593,5.3,7.821.536,12.587a2,2,0,0,0,2.828,2.828l3.862-3.862A3.356,3.356,0,0,1,7.07,9.593ZM1.951,14.749A.75.75,0,1,1,2.7,14,.752.752,0,0,1,1.951,14.749Z"
                                            fill="#fff"
                                            transform="translate(0.05)"
                                          />
                                        </svg>
                                      </ManageMyAppsIcon>
                                    </NavIcon>
                                    <div
                                      className="left-navbar__side-nav__item-section"
                                      id="manage-my-appsSection"
                                    >
                                      Manage My Apps
                                    </div>
                                  </li>
                                  <li
                                    className="left-navbar__side-nav__item api-guidelines border-top mt-auto mb-1"
                                  >
                                    <NavIcon
                                      iconColor="#fff"
                                      name="api-guidelines"
                                    >
                                      <APIGuidelinesIcon
                                        color="#fff"
                                        key="api guidelines"
                                      >
                                        <svg
                                          height={12.798}
                                          viewBox="0 0 16 12.798"
                                          width={16}
                                        >
                                          <path
                                            d="M6.948,12.823,5.423,12.38a.3.3,0,0,1-.2-.372L8.63.254A.3.3,0,0,1,9,.049l1.525.442a.3.3,0,0,1,.2.372L7.32,12.618A.3.3,0,0,1,6.948,12.823Zm-2.85-2.8,1.087-1.16a.3.3,0,0,0-.02-.43L2.9,6.436,5.165,4.443a.3.3,0,0,0,.02-.43L4.1,2.854a.3.3,0,0,0-.425-.012L.071,6.216a.3.3,0,0,0,0,.437l3.6,3.377a.3.3,0,0,0,.425-.012Zm8.179.015,3.6-3.377a.3.3,0,0,0,0-.437l-3.6-3.38a.3.3,0,0,0-.425.012l-1.087,1.16a.3.3,0,0,0,.02.43L13.05,6.436,10.785,8.428a.3.3,0,0,0-.02.43l1.087,1.16a.3.3,0,0,0,.425.015Z"
                                            fill="#fff"
                                            transform="translate(0.025 -0.037)"
                                          />
                                        </svg>
                                      </APIGuidelinesIcon>
                                    </NavIcon>
                                    <a
                                      className="left-navbar__side-nav__item-section"
                                      href="https://documenter.getpostman.com/view/3365774/UzBiQ8vJ"
                                      rel="noopener noreferrer"
                                      target="_blank"
                                    >
                                      Click for API guidelines
                                    </a>
                                  </li>
                                </ul>
                              </nav>
                            </aside>
                          </VerticalNavbar>
                        </Connect(VerticalNavbar)>
                        <main
                          className="main-content min-height-0 position-relative main-parent-wrapper left-nav-closed"
                        >
                          <withRouter(ApiStateHandler)
                            ErrorFallbackComponent={[Function]}
                            SkeletonComponent={[Function]}
                            error={null}
                            loading={false}
                            showErrorComponent={true}
                          >
                            <Route>
                              <ApiStateHandler
                                ErrorFallbackComponent={[Function]}
                                SkeletonComponent={[Function]}
                                error={null}
                                history={
                                  Object {
                                    "action": "POP",
                                    "block": [Function],
                                    "createHref": [Function],
                                    "go": [Function],
                                    "goBack": [Function],
                                    "goForward": [Function],
                                    "length": 1,
                                    "listen": [Function],
                                    "location": Object {
                                      "hash": "",
                                      "pathname": "/",
                                      "search": "",
                                      "state": undefined,
                                    },
                                    "push": [Function],
                                    "replace": [Function],
                                  }
                                }
                                loading={false}
                                location={
                                  Object {
                                    "hash": "",
                                    "pathname": "/",
                                    "search": "",
                                    "state": undefined,
                                  }
                                }
                                match={
                                  Object {
                                    "isExact": true,
                                    "params": Object {},
                                    "path": "/",
                                    "url": "/",
                                  }
                                }
                                showErrorComponent={true}
                              >
                                <div
                                  className="campaign-details"
                                >
                                  <div
                                    className="back-page"
                                    onClick={[Function]}
                                  >
                                    <i
                                      className="fas fa-arrow-left mr-2"
                                    />
                                    Back To 
                                    Campaigns
                                     List
                                  </div>
                                  <div
                                    className="header"
                                  >
                                    <div
                                      className="text-break"
                                    >
                                      <div
                                        className="campaign-name"
                                      >
                                        Kylas Campaign
                                      </div>
                                      <div
                                        className="campaign-description"
                                      >
                                        Kylas Campaign for Diwali festival
                                      </div>
                                    </div>
                                    <div
                                      className="actions"
                                    >
                                      <button
                                        className="campaign__action-type f-14 btn btn-outline-primary"
                                        key="0"
                                        onClick={[Function]}
                                      >
                                        Start Campaign
                                      </button>
                                      <div
                                        className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
                                        onClick={[Function]}
                                      >
                                        <EditIcon>
                                          <svg
                                            height={16}
                                            viewBox="0 0 16 16"
                                            width={16}
                                          >
                                            <defs>
                                              <clipPath
                                                id="clip-Ic_Edit"
                                              >
                                                <rect
                                                  height="16"
                                                  width="16"
                                                />
                                              </clipPath>
                                            </defs>
                                            <g
                                              clipPath="url(#clip-Ic_Edit)"
                                              id="Ic_Edit"
                                            >
                                              <path
                                                d="M9.075,2.921l4,4L4.388,15.608.822,16a.75.75,0,0,1-.828-.828l.4-3.569L9.075,2.921Zm6.475-.6L13.671.447a1.5,1.5,0,0,0-2.122,0L9.782,2.214l4,4L15.55,4.447a1.5,1.5,0,0,0,0-2.122Z"
                                                data-name="Icon/Edit"
                                                fill="#fff"
                                                id="Icon_Edit"
                                                transform="translate(0.011 -0.007)"
                                              />
                                            </g>
                                          </svg>
                                        </EditIcon>
                                      </div>
                                      <MultiActionModal
                                        className="btn-down-arrow btn-primary"
                                        icon={<DropdownIcon />}
                                        options={
                                          Array [
                                            Object {
                                              "action": [Function],
                                              "isDisabled": false,
                                              "label": "Delete",
                                              "tooltip": "",
                                            },
                                          ]
                                        }
                                      >
                                        <div
                                          className="dropdown "
                                        >
                                          <button
                                            aria-expanded="false"
                                            aria-haspopup="true"
                                            className="btn dropdown-toggle btn-down-arrow btn-primary"
                                            data-offset="0,3"
                                            data-toggle="dropdown"
                                            onClick={[Function]}
                                            style={
                                              Object {
                                                "cursor": "pointer",
                                              }
                                            }
                                            type="button"
                                          >
                                            <DropdownIcon>
                                              <svg
                                                height={16}
                                                viewBox="0 0 16 16"
                                                width={16}
                                              >
                                                <defs>
                                                  <clipPath
                                                    id="clip-Ic_Dropdown"
                                                  >
                                                    <rect
                                                      height="16"
                                                      width="16"
                                                    />
                                                  </clipPath>
                                                </defs>
                                                <g
                                                  clipPath="url(#clip-Ic_Dropdown)"
                                                  id="Ic_Dropdown"
                                                >
                                                  <path
                                                    d="M17.944,288h9.981a1.007,1.007,0,0,1,.713,1.718L23.647,294.7a1,1,0,0,1-1.422,0l-4.995-4.987A1.007,1.007,0,0,1,17.944,288Z"
                                                    data-name="Icon/DropdownDown"
                                                    fill="#fff"
                                                    id="Icon_DropdownDown"
                                                    transform="translate(-14.934 -283)"
                                                  />
                                                </g>
                                              </svg>
                                            </DropdownIcon>
                                          </button>
                                          <div
                                            className="dropdown-menu dropdown-menu-right"
                                            style={
                                              Object {
                                                "left": "20px",
                                                "position": "absolute",
                                                "top": "0px",
                                                "transform": "translate3d(-129px, 18px, 0px)",
                                                "willChange": "transform",
                                              }
                                            }
                                            x-placement="bottom-end"
                                          >
                                            <a
                                              className="dropdown-item"
                                              href="javascript:void(0);"
                                              key="0_option"
                                              onClick={[Function]}
                                            >
                                              Delete
                                            </a>
                                          </div>
                                        </div>
                                      </MultiActionModal>
                                    </div>
                                  </div>
                                  <div
                                    className="body "
                                  >
                                    <Connect(CampaignOverview)
                                      campaign={
                                        Object {
                                          "activities": Array [
                                            Object {
                                              "actualExpense": Object {
                                                "currencyId": 400,
                                                "value": 110000,
                                              },
                                              "bulkJobId": null,
                                              "campaign": Object {
                                                "id": 123,
                                                "name": "campaign name",
                                              },
                                              "createdAt": "2021-09-10T04:04:23.835Z",
                                              "createdBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "endDate": "2021-09-11T04:04:23.835Z",
                                              "endedAt": "2021-09-10T04:04:23.835Z",
                                              "endedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "entity": "LEAD",
                                              "estimatedBudget": Object {
                                                "currencyId": 400,
                                                "value": 100000,
                                              },
                                              "filters": Object {
                                                "jsonRule": Object {
                                                  "condition": "AND",
                                                  "rules": Array [
                                                    Object {
                                                      "field": "id",
                                                      "id": "id",
                                                      "operator": "in",
                                                      "type": "long",
                                                      "value": "553092,553052",
                                                    },
                                                  ],
                                                  "valid": true,
                                                },
                                              },
                                              "id": 1,
                                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                              "lastPausedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "name": "Kylas Activity",
                                              "payload": Object {
                                                "connectedAccount": Object {
                                                  "id": 1,
                                                  "name": "Whatsapp Business Account",
                                                },
                                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                                "type": "WHATSAPP",
                                                "whatsappTemplate": Object {
                                                  "id": 47,
                                                  "name": "Welcome to Kylas",
                                                },
                                              },
                                              "recordActions": Object {
                                                "delete": true,
                                                "read": true,
                                                "readAll": true,
                                                "update": true,
                                                "updateAll": true,
                                                "write": true,
                                              },
                                              "recordsGenerated": 0,
                                              "startDate": "2021-09-10T04:04:23.835Z",
                                              "startedAt": "2021-09-10T04:04:23.835Z",
                                              "startedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "status": "DRAFT",
                                              "totalEngagement": 0,
                                              "updatedAt": "2021-09-10T04:04:23.835Z",
                                              "updatedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "utmCampaign": "utm campaign",
                                              "utmContent": "content",
                                              "utmMedium": "medium",
                                              "utmSource": "google",
                                              "utmTerm": "term",
                                            },
                                            Object {
                                              "actualExpense": Object {
                                                "currencyId": 400,
                                                "value": 110000,
                                              },
                                              "bulkJobId": null,
                                              "campaign": Object {
                                                "id": 123,
                                                "name": "campaign name",
                                              },
                                              "createdAt": "2021-09-10T04:04:23.835Z",
                                              "createdBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "endDate": "2021-09-11T04:04:23.835Z",
                                              "endedAt": "2021-09-10T04:04:23.835Z",
                                              "endedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "entity": "LEAD",
                                              "estimatedBudget": Object {
                                                "currencyId": 400,
                                                "value": 100000,
                                              },
                                              "filters": Object {
                                                "jsonRule": Object {
                                                  "condition": "AND",
                                                  "rules": Array [
                                                    Object {
                                                      "field": "id",
                                                      "id": "id",
                                                      "operator": "in",
                                                      "type": "long",
                                                      "value": "553092,553052",
                                                    },
                                                  ],
                                                  "valid": true,
                                                },
                                              },
                                              "id": 2,
                                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                              "lastPausedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "name": "Kylas Activity",
                                              "payload": Object {
                                                "connectedAccount": Object {
                                                  "id": 1,
                                                  "name": "Whatsapp Business Account",
                                                },
                                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                                "type": "WHATSAPP",
                                                "whatsappTemplate": Object {
                                                  "id": 47,
                                                  "name": "Welcome to Kylas",
                                                },
                                              },
                                              "recordActions": Object {
                                                "delete": true,
                                                "read": true,
                                                "readAll": true,
                                                "update": true,
                                                "updateAll": true,
                                                "write": true,
                                              },
                                              "recordsGenerated": 0,
                                              "startDate": "2021-09-10T04:04:23.835Z",
                                              "startedAt": "2021-09-10T04:04:23.835Z",
                                              "startedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "status": "DRAFT",
                                              "totalEngagement": 0,
                                              "updatedAt": "2021-09-10T04:04:23.835Z",
                                              "updatedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "utmCampaign": "utm campaign",
                                              "utmContent": "content",
                                              "utmMedium": "medium",
                                              "utmSource": "google",
                                              "utmTerm": "term",
                                            },
                                          ],
                                          "actualExpense": Object {
                                            "currencyId": 400,
                                            "value": 110000,
                                          },
                                          "createdAt": "2021-09-10T04:04:23.835Z",
                                          "createdBy": Object {
                                            "id": 3788,
                                            "name": "Andrew Strauss",
                                          },
                                          "description": "Kylas Campaign for Diwali festival",
                                          "endDate": "2021-09-11T04:04:23.835Z",
                                          "endedAt": "2021-09-10T04:04:23.835Z",
                                          "endedBy": Object {
                                            "id": 3788,
                                            "name": "Andrew Strauss",
                                          },
                                          "estimatedBudget": Object {
                                            "currencyId": 400,
                                            "value": 100000,
                                          },
                                          "id": 1,
                                          "name": "Kylas Campaign",
                                          "recordActions": Object {
                                            "delete": true,
                                            "read": true,
                                            "readAll": true,
                                            "update": true,
                                            "updateAll": true,
                                            "write": true,
                                          },
                                          "recordsGenerated": 0,
                                          "startDate": "2021-09-10T04:04:23.835Z",
                                          "startedAt": "2021-09-10T04:04:23.835Z",
                                          "startedBy": Object {
                                            "id": 3788,
                                            "name": "Andrew Strauss",
                                          },
                                          "status": "DRAFT",
                                          "totalEngagement": 0,
                                          "updatedAt": "2021-09-10T04:04:23.835Z",
                                          "updatedBy": Object {
                                            "id": 3788,
                                            "name": "Andrew Strauss",
                                          },
                                          "utmCampaign": "utm campaign",
                                          "utmContent": "content",
                                          "utmMedium": "medium",
                                          "utmSource": "google",
                                          "utmTerm": "term",
                                        }
                                      }
                                      history={
                                        Object {
                                          "push": [MockFunction],
                                        }
                                      }
                                    >
                                      <CampaignOverview
                                        campaign={
                                          Object {
                                            "activities": Array [
                                              Object {
                                                "actualExpense": Object {
                                                  "currencyId": 400,
                                                  "value": 110000,
                                                },
                                                "bulkJobId": null,
                                                "campaign": Object {
                                                  "id": 123,
                                                  "name": "campaign name",
                                                },
                                                "createdAt": "2021-09-10T04:04:23.835Z",
                                                "createdBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "endDate": "2021-09-11T04:04:23.835Z",
                                                "endedAt": "2021-09-10T04:04:23.835Z",
                                                "endedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "entity": "LEAD",
                                                "estimatedBudget": Object {
                                                  "currencyId": 400,
                                                  "value": 100000,
                                                },
                                                "filters": Object {
                                                  "jsonRule": Object {
                                                    "condition": "AND",
                                                    "rules": Array [
                                                      Object {
                                                        "field": "id",
                                                        "id": "id",
                                                        "operator": "in",
                                                        "type": "long",
                                                        "value": "553092,553052",
                                                      },
                                                    ],
                                                    "valid": true,
                                                  },
                                                },
                                                "id": 1,
                                                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                "lastPausedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "name": "Kylas Activity",
                                                "payload": Object {
                                                  "connectedAccount": Object {
                                                    "id": 1,
                                                    "name": "Whatsapp Business Account",
                                                  },
                                                  "sentTo": "PRIMARY_PHONE_NUMBER",
                                                  "type": "WHATSAPP",
                                                  "whatsappTemplate": Object {
                                                    "id": 47,
                                                    "name": "Welcome to Kylas",
                                                  },
                                                },
                                                "recordActions": Object {
                                                  "delete": true,
                                                  "read": true,
                                                  "readAll": true,
                                                  "update": true,
                                                  "updateAll": true,
                                                  "write": true,
                                                },
                                                "recordsGenerated": 0,
                                                "startDate": "2021-09-10T04:04:23.835Z",
                                                "startedAt": "2021-09-10T04:04:23.835Z",
                                                "startedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "status": "DRAFT",
                                                "totalEngagement": 0,
                                                "updatedAt": "2021-09-10T04:04:23.835Z",
                                                "updatedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "utmCampaign": "utm campaign",
                                                "utmContent": "content",
                                                "utmMedium": "medium",
                                                "utmSource": "google",
                                                "utmTerm": "term",
                                              },
                                              Object {
                                                "actualExpense": Object {
                                                  "currencyId": 400,
                                                  "value": 110000,
                                                },
                                                "bulkJobId": null,
                                                "campaign": Object {
                                                  "id": 123,
                                                  "name": "campaign name",
                                                },
                                                "createdAt": "2021-09-10T04:04:23.835Z",
                                                "createdBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "endDate": "2021-09-11T04:04:23.835Z",
                                                "endedAt": "2021-09-10T04:04:23.835Z",
                                                "endedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "entity": "LEAD",
                                                "estimatedBudget": Object {
                                                  "currencyId": 400,
                                                  "value": 100000,
                                                },
                                                "filters": Object {
                                                  "jsonRule": Object {
                                                    "condition": "AND",
                                                    "rules": Array [
                                                      Object {
                                                        "field": "id",
                                                        "id": "id",
                                                        "operator": "in",
                                                        "type": "long",
                                                        "value": "553092,553052",
                                                      },
                                                    ],
                                                    "valid": true,
                                                  },
                                                },
                                                "id": 2,
                                                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                "lastPausedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "name": "Kylas Activity",
                                                "payload": Object {
                                                  "connectedAccount": Object {
                                                    "id": 1,
                                                    "name": "Whatsapp Business Account",
                                                  },
                                                  "sentTo": "PRIMARY_PHONE_NUMBER",
                                                  "type": "WHATSAPP",
                                                  "whatsappTemplate": Object {
                                                    "id": 47,
                                                    "name": "Welcome to Kylas",
                                                  },
                                                },
                                                "recordActions": Object {
                                                  "delete": true,
                                                  "read": true,
                                                  "readAll": true,
                                                  "update": true,
                                                  "updateAll": true,
                                                  "write": true,
                                                },
                                                "recordsGenerated": 0,
                                                "startDate": "2021-09-10T04:04:23.835Z",
                                                "startedAt": "2021-09-10T04:04:23.835Z",
                                                "startedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "status": "DRAFT",
                                                "totalEngagement": 0,
                                                "updatedAt": "2021-09-10T04:04:23.835Z",
                                                "updatedBy": Object {
                                                  "id": 3788,
                                                  "name": "Andrew Strauss",
                                                },
                                                "utmCampaign": "utm campaign",
                                                "utmContent": "content",
                                                "utmMedium": "medium",
                                                "utmSource": "google",
                                                "utmTerm": "term",
                                              },
                                            ],
                                            "actualExpense": Object {
                                              "currencyId": 400,
                                              "value": 110000,
                                            },
                                            "createdAt": "2021-09-10T04:04:23.835Z",
                                            "createdBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "description": "Kylas Campaign for Diwali festival",
                                            "endDate": "2021-09-11T04:04:23.835Z",
                                            "endedAt": "2021-09-10T04:04:23.835Z",
                                            "endedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "estimatedBudget": Object {
                                              "currencyId": 400,
                                              "value": 100000,
                                            },
                                            "id": 1,
                                            "name": "Kylas Campaign",
                                            "recordActions": Object {
                                              "delete": true,
                                              "read": true,
                                              "readAll": true,
                                              "update": true,
                                              "updateAll": true,
                                              "write": true,
                                            },
                                            "recordsGenerated": 0,
                                            "startDate": "2021-09-10T04:04:23.835Z",
                                            "startedAt": "2021-09-10T04:04:23.835Z",
                                            "startedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "status": "DRAFT",
                                            "totalEngagement": 0,
                                            "updatedAt": "2021-09-10T04:04:23.835Z",
                                            "updatedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "utmCampaign": "utm campaign",
                                            "utmContent": "content",
                                            "utmMedium": "medium",
                                            "utmSource": "google",
                                            "utmTerm": "term",
                                          }
                                        }
                                        dispatch={[Function]}
                                        history={
                                          Object {
                                            "push": [MockFunction],
                                          }
                                        }
                                        numberFormat="INDIAN_NUMBER_FORMAT"
                                        timezone="Asia/Calcutta"
                                      >
                                        <div
                                          className="campaign-overview"
                                        >
                                          <strong>
                                            Campaign
                                             Overview
                                          </strong>
                                          <div
                                            className="campaign-overview__section"
                                            style={
                                              Object {
                                                "height": "34.98rem",
                                              }
                                            }
                                          >
                                            <div
                                              className="campaign-report w-65"
                                            >
                                              <div
                                                className="campaign-activities__not-started"
                                              >
                                                <div
                                                  className="d-flex flex-column align-items-center m-auto"
                                                >
                                                  <img
                                                    alt="report-data"
                                                    className="empty__report-data"
                                                    src="test-file-stub"
                                                  />
                                                  <div
                                                    className="mt-1"
                                                  >
                                                    <img
                                                      alt="bullseye-arrow"
                                                      src="test-file-stub"
                                                    />
                                                    <i
                                                      className="ml-1"
                                                    >
                                                      You're all set to launch!
                                                    </i>
                                                  </div>
                                                  <div
                                                    className="mt-2 add-instruction"
                                                  >
                                                    You've added an activity to this 
                                                    campaign
                                                    . Once it starts, you'll see key performance insights
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div
                                              className="w-35"
                                            >
                                              <Connect(CampaignRelatedFormValues)
                                                entity="campaigns"
                                                formValues={
                                                  Object {
                                                    "activities": Array [
                                                      Object {
                                                        "actualExpense": Object {
                                                          "currencyId": 400,
                                                          "value": 110000,
                                                        },
                                                        "bulkJobId": null,
                                                        "campaign": Object {
                                                          "id": 123,
                                                          "name": "campaign name",
                                                        },
                                                        "createdAt": "2021-09-10T04:04:23.835Z",
                                                        "createdBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "endDate": "2021-09-11T04:04:23.835Z",
                                                        "endedAt": "2021-09-10T04:04:23.835Z",
                                                        "endedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "entity": "LEAD",
                                                        "estimatedBudget": Object {
                                                          "currencyId": 400,
                                                          "value": 100000,
                                                        },
                                                        "filters": Object {
                                                          "jsonRule": Object {
                                                            "condition": "AND",
                                                            "rules": Array [
                                                              Object {
                                                                "field": "id",
                                                                "id": "id",
                                                                "operator": "in",
                                                                "type": "long",
                                                                "value": "553092,553052",
                                                              },
                                                            ],
                                                            "valid": true,
                                                          },
                                                        },
                                                        "id": 1,
                                                        "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                        "lastPausedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "name": "Kylas Activity",
                                                        "payload": Object {
                                                          "connectedAccount": Object {
                                                            "id": 1,
                                                            "name": "Whatsapp Business Account",
                                                          },
                                                          "sentTo": "PRIMARY_PHONE_NUMBER",
                                                          "type": "WHATSAPP",
                                                          "whatsappTemplate": Object {
                                                            "id": 47,
                                                            "name": "Welcome to Kylas",
                                                          },
                                                        },
                                                        "recordActions": Object {
                                                          "delete": true,
                                                          "read": true,
                                                          "readAll": true,
                                                          "update": true,
                                                          "updateAll": true,
                                                          "write": true,
                                                        },
                                                        "recordsGenerated": 0,
                                                        "startDate": "2021-09-10T04:04:23.835Z",
                                                        "startedAt": "2021-09-10T04:04:23.835Z",
                                                        "startedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "status": "DRAFT",
                                                        "totalEngagement": 0,
                                                        "updatedAt": "2021-09-10T04:04:23.835Z",
                                                        "updatedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "utmCampaign": "utm campaign",
                                                        "utmContent": "content",
                                                        "utmMedium": "medium",
                                                        "utmSource": "google",
                                                        "utmTerm": "term",
                                                      },
                                                      Object {
                                                        "actualExpense": Object {
                                                          "currencyId": 400,
                                                          "value": 110000,
                                                        },
                                                        "bulkJobId": null,
                                                        "campaign": Object {
                                                          "id": 123,
                                                          "name": "campaign name",
                                                        },
                                                        "createdAt": "2021-09-10T04:04:23.835Z",
                                                        "createdBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "endDate": "2021-09-11T04:04:23.835Z",
                                                        "endedAt": "2021-09-10T04:04:23.835Z",
                                                        "endedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "entity": "LEAD",
                                                        "estimatedBudget": Object {
                                                          "currencyId": 400,
                                                          "value": 100000,
                                                        },
                                                        "filters": Object {
                                                          "jsonRule": Object {
                                                            "condition": "AND",
                                                            "rules": Array [
                                                              Object {
                                                                "field": "id",
                                                                "id": "id",
                                                                "operator": "in",
                                                                "type": "long",
                                                                "value": "553092,553052",
                                                              },
                                                            ],
                                                            "valid": true,
                                                          },
                                                        },
                                                        "id": 2,
                                                        "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                        "lastPausedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "name": "Kylas Activity",
                                                        "payload": Object {
                                                          "connectedAccount": Object {
                                                            "id": 1,
                                                            "name": "Whatsapp Business Account",
                                                          },
                                                          "sentTo": "PRIMARY_PHONE_NUMBER",
                                                          "type": "WHATSAPP",
                                                          "whatsappTemplate": Object {
                                                            "id": 47,
                                                            "name": "Welcome to Kylas",
                                                          },
                                                        },
                                                        "recordActions": Object {
                                                          "delete": true,
                                                          "read": true,
                                                          "readAll": true,
                                                          "update": true,
                                                          "updateAll": true,
                                                          "write": true,
                                                        },
                                                        "recordsGenerated": 0,
                                                        "startDate": "2021-09-10T04:04:23.835Z",
                                                        "startedAt": "2021-09-10T04:04:23.835Z",
                                                        "startedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "status": "DRAFT",
                                                        "totalEngagement": 0,
                                                        "updatedAt": "2021-09-10T04:04:23.835Z",
                                                        "updatedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "utmCampaign": "utm campaign",
                                                        "utmContent": "content",
                                                        "utmMedium": "medium",
                                                        "utmSource": "google",
                                                        "utmTerm": "term",
                                                      },
                                                    ],
                                                    "actualExpense": Object {
                                                      "currencyId": 400,
                                                      "value": 110000,
                                                    },
                                                    "createdAt": "2021-09-10T04:04:23.835Z",
                                                    "createdBy": Object {
                                                      "id": 3788,
                                                      "name": "Andrew Strauss",
                                                    },
                                                    "description": "Kylas Campaign for Diwali festival",
                                                    "endDate": "2021-09-11T04:04:23.835Z",
                                                    "endedAt": "2021-09-10T04:04:23.835Z",
                                                    "endedBy": Object {
                                                      "id": 3788,
                                                      "name": "Andrew Strauss",
                                                    },
                                                    "estimatedBudget": Object {
                                                      "currencyId": 400,
                                                      "value": 100000,
                                                    },
                                                    "id": 1,
                                                    "name": "Kylas Campaign",
                                                    "recordActions": Object {
                                                      "delete": true,
                                                      "read": true,
                                                      "readAll": true,
                                                      "update": true,
                                                      "updateAll": true,
                                                      "write": true,
                                                    },
                                                    "recordsGenerated": 0,
                                                    "startDate": "2021-09-10T04:04:23.835Z",
                                                    "startedAt": "2021-09-10T04:04:23.835Z",
                                                    "startedBy": Object {
                                                      "id": 3788,
                                                      "name": "Andrew Strauss",
                                                    },
                                                    "status": "DRAFT",
                                                    "totalEngagement": 0,
                                                    "updatedAt": "2021-09-10T04:04:23.835Z",
                                                    "updatedBy": Object {
                                                      "id": 3788,
                                                      "name": "Andrew Strauss",
                                                    },
                                                    "utmCampaign": "utm campaign",
                                                    "utmContent": "content",
                                                    "utmMedium": "medium",
                                                    "utmSource": "google",
                                                    "utmTerm": "term",
                                                  }
                                                }
                                              >
                                                <CampaignRelatedFormValues
                                                  dateFormat="MMM D, YYYY [at] h:mm a"
                                                  dispatch={[Function]}
                                                  entity="campaigns"
                                                  entityLabelMap={
                                                    Object {
                                                      "COMPANY": Object {
                                                        "displayName": "Company",
                                                        "displayNamePlural": "Companies",
                                                      },
                                                      "CONTACT": Object {
                                                        "displayName": "Student",
                                                        "displayNamePlural": "Contacts",
                                                      },
                                                      "DEAL": Object {
                                                        "displayName": "Deal",
                                                        "displayNamePlural": "Deals",
                                                      },
                                                      "LEAD": Object {
                                                        "displayName": "Teacher",
                                                        "displayNamePlural": "Teachers",
                                                      },
                                                      "TASK": Object {
                                                        "displayName": "Task",
                                                        "displayNamePlural": "Tasks",
                                                      },
                                                      "TEAM": Object {
                                                        "displayName": "Team",
                                                        "displayNamePlural": "Teams",
                                                      },
                                                      "USER": Object {
                                                        "displayName": "User",
                                                        "displayNamePlural": "Users",
                                                      },
                                                    }
                                                  }
                                                  formValues={
                                                    Object {
                                                      "activities": Array [
                                                        Object {
                                                          "actualExpense": Object {
                                                            "currencyId": 400,
                                                            "value": 110000,
                                                          },
                                                          "bulkJobId": null,
                                                          "campaign": Object {
                                                            "id": 123,
                                                            "name": "campaign name",
                                                          },
                                                          "createdAt": "2021-09-10T04:04:23.835Z",
                                                          "createdBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "endDate": "2021-09-11T04:04:23.835Z",
                                                          "endedAt": "2021-09-10T04:04:23.835Z",
                                                          "endedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "entity": "LEAD",
                                                          "estimatedBudget": Object {
                                                            "currencyId": 400,
                                                            "value": 100000,
                                                          },
                                                          "filters": Object {
                                                            "jsonRule": Object {
                                                              "condition": "AND",
                                                              "rules": Array [
                                                                Object {
                                                                  "field": "id",
                                                                  "id": "id",
                                                                  "operator": "in",
                                                                  "type": "long",
                                                                  "value": "553092,553052",
                                                                },
                                                              ],
                                                              "valid": true,
                                                            },
                                                          },
                                                          "id": 1,
                                                          "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                          "lastPausedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "name": "Kylas Activity",
                                                          "payload": Object {
                                                            "connectedAccount": Object {
                                                              "id": 1,
                                                              "name": "Whatsapp Business Account",
                                                            },
                                                            "sentTo": "PRIMARY_PHONE_NUMBER",
                                                            "type": "WHATSAPP",
                                                            "whatsappTemplate": Object {
                                                              "id": 47,
                                                              "name": "Welcome to Kylas",
                                                            },
                                                          },
                                                          "recordActions": Object {
                                                            "delete": true,
                                                            "read": true,
                                                            "readAll": true,
                                                            "update": true,
                                                            "updateAll": true,
                                                            "write": true,
                                                          },
                                                          "recordsGenerated": 0,
                                                          "startDate": "2021-09-10T04:04:23.835Z",
                                                          "startedAt": "2021-09-10T04:04:23.835Z",
                                                          "startedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "status": "DRAFT",
                                                          "totalEngagement": 0,
                                                          "updatedAt": "2021-09-10T04:04:23.835Z",
                                                          "updatedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "utmCampaign": "utm campaign",
                                                          "utmContent": "content",
                                                          "utmMedium": "medium",
                                                          "utmSource": "google",
                                                          "utmTerm": "term",
                                                        },
                                                        Object {
                                                          "actualExpense": Object {
                                                            "currencyId": 400,
                                                            "value": 110000,
                                                          },
                                                          "bulkJobId": null,
                                                          "campaign": Object {
                                                            "id": 123,
                                                            "name": "campaign name",
                                                          },
                                                          "createdAt": "2021-09-10T04:04:23.835Z",
                                                          "createdBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "endDate": "2021-09-11T04:04:23.835Z",
                                                          "endedAt": "2021-09-10T04:04:23.835Z",
                                                          "endedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "entity": "LEAD",
                                                          "estimatedBudget": Object {
                                                            "currencyId": 400,
                                                            "value": 100000,
                                                          },
                                                          "filters": Object {
                                                            "jsonRule": Object {
                                                              "condition": "AND",
                                                              "rules": Array [
                                                                Object {
                                                                  "field": "id",
                                                                  "id": "id",
                                                                  "operator": "in",
                                                                  "type": "long",
                                                                  "value": "553092,553052",
                                                                },
                                                              ],
                                                              "valid": true,
                                                            },
                                                          },
                                                          "id": 2,
                                                          "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                          "lastPausedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "name": "Kylas Activity",
                                                          "payload": Object {
                                                            "connectedAccount": Object {
                                                              "id": 1,
                                                              "name": "Whatsapp Business Account",
                                                            },
                                                            "sentTo": "PRIMARY_PHONE_NUMBER",
                                                            "type": "WHATSAPP",
                                                            "whatsappTemplate": Object {
                                                              "id": 47,
                                                              "name": "Welcome to Kylas",
                                                            },
                                                          },
                                                          "recordActions": Object {
                                                            "delete": true,
                                                            "read": true,
                                                            "readAll": true,
                                                            "update": true,
                                                            "updateAll": true,
                                                            "write": true,
                                                          },
                                                          "recordsGenerated": 0,
                                                          "startDate": "2021-09-10T04:04:23.835Z",
                                                          "startedAt": "2021-09-10T04:04:23.835Z",
                                                          "startedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "status": "DRAFT",
                                                          "totalEngagement": 0,
                                                          "updatedAt": "2021-09-10T04:04:23.835Z",
                                                          "updatedBy": Object {
                                                            "id": 3788,
                                                            "name": "Andrew Strauss",
                                                          },
                                                          "utmCampaign": "utm campaign",
                                                          "utmContent": "content",
                                                          "utmMedium": "medium",
                                                          "utmSource": "google",
                                                          "utmTerm": "term",
                                                        },
                                                      ],
                                                      "actualExpense": Object {
                                                        "currencyId": 400,
                                                        "value": 110000,
                                                      },
                                                      "createdAt": "2021-09-10T04:04:23.835Z",
                                                      "createdBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "description": "Kylas Campaign for Diwali festival",
                                                      "endDate": "2021-09-11T04:04:23.835Z",
                                                      "endedAt": "2021-09-10T04:04:23.835Z",
                                                      "endedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "estimatedBudget": Object {
                                                        "currencyId": 400,
                                                        "value": 100000,
                                                      },
                                                      "id": 1,
                                                      "name": "Kylas Campaign",
                                                      "recordActions": Object {
                                                        "delete": true,
                                                        "read": true,
                                                        "readAll": true,
                                                        "update": true,
                                                        "updateAll": true,
                                                        "write": true,
                                                      },
                                                      "recordsGenerated": 0,
                                                      "startDate": "2021-09-10T04:04:23.835Z",
                                                      "startedAt": "2021-09-10T04:04:23.835Z",
                                                      "startedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "status": "DRAFT",
                                                      "totalEngagement": 0,
                                                      "updatedAt": "2021-09-10T04:04:23.835Z",
                                                      "updatedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "utmCampaign": "utm campaign",
                                                      "utmContent": "content",
                                                      "utmMedium": "medium",
                                                      "utmSource": "google",
                                                      "utmTerm": "term",
                                                    }
                                                  }
                                                  timezone="Asia/Calcutta"
                                                >
                                                  <div
                                                    className="campaign-related__form-values campaign__draft-status"
                                                  >
                                                    <div
                                                      className="section"
                                                      key="0"
                                                    >
                                                      <div
                                                        className="section-name"
                                                      >
                                                        Campaign Details
                                                      </div>
                                                      <div
                                                        className="row"
                                                      >
                                                        <div
                                                          className="col-6"
                                                          key="0"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            ID
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            1
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="1"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Status
                                                          </div>
                                                          <div
                                                            className="d-flex justify-content-end"
                                                          >
                                                            <div
                                                              className="campaign-related__status"
                                                              style={
                                                                Object {
                                                                  "backgroundColor": "#C6DEFF",
                                                                  "border": "1px solid #006DEE",
                                                                  "color": "#006DEE",
                                                                }
                                                              }
                                                            >
                                                              Draft
                                                            </div>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="2"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Start Date
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 10, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="3"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            End Date
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 11, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="4"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Created At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 10, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="5"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Created By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Andrew Strauss
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="6"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Updated At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 10, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="7"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Updated By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Andrew Strauss
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="8"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Started At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 10, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="9"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Started By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Andrew Strauss
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="10"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Last Paused At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            -
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="11"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Last Paused By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            -
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="12"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Last Resumed At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            -
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="13"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Last Resumed By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            -
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="14"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Ended At
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Sep 10, 2021 at 9:34 am
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="15"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            Ended By
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            Andrew Strauss
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      className="section"
                                                      key="1"
                                                    >
                                                      <div
                                                        className="section-name"
                                                      >
                                                        UTM Details
                                                      </div>
                                                      <div
                                                        className="row"
                                                      >
                                                        <div
                                                          className="col-6"
                                                          key="0"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            UTM Campaign
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            utm campaign
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="1"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            UTM Source
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            google
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="2"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            UTM Medium
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            medium
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="3"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            UTM Content
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            content
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="col-6"
                                                          key="4"
                                                        >
                                                          <div
                                                            className="label"
                                                          >
                                                            UTM Term
                                                          </div>
                                                          <div
                                                            className="f-14"
                                                          >
                                                            term
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </CampaignRelatedFormValues>
                                              </Connect(CampaignRelatedFormValues)>
                                            </div>
                                          </div>
                                        </div>
                                      </CampaignOverview>
                                    </Connect(CampaignOverview)>
                                    <div
                                      id="campaign-activity__details"
                                    >
                                      <div
                                        className="d-flex justify-content-between mb-3"
                                      >
                                        <div
                                          className="f-20"
                                        >
                                          Activities
                                        </div>
                                        <button
                                          className="add-activity btn f-14 btn-outline-primary"
                                          onClick={[Function]}
                                          style={
                                            Object {
                                              "backgroundColor": "",
                                            }
                                          }
                                        >
                                          + Activity
                                        </button>
                                      </div>
                                      <div
                                        className="tab-section"
                                      >
                                        <a
                                          className="nav-item nav-link active"
                                          data-toggle="tab"
                                          href="#"
                                          key="1"
                                          onClick={[Function]}
                                          role="tab"
                                        >
                                          Kylas Activity
                                        </a>
                                        <a
                                          className="nav-item nav-link"
                                          data-toggle="tab"
                                          href="#"
                                          key="2"
                                          onClick={[Function]}
                                          role="tab"
                                        >
                                          Kylas Activity
                                        </a>
                                      </div>
                                      <Connect(CampaignActivityOverview)
                                        activity={
                                          Object {
                                            "actualExpense": Object {
                                              "currencyId": 400,
                                              "value": 110000,
                                            },
                                            "bulkJobId": null,
                                            "campaign": Object {
                                              "id": 123,
                                              "name": "campaign name",
                                            },
                                            "createdAt": "2021-09-10T04:04:23.835Z",
                                            "createdBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "endDate": "2021-09-11T04:04:23.835Z",
                                            "endedAt": "2021-09-10T04:04:23.835Z",
                                            "endedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "entity": "LEAD",
                                            "estimatedBudget": Object {
                                              "currencyId": 400,
                                              "value": 100000,
                                            },
                                            "filters": Object {
                                              "jsonRule": Object {
                                                "condition": "AND",
                                                "rules": Array [
                                                  Object {
                                                    "field": "id",
                                                    "id": "id",
                                                    "operator": "in",
                                                    "type": "long",
                                                    "value": "553092,553052",
                                                  },
                                                ],
                                                "valid": true,
                                              },
                                            },
                                            "id": 1,
                                            "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                            "lastPausedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "name": "Kylas Activity",
                                            "payload": Object {
                                              "connectedAccount": Object {
                                                "id": 1,
                                                "name": "Whatsapp Business Account",
                                              },
                                              "sentTo": "PRIMARY_PHONE_NUMBER",
                                              "type": "WHATSAPP",
                                              "whatsappTemplate": Object {
                                                "id": 47,
                                                "name": "Welcome to Kylas",
                                              },
                                            },
                                            "recordActions": Object {
                                              "delete": true,
                                              "read": true,
                                              "readAll": true,
                                              "update": true,
                                              "updateAll": true,
                                              "write": true,
                                            },
                                            "recordsGenerated": 0,
                                            "startDate": "2021-09-10T04:04:23.835Z",
                                            "startedAt": "2021-09-10T04:04:23.835Z",
                                            "startedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "status": "DRAFT",
                                            "totalEngagement": 0,
                                            "updatedAt": "2021-09-10T04:04:23.835Z",
                                            "updatedBy": Object {
                                              "id": 3788,
                                              "name": "Andrew Strauss",
                                            },
                                            "utmCampaign": "utm campaign",
                                            "utmContent": "content",
                                            "utmMedium": "medium",
                                            "utmSource": "google",
                                            "utmTerm": "term",
                                          }
                                        }
                                        fetchData={[Function]}
                                        history={
                                          Object {
                                            "push": [MockFunction],
                                          }
                                        }
                                        recordActions={
                                          Object {
                                            "delete": true,
                                            "read": true,
                                            "readAll": true,
                                            "update": true,
                                            "updateAll": true,
                                            "write": true,
                                          }
                                        }
                                      >
                                        <CampaignActivityOverview
                                          activity={
                                            Object {
                                              "actualExpense": Object {
                                                "currencyId": 400,
                                                "value": 110000,
                                              },
                                              "bulkJobId": null,
                                              "campaign": Object {
                                                "id": 123,
                                                "name": "campaign name",
                                              },
                                              "createdAt": "2021-09-10T04:04:23.835Z",
                                              "createdBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "endDate": "2021-09-11T04:04:23.835Z",
                                              "endedAt": "2021-09-10T04:04:23.835Z",
                                              "endedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "entity": "LEAD",
                                              "estimatedBudget": Object {
                                                "currencyId": 400,
                                                "value": 100000,
                                              },
                                              "filters": Object {
                                                "jsonRule": Object {
                                                  "condition": "AND",
                                                  "rules": Array [
                                                    Object {
                                                      "field": "id",
                                                      "id": "id",
                                                      "operator": "in",
                                                      "type": "long",
                                                      "value": "553092,553052",
                                                    },
                                                  ],
                                                  "valid": true,
                                                },
                                              },
                                              "id": 1,
                                              "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                              "lastPausedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "name": "Kylas Activity",
                                              "payload": Object {
                                                "connectedAccount": Object {
                                                  "id": 1,
                                                  "name": "Whatsapp Business Account",
                                                },
                                                "sentTo": "PRIMARY_PHONE_NUMBER",
                                                "type": "WHATSAPP",
                                                "whatsappTemplate": Object {
                                                  "id": 47,
                                                  "name": "Welcome to Kylas",
                                                },
                                              },
                                              "recordActions": Object {
                                                "delete": true,
                                                "read": true,
                                                "readAll": true,
                                                "update": true,
                                                "updateAll": true,
                                                "write": true,
                                              },
                                              "recordsGenerated": 0,
                                              "startDate": "2021-09-10T04:04:23.835Z",
                                              "startedAt": "2021-09-10T04:04:23.835Z",
                                              "startedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "status": "DRAFT",
                                              "totalEngagement": 0,
                                              "updatedAt": "2021-09-10T04:04:23.835Z",
                                              "updatedBy": Object {
                                                "id": 3788,
                                                "name": "Andrew Strauss",
                                              },
                                              "utmCampaign": "utm campaign",
                                              "utmContent": "content",
                                              "utmMedium": "medium",
                                              "utmSource": "google",
                                              "utmTerm": "term",
                                            }
                                          }
                                          dispatch={[Function]}
                                          entityLabelMap={
                                            Object {
                                              "COMPANY": Object {
                                                "displayName": "Company",
                                                "displayNamePlural": "Companies",
                                              },
                                              "CONTACT": Object {
                                                "displayName": "Student",
                                                "displayNamePlural": "Contacts",
                                              },
                                              "DEAL": Object {
                                                "displayName": "Deal",
                                                "displayNamePlural": "Deals",
                                              },
                                              "LEAD": Object {
                                                "displayName": "Teacher",
                                                "displayNamePlural": "Teachers",
                                              },
                                              "TASK": Object {
                                                "displayName": "Task",
                                                "displayNamePlural": "Tasks",
                                              },
                                              "TEAM": Object {
                                                "displayName": "Team",
                                                "displayNamePlural": "Teams",
                                              },
                                              "USER": Object {
                                                "displayName": "User",
                                                "displayNamePlural": "Users",
                                              },
                                            }
                                          }
                                          fetchData={[Function]}
                                          history={
                                            Object {
                                              "push": [MockFunction],
                                            }
                                          }
                                          numberFormat="INDIAN_NUMBER_FORMAT"
                                          recordActions={
                                            Object {
                                              "delete": true,
                                              "read": true,
                                              "readAll": true,
                                              "update": true,
                                              "updateAll": true,
                                              "write": true,
                                            }
                                          }
                                          timezone="Asia/Calcutta"
                                        >
                                          <div
                                            className="campaign-activity__overview"
                                          >
                                            <div
                                              className="d-flex justify-content-between"
                                            >
                                              <strong
                                                className="align-self-center"
                                              >
                                                Activity Overview
                                              </strong>
                                              <div
                                                className="actions"
                                              >
                                                <div
                                                  className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
                                                  onClick={[Function]}
                                                >
                                                  <EditIcon>
                                                    <svg
                                                      height={16}
                                                      viewBox="0 0 16 16"
                                                      width={16}
                                                    >
                                                      <defs>
                                                        <clipPath
                                                          id="clip-Ic_Edit"
                                                        >
                                                          <rect
                                                            height="16"
                                                            width="16"
                                                          />
                                                        </clipPath>
                                                      </defs>
                                                      <g
                                                        clipPath="url(#clip-Ic_Edit)"
                                                        id="Ic_Edit"
                                                      >
                                                        <path
                                                          d="M9.075,2.921l4,4L4.388,15.608.822,16a.75.75,0,0,1-.828-.828l.4-3.569L9.075,2.921Zm6.475-.6L13.671.447a1.5,1.5,0,0,0-2.122,0L9.782,2.214l4,4L15.55,4.447a1.5,1.5,0,0,0,0-2.122Z"
                                                          data-name="Icon/Edit"
                                                          fill="#fff"
                                                          id="Icon_Edit"
                                                          transform="translate(0.011 -0.007)"
                                                        />
                                                      </g>
                                                    </svg>
                                                  </EditIcon>
                                                </div>
                                                <MultiActionModal
                                                  className="btn-down-arrow btn-primary"
                                                  icon={<DropdownIcon />}
                                                  options={
                                                    Array [
                                                      Object {
                                                        "action": [Function],
                                                        "label": "Start",
                                                      },
                                                      Object {
                                                        "action": [Function],
                                                        "isDisabled": false,
                                                        "label": "Delete",
                                                        "tooltip": "",
                                                      },
                                                    ]
                                                  }
                                                >
                                                  <div
                                                    className="dropdown "
                                                  >
                                                    <button
                                                      aria-expanded="false"
                                                      aria-haspopup="true"
                                                      className="btn dropdown-toggle btn-down-arrow btn-primary"
                                                      data-offset="0,3"
                                                      data-toggle="dropdown"
                                                      onClick={[Function]}
                                                      style={
                                                        Object {
                                                          "cursor": "pointer",
                                                        }
                                                      }
                                                      type="button"
                                                    >
                                                      <DropdownIcon>
                                                        <svg
                                                          height={16}
                                                          viewBox="0 0 16 16"
                                                          width={16}
                                                        >
                                                          <defs>
                                                            <clipPath
                                                              id="clip-Ic_Dropdown"
                                                            >
                                                              <rect
                                                                height="16"
                                                                width="16"
                                                              />
                                                            </clipPath>
                                                          </defs>
                                                          <g
                                                            clipPath="url(#clip-Ic_Dropdown)"
                                                            id="Ic_Dropdown"
                                                          >
                                                            <path
                                                              d="M17.944,288h9.981a1.007,1.007,0,0,1,.713,1.718L23.647,294.7a1,1,0,0,1-1.422,0l-4.995-4.987A1.007,1.007,0,0,1,17.944,288Z"
                                                              data-name="Icon/DropdownDown"
                                                              fill="#fff"
                                                              id="Icon_DropdownDown"
                                                              transform="translate(-14.934 -283)"
                                                            />
                                                          </g>
                                                        </svg>
                                                      </DropdownIcon>
                                                    </button>
                                                    <div
                                                      className="dropdown-menu dropdown-menu-right"
                                                      style={
                                                        Object {
                                                          "left": "20px",
                                                          "position": "absolute",
                                                          "top": "0px",
                                                          "transform": "translate3d(-129px, 18px, 0px)",
                                                          "willChange": "transform",
                                                        }
                                                      }
                                                      x-placement="bottom-end"
                                                    >
                                                      <a
                                                        className="dropdown-item"
                                                        href="javascript:void(0);"
                                                        key="0_option"
                                                        onClick={[Function]}
                                                      >
                                                        Start
                                                      </a>
                                                      <a
                                                        className="dropdown-item"
                                                        href="javascript:void(0);"
                                                        key="1_option"
                                                        onClick={[Function]}
                                                      >
                                                        Delete
                                                      </a>
                                                    </div>
                                                  </div>
                                                </MultiActionModal>
                                              </div>
                                            </div>
                                            <div
                                              className="d-flex"
                                              style={
                                                Object {
                                                  "gap": "1.5rem",
                                                  "marginTop": "1.25rem",
                                                }
                                              }
                                            >
                                              <div
                                                className="campaign-activity__report w-65"
                                              >
                                                <div
                                                  className="campaign-activity__not-started"
                                                >
                                                  <div
                                                    className="d-flex flex-column align-items-center m-auto"
                                                  >
                                                    <img
                                                      alt="Start Activity"
                                                      className="empty__report-data"
                                                      src="test-file-stub"
                                                    />
                                                    <div
                                                      className="mt-1"
                                                    >
                                                      Your activity is still in draft mode
                                                    </div>
                                                    <div
                                                      className="mt-2 add-instruction"
                                                    >
                                                      To begin tracking performance and viewing analytics, start the activity or complete its setup
                                                    </div>
                                                  </div>
                                                </div>
                                              </div>
                                              <div
                                                className="w-35"
                                              >
                                                <Connect(CampaignRelatedFormValues)
                                                  entity="campaign-activities"
                                                  formValues={
                                                    Object {
                                                      "actualExpense": Object {
                                                        "currencyId": 400,
                                                        "value": 110000,
                                                      },
                                                      "bulkJobId": null,
                                                      "campaign": Object {
                                                        "id": 123,
                                                        "name": "campaign name",
                                                      },
                                                      "connectedAccount": Object {
                                                        "id": 1,
                                                        "name": "Whatsapp Business Account",
                                                      },
                                                      "createdAt": "2021-09-10T04:04:23.835Z",
                                                      "createdBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "endDate": "2021-09-11T04:04:23.835Z",
                                                      "endedAt": "2021-09-10T04:04:23.835Z",
                                                      "endedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "entity": Object {
                                                        "disabled": false,
                                                        "displayName": "Lead",
                                                        "id": "LEAD",
                                                        "name": "LEAD",
                                                        "systemDefault": true,
                                                      },
                                                      "estimatedBudget": Object {
                                                        "currencyId": 400,
                                                        "value": 100000,
                                                      },
                                                      "filters": Array [
                                                        Object {
                                                          "field": "id",
                                                          "id": "id",
                                                          "operator": "in",
                                                          "type": "long",
                                                          "value": "553092,553052",
                                                        },
                                                      ],
                                                      "id": 1,
                                                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                      "lastPausedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "name": "Kylas Activity",
                                                      "recordActions": Object {
                                                        "delete": true,
                                                        "read": true,
                                                        "readAll": true,
                                                        "update": true,
                                                        "updateAll": true,
                                                        "write": true,
                                                      },
                                                      "recordsGenerated": 0,
                                                      "sentTo": Object {
                                                        "disabled": false,
                                                        "displayName": "Primary Phone number",
                                                        "id": "PRIMARY_PHONE_NUMBER",
                                                        "name": "PRIMARY_PHONE_NUMBER",
                                                        "systemDefault": true,
                                                      },
                                                      "startDate": "2021-09-10T04:04:23.835Z",
                                                      "startedAt": "2021-09-10T04:04:23.835Z",
                                                      "startedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "status": "DRAFT",
                                                      "totalEngagement": 0,
                                                      "type": Object {
                                                        "disabled": false,
                                                        "displayName": "WhatsApp",
                                                        "id": "WHATSAPP",
                                                        "name": "WHATSAPP",
                                                        "systemDefault": true,
                                                      },
                                                      "updatedAt": "2021-09-10T04:04:23.835Z",
                                                      "updatedBy": Object {
                                                        "id": 3788,
                                                        "name": "Andrew Strauss",
                                                      },
                                                      "utmCampaign": "utm campaign",
                                                      "utmContent": "content",
                                                      "utmMedium": "medium",
                                                      "utmSource": "google",
                                                      "utmTerm": "term",
                                                      "whatsappTemplate": Object {
                                                        "id": 47,
                                                        "name": "Welcome to Kylas",
                                                      },
                                                    }
                                                  }
                                                >
                                                  <CampaignRelatedFormValues
                                                    dateFormat="MMM D, YYYY [at] h:mm a"
                                                    dispatch={[Function]}
                                                    entity="campaign-activities"
                                                    entityLabelMap={
                                                      Object {
                                                        "COMPANY": Object {
                                                          "displayName": "Company",
                                                          "displayNamePlural": "Companies",
                                                        },
                                                        "CONTACT": Object {
                                                          "displayName": "Student",
                                                          "displayNamePlural": "Contacts",
                                                        },
                                                        "DEAL": Object {
                                                          "displayName": "Deal",
                                                          "displayNamePlural": "Deals",
                                                        },
                                                        "LEAD": Object {
                                                          "displayName": "Teacher",
                                                          "displayNamePlural": "Teachers",
                                                        },
                                                        "TASK": Object {
                                                          "displayName": "Task",
                                                          "displayNamePlural": "Tasks",
                                                        },
                                                        "TEAM": Object {
                                                          "displayName": "Team",
                                                          "displayNamePlural": "Teams",
                                                        },
                                                        "USER": Object {
                                                          "displayName": "User",
                                                          "displayNamePlural": "Users",
                                                        },
                                                      }
                                                    }
                                                    formValues={
                                                      Object {
                                                        "actualExpense": Object {
                                                          "currencyId": 400,
                                                          "value": 110000,
                                                        },
                                                        "bulkJobId": null,
                                                        "campaign": Object {
                                                          "id": 123,
                                                          "name": "campaign name",
                                                        },
                                                        "connectedAccount": Object {
                                                          "id": 1,
                                                          "name": "Whatsapp Business Account",
                                                        },
                                                        "createdAt": "2021-09-10T04:04:23.835Z",
                                                        "createdBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "endDate": "2021-09-11T04:04:23.835Z",
                                                        "endedAt": "2021-09-10T04:04:23.835Z",
                                                        "endedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "entity": Object {
                                                          "disabled": false,
                                                          "displayName": "Lead",
                                                          "id": "LEAD",
                                                          "name": "LEAD",
                                                          "systemDefault": true,
                                                        },
                                                        "estimatedBudget": Object {
                                                          "currencyId": 400,
                                                          "value": 100000,
                                                        },
                                                        "filters": Array [
                                                          Object {
                                                            "field": "id",
                                                            "id": "id",
                                                            "operator": "in",
                                                            "type": "long",
                                                            "value": "553092,553052",
                                                          },
                                                        ],
                                                        "id": 1,
                                                        "lastPausedAt": "2021-09-10T04:04:23.835Z",
                                                        "lastPausedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "name": "Kylas Activity",
                                                        "recordActions": Object {
                                                          "delete": true,
                                                          "read": true,
                                                          "readAll": true,
                                                          "update": true,
                                                          "updateAll": true,
                                                          "write": true,
                                                        },
                                                        "recordsGenerated": 0,
                                                        "sentTo": Object {
                                                          "disabled": false,
                                                          "displayName": "Primary Phone number",
                                                          "id": "PRIMARY_PHONE_NUMBER",
                                                          "name": "PRIMARY_PHONE_NUMBER",
                                                          "systemDefault": true,
                                                        },
                                                        "startDate": "2021-09-10T04:04:23.835Z",
                                                        "startedAt": "2021-09-10T04:04:23.835Z",
                                                        "startedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "status": "DRAFT",
                                                        "totalEngagement": 0,
                                                        "type": Object {
                                                          "disabled": false,
                                                          "displayName": "WhatsApp",
                                                          "id": "WHATSAPP",
                                                          "name": "WHATSAPP",
                                                          "systemDefault": true,
                                                        },
                                                        "updatedAt": "2021-09-10T04:04:23.835Z",
                                                        "updatedBy": Object {
                                                          "id": 3788,
                                                          "name": "Andrew Strauss",
                                                        },
                                                        "utmCampaign": "utm campaign",
                                                        "utmContent": "content",
                                                        "utmMedium": "medium",
                                                        "utmSource": "google",
                                                        "utmTerm": "term",
                                                        "whatsappTemplate": Object {
                                                          "id": 47,
                                                          "name": "Welcome to Kylas",
                                                        },
                                                      }
                                                    }
                                                    timezone="Asia/Calcutta"
                                                  >
                                                    <div
                                                      className="campaign-related__form-values campaign__draft-status"
                                                    >
                                                      <div
                                                        className="section"
                                                        key="0"
                                                      >
                                                        <div
                                                          className="section-name"
                                                        >
                                                          Activity Details
                                                        </div>
                                                        <div
                                                          className="row"
                                                        >
                                                          <div
                                                            className="col-6"
                                                            key="0"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              ID
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              1
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="1"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Activity Name
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Kylas Activity
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="2"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Activity Type
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              WhatsApp
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="3"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Status
                                                            </div>
                                                            <div
                                                              className="d-flex justify-content-end"
                                                            >
                                                              <div
                                                                className="campaign-related__status"
                                                                style={
                                                                  Object {
                                                                    "backgroundColor": "#C6DEFF",
                                                                    "border": "1px solid #006DEE",
                                                                    "color": "#006DEE",
                                                                  }
                                                                }
                                                              >
                                                                Draft
                                                              </div>
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="4"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Start Date
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="5"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              End Date
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 11, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="6"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Entity
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Teacher
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="7"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Sent To
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Primary Phone number
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="8"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Whatsapp Account
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Whatsapp Business Account
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="9"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Template Name
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              <Connect(WhatsAppTemplatePreviewMode)
                                                                connectedAccount={
                                                                  Object {
                                                                    "id": 1,
                                                                    "name": "Whatsapp Business Account",
                                                                  }
                                                                }
                                                                referrer="Campaign Details"
                                                                template={
                                                                  Object {
                                                                    "id": 47,
                                                                    "name": "Welcome to Kylas",
                                                                  }
                                                                }
                                                              >
                                                                <WhatsAppTemplatePreviewMode
                                                                  connectedAccount={
                                                                    Object {
                                                                      "id": 1,
                                                                      "name": "Whatsapp Business Account",
                                                                    }
                                                                  }
                                                                  referrer="Campaign Details"
                                                                  template={
                                                                    Object {
                                                                      "id": 47,
                                                                      "name": "Welcome to Kylas",
                                                                    }
                                                                  }
                                                                >
                                                                  <div
                                                                    className="whatsapp-template__preview-mode"
                                                                  >
                                                                    <span
                                                                      className="preview-cta "
                                                                      onClick={[Function]}
                                                                    >
                                                                      Welcome to Kylas
                                                                    </span>
                                                                  </div>
                                                                </WhatsAppTemplatePreviewMode>
                                                              </Connect(WhatsAppTemplatePreviewMode)>
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="10"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Created At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="11"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Created By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Andrew Strauss
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="12"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Updated At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="13"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Updated By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Andrew Strauss
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="14"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Started At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="15"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Started By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Andrew Strauss
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="16"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Last Paused At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="17"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Last Paused By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Andrew Strauss
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="18"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Last Resumed At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              -
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="19"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Last Resumed By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              -
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="20"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Ended At
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Sep 10, 2021 at 9:34 am
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="21"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              Ended By
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              Andrew Strauss
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                      <div
                                                        className="section"
                                                        key="1"
                                                      >
                                                        <div
                                                          className="section-name"
                                                        >
                                                          UTM Details
                                                        </div>
                                                        <div
                                                          className="row"
                                                        >
                                                          <div
                                                            className="col-6"
                                                            key="0"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              UTM Campaign
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              utm campaign
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="1"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              UTM Source
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              google
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="2"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              UTM Medium
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              medium
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="3"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              UTM Content
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              content
                                                            </div>
                                                          </div>
                                                          <div
                                                            className="col-6"
                                                            key="4"
                                                          >
                                                            <div
                                                              className="label"
                                                            >
                                                              UTM Term
                                                            </div>
                                                            <div
                                                              className="f-14"
                                                            >
                                                              term
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </CampaignRelatedFormValues>
                                                </Connect(CampaignRelatedFormValues)>
                                              </div>
                                            </div>
                                          </div>
                                        </CampaignActivityOverview>
                                      </Connect(CampaignActivityOverview)>
                                    </div>
                                  </div>
                                </div>
                              </ApiStateHandler>
                            </Route>
                          </withRouter(ApiStateHandler)>
                        </main>
                      </div>
                    </SalesLayout>
                  </Route>
                </withRouter(SalesLayout)>
              </Connect(withRouter(SalesLayout))>
            </CampaignDetails>
          </Component>
        </WithAbortController>
      </Connect(WithAbortController)>
    </Router>
  </BrowserRouter>
</Provider>
`;
