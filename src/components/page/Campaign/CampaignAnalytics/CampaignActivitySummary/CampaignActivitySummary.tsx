import * as React from 'react';

import { CurrencyValue } from '../../../../shared/Input/Money/Money';

import { isBlank } from '../../../../../utils/globalUtil';

import ReadOnlyMoney from '../../../../shared/ReadOnlyMoney/ReadOnlyMoney';

import './_campaign-activity-summary.scss';

interface Props {
  entityName: string;
  totalEngagement: number;
  recordsGenerated: number;
  actualExpense: CurrencyValue;
  estimatedBudget: CurrencyValue;
}

const CampaignActivitySummary:React.FC<Props> = ({ entityName, estimatedBudget, actualExpense, totalEngagement, recordsGenerated }) => {
  return (
    <div className="campaign-activity__summary">
      <div>
        <span className="label">Estimated Budget</span>
        <ReadOnlyMoney {...estimatedBudget} />
      </div>

      <div>
        <span className="label">Actual Expense</span>
        <ReadOnlyMoney {...actualExpense} />
      </div>

      <div>
        <span className="label">Total Engagement</span>
        <div>{!isBlank(totalEngagement) ? totalEngagement : '-'}</div>
      </div>

      <div className="records-generated">
        <span className="label">{entityName ?? 'Total Records'} Generated</span>
        <div>{recordsGenerated ?? '-'}</div>
      </div>
    </div>
  );
};

export default CampaignActivitySummary;
