import * as React from 'react';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';

import { entities } from '../../../../../utils/constants';

import CampaignActivitySummary from './CampaignActivitySummary';

describe('CampaignActivitySummary component', () => {
  const props = {
    totalEngagement: 123,
    recordsGenerated: 100,
    entityName: 'Student',
    actualExpense: { currencyId: 400, value: 1000 },
    estimatedBudget: { currencyId: 400, value: 1200 }
  };

  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    wrapper = shallow(<CampaignActivitySummary {...props} />);
  });

  it('should render component', () => {
    // @ts-ignore
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
