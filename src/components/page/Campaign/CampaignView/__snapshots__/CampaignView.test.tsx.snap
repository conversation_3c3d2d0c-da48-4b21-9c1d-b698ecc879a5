// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignView component should render component 1`] = `
<Connect(withRouter(SalesLayout))>
  <withRouter(ApiStateHandler)
    ErrorFallbackComponent={[Function]}
    SkeletonComponent={[Function]}
    error={null}
    loading={false}
    showErrorComponent={true}
  >
    <div
      className="campaign-view"
    >
      <div
        className="campaign-info "
      >
        <div
          className="back-page"
          onClick={[Function]}
        >
          <i
            className="fas fa-arrow-left mr-2"
          />
          Back To 
          Campaigns
           
          List
        </div>
        <Connect(CampaignCard)
          campaign={
            Object {
              "activities": Array [
                Object {
                  "actualExpense": Object {
                    "currencyId": 400,
                    "value": 110000,
                  },
                  "bulkJobId": null,
                  "campaign": Object {
                    "id": 123,
                    "name": "campaign name",
                  },
                  "createdAt": "2021-09-10T04:04:23.835Z",
                  "createdBy": Object {
                    "id": 3788,
                    "name": "<PERSON>",
                  },
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "endedAt": "2021-09-10T04:04:23.835Z",
                  "endedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "entity": "LEAD",
                  "estimatedBudget": Object {
                    "currencyId": 400,
                    "value": 100000,
                  },
                  "filters": Object {
                    "jsonRule": Object {
                      "condition": "AND",
                      "rules": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "valid": true,
                    },
                  },
                  "id": 1,
                  "lastPausedAt": "2021-09-10T04:04:23.835Z",
                  "lastPausedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "name": "Kylas Activity",
                  "payload": Object {
                    "connectedAccount": Object {
                      "id": 1,
                      "name": "Whatsapp Business Account",
                    },
                    "sentTo": "PRIMARY_PHONE_NUMBER",
                    "type": "WHATSAPP",
                    "whatsappTemplate": Object {
                      "id": 47,
                      "name": "Welcome to Kylas",
                    },
                  },
                  "recordActions": Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  },
                  "recordsGenerated": 0,
                  "startDate": "2021-09-10T04:04:23.835Z",
                  "startedAt": "2021-09-10T04:04:23.835Z",
                  "startedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "status": "DRAFT",
                  "totalEngagement": 0,
                  "updatedAt": "2021-09-10T04:04:23.835Z",
                  "updatedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "utmCampaign": "utm campaign",
                  "utmContent": "content",
                  "utmMedium": "medium",
                  "utmSource": "google",
                  "utmTerm": "term",
                },
              ],
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "description": "Kylas Campaign for Diwali festival",
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "id": 1,
              "name": "Kylas Campaign",
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "DRAFT",
              "totalEngagement": 0,
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
            }
          }
          fetchData={[Function]}
          history={
            Object {
              "push": [MockFunction],
            }
          }
          referrer="Campaign Form"
        />
        <Connect(CampaignActivityCreate)
          campaign={
            Object {
              "id": 1,
              "name": "Kylas Campaign",
            }
          }
          campaignCurrencyId={400}
          campaignRemainingBudgetValue={
            Object {
              "currencyId": 400,
              "value": 0,
            }
          }
          history={
            Object {
              "push": [MockFunction],
            }
          }
        />
      </div>
    </div>
  </withRouter(ApiStateHandler)>
</Connect(withRouter(SalesLayout))>
`;
