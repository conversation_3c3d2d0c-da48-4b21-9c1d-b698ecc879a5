// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Campaign List component should render component 1`] = `
<Connect(withRouter(SalesLayout))>
  <TabsSection
    history={
      Object {
        "push": [MockFunction],
      }
    }
    loading={false}
    section="Campaigns"
    tabs={
      Array [
        Object {
          "displayName": "Campaigns",
          "route": "/sales/campaigns/list",
        },
        Object {
          "displayName": "Activities",
          "route": "/sales/campaigns/activities/list",
        },
      ]
    }
  />
  <div
    className="main-content-wrapper position-relative campaign-listing"
  >
    <div
      className="page-header mb-2"
    >
      <div
        className="page-title-wrapper"
      >
        <div
          className="page-title"
        >
          <h1
            className="h1"
          >
            All 
            Campaigns
          </h1>
          <div
            className="page-subtitle"
          >
            Track and manage your marketing efforts across channels. Monitor performance, engagement, and ROI in one place
          </div>
        </div>
      </div>
      <ListActions
        addButtonLabel="Create Campaign"
        currentPage={1}
        currentUserId={0}
        entity="campaigns"
        fetchData={[Function]}
        filterColumns={Array []}
        headers={Array []}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        isFilterUpdated={false}
        onClickAdd={[Function]}
        profileBasedAbility={Object {}}
        refreshData={[Function]}
        showFilterIcon={false}
        sort={
          Object {
            "field": "Updated At",
            "order": "desc",
          }
        }
        totalItems={1}
      />
    </div>
    <div
      className="page-content flex-column "
    >
      <withRouter(ApiStateHandler)
        ErrorFallbackComponent={[Function]}
        SkeletonComponent={[Function]}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        loading={false}
      >
        <div>
          <Connect(CampaignCard)
            campaign={
              Object {
                "activities": Array [
                  Object {
                    "actualExpense": Object {
                      "currencyId": 400,
                      "value": 110000,
                    },
                    "bulkJobId": null,
                    "campaign": Object {
                      "id": 123,
                      "name": "campaign name",
                    },
                    "createdAt": "2021-09-10T04:04:23.835Z",
                    "createdBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "endDate": "2021-09-11T04:04:23.835Z",
                    "endedAt": "2021-09-10T04:04:23.835Z",
                    "endedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "entity": "LEAD",
                    "estimatedBudget": Object {
                      "currencyId": 400,
                      "value": 100000,
                    },
                    "filters": Object {
                      "jsonRule": Object {
                        "condition": "AND",
                        "rules": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "valid": true,
                      },
                    },
                    "id": 1,
                    "lastPausedAt": "2021-09-10T04:04:23.835Z",
                    "lastPausedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "name": "Kylas Activity",
                    "payload": Object {
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "sentTo": "PRIMARY_PHONE_NUMBER",
                      "type": "WHATSAPP",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    },
                    "recordActions": Object {
                      "delete": true,
                      "read": true,
                      "readAll": true,
                      "update": true,
                      "updateAll": true,
                      "write": true,
                    },
                    "recordsGenerated": 0,
                    "startDate": "2021-09-10T04:04:23.835Z",
                    "startedAt": "2021-09-10T04:04:23.835Z",
                    "startedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "status": "DRAFT",
                    "totalEngagement": 0,
                    "updatedAt": "2021-09-10T04:04:23.835Z",
                    "updatedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "utmCampaign": "utm campaign",
                    "utmContent": "content",
                    "utmMedium": "medium",
                    "utmSource": "google",
                    "utmTerm": "term",
                  },
                ],
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "description": "Kylas Campaign for Diwali festival",
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "id": 1,
                "name": "Kylas Campaign",
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              }
            }
            fetchData={[Function]}
            history={
              Object {
                "push": [MockFunction],
              }
            }
            key="1"
            referrer="Campaign List"
          />
        </div>
      </withRouter(ApiStateHandler)>
    </div>
    <div
      className="campaign-pagination"
    >
      <Pagination
        changePage={[Function]}
        currentPage={1}
        itemList={
          Object {
            "first": true,
            "last": false,
            "size": 30,
            "totalElements": 1,
            "totalPages": 1,
          }
        }
        setPageSize={[Function]}
        showPageSizeDropdown={true}
      />
    </div>
  </div>
</Connect(withRouter(SalesLayout))>
`;
