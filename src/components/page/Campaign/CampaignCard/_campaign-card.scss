@import '../../../../assets/styles/scss/base/variables';

.campaign-card {
  width: 100%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  background-color: $white;
  box-shadow: 0px 0px 4px 0px #00000029;

  .campaign-info {
    &__left-section {
      .clamp-text {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .campaign-id, .campaign-status {
        display: flex;
        height: 1.45rem;
        font-size: 0.8rem;
        padding: 0 0.75rem;
        align-items: center;
        margin-left: 0.75rem;
        justify-content: center;
        border-radius: 0.688rem;
      }
  
      .campaign-id {
        color: $white;
        width: fit-content;
        min-width: 3.688rem;
        background-color: $table-head-color;
      }
  
      .campaign-status {
        min-width: fit-content;
      }
  
      .description {
        color: $gray-650;
      }
  
      .campaign-financial {
        padding-left: 1rem;
        padding-right: 1rem;
        border-right: 1px solid $gray-700;

        .name {
          color: $gray-650;
          font-size: 0.8rem;
        }

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          padding-right: 0;
          border-right: none;
        }
      }
    }
  
    &__right-section {
      display: flex;
      flex-direction: column;
  
      .actions {
        gap: 0.8rem;
        display: flex;
        margin-left: 1rem;
        justify-content: end;

        .add-activity {
          line-height: 1;
          min-width: fit-content;
          padding: 0.5rem 1rem 0.5rem 1rem;
        }
  
        .dropdown-toggle {
          display: flex;
          width: 2.25rem;
          height: 2.25rem;
          justify-content: center;
    
          .far.fa-ellipsis-v, .fas.fa-pencil {
            font-size: 1.3rem;
          }
        }
  
        button {
          height: 2.25rem;
        }
      }

      .fas.fa-calendar-alt {
        margin-right: 0.5rem;
        color: $table-head-color;
      }
    }
  }

  .campaign-activities {
    margin-top: 0.5rem;

    .activities-header {
      width: 100%;
      padding: 1rem;
      max-height: 4rem;
      background: $gray-200;
      border-top-left-radius: 0.8rem;
      border-top-right-radius: 0.8rem;
      transition: border-radius 0.3s ease;

      .activities-count {
        font-size: 1.07rem;
        font-weight: 500;
      }
    }

    .activities-list {
      max-height: 0;
      opacity: 0;
      gap: 1.5rem;
      background: $gray-200;
      transition: all 0.3s ease;
      padding: 0 1rem 1rem 1rem;
      border-bottom-left-radius: 0.8rem;
      border-bottom-right-radius: 0.8rem;
      
      &.collapse.show {
        opacity: 1;
        display: flex;
        flex-wrap: wrap;
        max-height: fit-content;
      }

      .campaign-activity__card {
        flex: 1 1 48%;
        min-width: 30rem;
      }
    }
  }
}