import * as React from 'react';
import { connect } from 'react-redux';
import { toastr } from 'react-redux-toastr';

import { StateInterface } from '../../../../store/store';
import { PlanTypes } from '../../Billing/models/UsageStatistic';
import { Campaign, CampaignActivity, CampaignStatus, CampaignActionType } from '../model';

import { CampaignStatusColors } from '../constants';
import { isBlank } from '../../../../utils/globalUtil';
import { formatDate } from '../../../../utils/dateUtils';
import { entities, ReferrerFlow } from '../../../../utils/constants';
import { showErrorToast } from '../../../../middlewares/errorToastr';
import { convertSnakeCaseTextToLabel } from '../../../../utils/stringUtils';
import { capitalizeLabel, routeToEntitySingular } from '../../../../utils/entityUtils';
import { DELETE, isActionAllowed, UPDATE, WRITE } from '../../../../utils/permissionUtil';
import { getAvailableActionsForCampaignAsPerStatus, getCampaignActionName, getConfirmModalPropsAsPerCampaignActionType, isCampaignDisabled } from '../utils';

import { deleteCampaign, runCampaign } from '../service';

import { EditIcon } from '../../../shared/Icons/GlobalIcons';
import ConfirmModal from '../../../shared/Modal/ConfirmModal';
import MultiActionModal from '../../../shared/Input/MultiActionModal';
import ReadOnlyMoney from '../../../shared/ReadOnlyMoney/ReadOnlyMoney';
import CampaignActivityCard from '../CampaignActivityCard/CampaignActivityCard';
import PlanUpgradeModal from '../../../shared/PlanUpgradeModal/PlanUpgradeModal';

import './_campaign-card.scss';

interface StoreProps {
  timezone: string;
  planName: string;
  dateFormat: string;
  isTenantUser: boolean;
}

interface OwnProps {
  history: any;
  campaign: Campaign;
  referrer: ReferrerFlow;
  fetchData: (type: string) => void;
}

type Props = StoreProps & OwnProps;

export const CampaignCard:React.FC<Props> = ({
  history,
  referrer,
  campaign,
  timezone,
  planName,
  fetchData,
  dateFormat,
  isTenantUser
}) => {

  const {
    id,
    name,
    status,
    endDate,
    startDate,
    activities,
    description,
    actualExpense,
    recordActions,
    estimatedBudget,
    totalEngagement,
    recordsGenerated
  } = campaign;

  const [showModalFor, toggleModalFor] = React.useState<string>(null);
  const [showCampaignActivities, toggleCampaignActivities] = React.useState<boolean>(false);

  const getMultiActionModalOptions = () => {
    const options = [];
    const canDeleteCampaign = isActionAllowed([DELETE], recordActions);
    const canEditCampaign = isActionAllowed([UPDATE], recordActions);

    options.push({ label: 'View', action: (e) => { e.stopPropagation(); history.push(`/sales/${entities.CAMPAIGNS}/details/${id}`); } });

    if(canEditCampaign && (referrer === ReferrerFlow.CAMPAIGN_LIST)) {
      options.push({ label: 'Edit', action: (e) => { e.stopPropagation(); history.push(`/sales/${entities.CAMPAIGNS}/edit/${id}`); } });
    }

    if(canEditCampaign) {
      const requiredActions = getAvailableActionsForCampaignAsPerStatus(status);

      requiredActions.forEach((action: CampaignActionType) => options.push({
        label: (action === CampaignActionType.COMPLETE) ? 'Mark as complete' : capitalizeLabel(action),
        action: (e) => { e.stopPropagation(); toggleModalFor(action); }
      }));
    }

    if(canDeleteCampaign) {
      const shouldDisabled = isCampaignDisabled(status);

      options.push({
        label: 'Delete',
        isDisabled: shouldDisabled,
        action: (e) => { e.stopPropagation(); toggleModalFor('delete_campaign'); },
        tooltip: shouldDisabled ? 'This campaign has already been started and cannot be deleted' : ''
      });
    }

    return options;
  };

  const deleteDraftCampaign = () => {
    deleteCampaign(id, history)
      .then(() => {
        fetchData('delete_campaign');
        toastr.success('Success', `${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))} Deleted`);
      })
      .catch(err => showErrorToast(err));
  };

  const runParticularCampaign = (action: CampaignActionType) => {
    runCampaign(id, action, history)
      .then(() => {
        fetchData('run_campaign');
        toastr.success('Success', `${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))} ${capitalizeLabel(getCampaignActionName(action))}`);
      })
      .catch(err => showErrorToast(err));
  };

  const renderModalPopup = () => {
    switch(showModalFor) {
      case 'delete_campaign':
      case CampaignActionType.START:
      case CampaignActionType.PAUSE:
      case CampaignActionType.RESUME:
      case CampaignActionType.COMPLETE:
        const { title, message, confirmBtn } = getConfirmModalPropsAsPerCampaignActionType(entities.CAMPAIGNS, name, showModalFor);

        return (
          <ConfirmModal
            show
            title={title}
            message={message}
            confirmBtnLabel={confirmBtn.label}
            confirmBtnClass={confirmBtn.className}
            onCancel={() => toggleModalFor(null)}
            onConfirm={() => {
              toggleModalFor(null);

              if(showModalFor === 'delete_campaign') {
                deleteDraftCampaign();
                return;
              }

              runParticularCampaign(showModalFor);
            }}
          />
        );

      case 'campaign_activity_limit_exceed':
        return (
          <PlanUpgradeModal
            history={history}
            planName={planName}
            isTenantUser={isTenantUser}
            onClose={() => toggleModalFor(null)}
            entity={'campaign_activity_limit_exceed'}
            isFreePlan={planName === PlanTypes.FREEMIUM}
          />
        );
    }
  };

  return (
    <React.Fragment>
      <div
        key={id}
        className={`campaign-card ${(referrer === ReferrerFlow.CAMPAIGN_LIST) ? 'cursor-pointer' : ''}`}
        onClick={() => (referrer === ReferrerFlow.CAMPAIGN_LIST) && history.push(`/sales/${entities.CAMPAIGNS}/details/${id}`)}
      >
        <div className="d-flex justify-content-sm-between">
          <div className="campaign-info__left-section w-75">
            <div className="text-break">
              <div className="d-flex">
                <div className={`campaign-name f-15 ${(referrer === ReferrerFlow.CAMPAIGN_FORM) ? 'clamp-text' : ''}`}>{name}</div>

                <div className="campaign-id">#{id}</div>

                <div
                  className="campaign-status"
                  style={{
                    backgroundColor: CampaignStatusColors[status].backgroundColor,
                    color: CampaignStatusColors[status].color,
                    border: `1px solid ${CampaignStatusColors[status].color}`
                  }}
                >
                  {convertSnakeCaseTextToLabel(status)}
                </div>
              </div>

              { !isBlank(description) && <div className={`f-12 description ${(referrer === ReferrerFlow.CAMPAIGN_FORM) ? 'clamp-text' : ''}`}>{description}</div> }
            </div>

            <div className="d-flex mb-2 mt-1">
              <div className="campaign-financial">
                <span className="name">Total Estimated Budget</span>
                <ReadOnlyMoney {...estimatedBudget} />
              </div>

              <div className="campaign-financial">
                <span className="name">Total Actual Expense</span>
                <ReadOnlyMoney {...actualExpense} />
              </div>

              <div className="campaign-financial">
                <span className="name">Total Engagement</span>
                <div>{!isBlank(totalEngagement) ? totalEngagement : '-'}</div>
              </div>

              <div className="campaign-financial">
                <span className="name">Total Records Generated</span>
                <div>{recordsGenerated ?? '-'}</div>
              </div>
            </div>
          </div>

          <div className="campaign-info__right-section">
            <div className="actions">
              { (referrer === ReferrerFlow.CAMPAIGN_FORM) && isActionAllowed([UPDATE], recordActions) &&
                <div
                  className="btn btn-primary dropdown-toggle cursor-pointer btn-sm line-height-1 p-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    history.push(`/sales/${entities.CAMPAIGNS}/edit/${id}`, { referrer: ReferrerFlow.CAMPAIGN_FORM });
                  }}
                >
                  <EditIcon />
                </div>
              }

            { (referrer === ReferrerFlow.CAMPAIGN_LIST) && isActionAllowed([WRITE], recordActions) && (status !== CampaignStatus.COMPLETED) &&
              <button
                style={{ backgroundColor: (activities?.length >= 4) ? '#EBEDF3' : '' }}
                className={`add-activity btn f-14 ${(activities?.length < 4) ? 'btn-outline-primary' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();

                  if(activities?.length >= 4) {
                    toggleModalFor('campaign_activity_limit_exceed');
                    return;
                  }

                  history.push(`/sales/${entities.CAMPAIGNS}/view/${id}`, { referrer: ReferrerFlow.CAMPAIGN_LIST });
                }}
              >
                { (activities?.length >= 4) && <i className="fa fa-lock mr-2" /> }
                + Activity
              </button>
            }

              { (getMultiActionModalOptions().length > 0) &&
                <MultiActionModal
                  className="btn-outline-primary"
                  options={getMultiActionModalOptions()}
                  icon={<i className="fas fa-ellipsis-v" />}
                />
              }
            </div>

            <div className="mt-2">
              <i className="fas fa-calendar-alt" />
              <span className="f-12">{formatDate(startDate, dateFormat, timezone)} {!isBlank(endDate) ? `- ${formatDate(endDate, dateFormat, timezone)}` : ''}</span>
            </div>
          </div>
        </div>

        { (referrer === ReferrerFlow.CAMPAIGN_LIST) && !isBlank(activities) &&
          <div className="campaign-activities" onClick={e => e.stopPropagation()}>
            <div
              className="activities-header"
              style={{
                borderBottomLeftRadius: showCampaignActivities ? '0' : '0.8rem',
                borderBottomRightRadius: showCampaignActivities ? '0' : '0.8rem'
              }}
            >
              <div
                aria-expanded="true"
                data-toggle="collapse"
                data-target={`#collapseActivities_${id}`}
                aria-controls={`collapseActivities_${id}`}
                className="d-flex justify-content-sm-between cursor-pointer"
                onClick={() => toggleCampaignActivities(!showCampaignActivities)}
              >
                <div className="activities-count">Activities ({activities.length})</div>

                <span className="mr-2">
                  <i className={`fa ${showCampaignActivities ? 'fa-chevron-up' : 'fa-chevron-down'}`} />
                </span>
              </div>
            </div>

            <div id={`collapseActivities_${id}`} className={`activities-list collapse ${showCampaignActivities ? 'show' : ''}`}>
              { activities.map((activity: CampaignActivity, index: number) => (
                  <CampaignActivityCard
                    history={history}
                    key={activity.id}
                    referrer={referrer}
                    activity={activity}
                    recordActions={recordActions}
                    fetchData={() => fetchData('campaign_activity')}
                    flexDirectionForProgressBar={(index === (activities.length - 1)) && (activities.length % 2 === 1) ? 'flex-row' : 'flex-column'}
                  />
                ))}
            </div>
          </div>
        }
      </div>

      { !isBlank(showModalFor) && renderModalPopup() }
    </React.Fragment>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  planName: state.genSettings?.payload?.planName,
  timezone: state.loginForm.userPreferences.timezone,
  dateFormat: state.loginForm.userPreferences.dateFormat,
  isTenantUser: isBlank(state.header?.profile?.createdBy)
});

export default connect(mapStateToProps, {})(CampaignCard);
