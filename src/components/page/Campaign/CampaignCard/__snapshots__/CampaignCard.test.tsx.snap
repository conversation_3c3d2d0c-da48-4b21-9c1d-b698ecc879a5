// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Campaign Card should render component 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <CampaignCard
        campaign={
          Object {
            "activities": Array [
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew <PERSON>",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 1,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
            ],
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "description": "Kylas Campaign for Diwali festival",
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "id": 1,
            "name": "Kylas Campaign",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
        dateFormat="MMM D, YYYY [at] h:mm a"
        fetchData={[MockFunction]}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        isTenantUser={true}
        planName="elevate-annual"
        referrer="Campaign List"
        timezone="Asia/Calcutta"
      >
        <div
          className="campaign-card cursor-pointer"
          key="1"
          onClick={[Function]}
        >
          <div
            className="d-flex justify-content-sm-between"
          >
            <div
              className="campaign-info__left-section w-75"
            >
              <div
                className="text-break"
              >
                <div
                  className="d-flex"
                >
                  <div
                    className="campaign-name f-15 "
                  >
                    Kylas Campaign
                  </div>
                  <div
                    className="campaign-id"
                  >
                    #
                    1
                  </div>
                  <div
                    className="campaign-status"
                    style={
                      Object {
                        "backgroundColor": "#C6DEFF",
                        "border": "1px solid #006DEE",
                        "color": "#006DEE",
                      }
                    }
                  >
                    Draft
                  </div>
                </div>
                <div
                  className="f-12 description "
                >
                  Kylas Campaign for Diwali festival
                </div>
              </div>
              <div
                className="d-flex mb-2 mt-1"
              >
                <div
                  className="campaign-financial"
                >
                  <span
                    className="name"
                  >
                    Total Estimated Budget
                  </span>
                  <Connect(ReadOnlyMoney)
                    currencyId={400}
                    value={100000}
                  >
                    <ReadOnlyMoney
                      currencies={
                        Array [
                          Object {
                            "displayName": "Indian Rupee",
                            "id": 13,
                            "name": "INR",
                          },
                        ]
                      }
                      currencyId={400}
                      dispatch={[Function]}
                      numberFormat="INDIAN_NUMBER_FORMAT"
                      value={100000}
                    >
                      <TooltipInitialiseContainer>
                        <div
                          className=""
                          onClick={[Function]}
                        >
                          <span
                            data-html="true"
                            data-placement="top"
                            data-toggle="tooltip"
                            title=" 100,000"
                          >
                             1 L
                          </span>
                        </div>
                      </TooltipInitialiseContainer>
                    </ReadOnlyMoney>
                  </Connect(ReadOnlyMoney)>
                </div>
                <div
                  className="campaign-financial"
                >
                  <span
                    className="name"
                  >
                    Total Actual Expense
                  </span>
                  <Connect(ReadOnlyMoney)
                    currencyId={400}
                    value={110000}
                  >
                    <ReadOnlyMoney
                      currencies={
                        Array [
                          Object {
                            "displayName": "Indian Rupee",
                            "id": 13,
                            "name": "INR",
                          },
                        ]
                      }
                      currencyId={400}
                      dispatch={[Function]}
                      numberFormat="INDIAN_NUMBER_FORMAT"
                      value={110000}
                    >
                      <TooltipInitialiseContainer>
                        <div
                          className=""
                          onClick={[Function]}
                        >
                          <span
                            data-html="true"
                            data-placement="top"
                            data-toggle="tooltip"
                            title=" 110,000"
                          >
                             1.1 L
                          </span>
                        </div>
                      </TooltipInitialiseContainer>
                    </ReadOnlyMoney>
                  </Connect(ReadOnlyMoney)>
                </div>
                <div
                  className="campaign-financial"
                >
                  <span
                    className="name"
                  >
                    Total Engagement
                  </span>
                  <div>
                    0
                  </div>
                </div>
                <div
                  className="campaign-financial"
                >
                  <span
                    className="name"
                  >
                    Total Records Generated
                  </span>
                  <div>
                    0
                  </div>
                </div>
              </div>
            </div>
            <div
              className="campaign-info__right-section"
            >
              <div
                className="actions"
              >
                <button
                  className="add-activity btn f-14 btn-outline-primary"
                  onClick={[Function]}
                  style={
                    Object {
                      "backgroundColor": "",
                    }
                  }
                >
                  + Activity
                </button>
                <MultiActionModal
                  className="btn-outline-primary"
                  icon={
                    <i
                      className="fas fa-ellipsis-v"
                    />
                  }
                  options={
                    Array [
                      Object {
                        "action": [Function],
                        "label": "View",
                      },
                      Object {
                        "action": [Function],
                        "label": "Edit",
                      },
                      Object {
                        "action": [Function],
                        "label": "Start",
                      },
                      Object {
                        "action": [Function],
                        "isDisabled": false,
                        "label": "Delete",
                        "tooltip": "",
                      },
                    ]
                  }
                >
                  <div
                    className="dropdown "
                  >
                    <button
                      aria-expanded="false"
                      aria-haspopup="true"
                      className="btn dropdown-toggle btn-outline-primary"
                      data-offset="0,3"
                      data-toggle="dropdown"
                      onClick={[Function]}
                      style={
                        Object {
                          "cursor": "pointer",
                        }
                      }
                      type="button"
                    >
                      <i
                        className="fas fa-ellipsis-v"
                      />
                    </button>
                    <div
                      className="dropdown-menu dropdown-menu-right"
                      style={
                        Object {
                          "left": "20px",
                          "position": "absolute",
                          "top": "0px",
                          "transform": "translate3d(-129px, 18px, 0px)",
                          "willChange": "transform",
                        }
                      }
                      x-placement="bottom-end"
                    >
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="0_option"
                        onClick={[Function]}
                      >
                        View
                      </a>
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="1_option"
                        onClick={[Function]}
                      >
                        Edit
                      </a>
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="2_option"
                        onClick={[Function]}
                      >
                        Start
                      </a>
                      <a
                        className="dropdown-item"
                        href="javascript:void(0);"
                        key="3_option"
                        onClick={[Function]}
                      >
                        Delete
                      </a>
                    </div>
                  </div>
                </MultiActionModal>
              </div>
              <div
                className="mt-2"
              >
                <i
                  className="fas fa-calendar-alt"
                />
                <span
                  className="f-12"
                >
                  Sep 10, 2021 at 9:34 am
                   
                  - Sep 11, 2021 at 9:34 am
                </span>
              </div>
            </div>
          </div>
          <div
            className="campaign-activities"
            onClick={[Function]}
          >
            <div
              className="activities-header"
              style={
                Object {
                  "borderBottomLeftRadius": "0.8rem",
                  "borderBottomRightRadius": "0.8rem",
                }
              }
            >
              <div
                aria-controls="collapseActivities_1"
                aria-expanded="true"
                className="d-flex justify-content-sm-between cursor-pointer"
                data-target="#collapseActivities_1"
                data-toggle="collapse"
                onClick={[Function]}
              >
                <div
                  className="activities-count"
                >
                  Activities (
                  1
                  )
                </div>
                <span
                  className="mr-2"
                >
                  <i
                    className="fa fa-chevron-down"
                  />
                </span>
              </div>
            </div>
            <div
              className="activities-list collapse "
              id="collapseActivities_1"
            >
              <Connect(CampaignActivityCard)
                activity={
                  Object {
                    "actualExpense": Object {
                      "currencyId": 400,
                      "value": 110000,
                    },
                    "bulkJobId": null,
                    "campaign": Object {
                      "id": 123,
                      "name": "campaign name",
                    },
                    "createdAt": "2021-09-10T04:04:23.835Z",
                    "createdBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "endDate": "2021-09-11T04:04:23.835Z",
                    "endedAt": "2021-09-10T04:04:23.835Z",
                    "endedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "entity": "LEAD",
                    "estimatedBudget": Object {
                      "currencyId": 400,
                      "value": 100000,
                    },
                    "filters": Object {
                      "jsonRule": Object {
                        "condition": "AND",
                        "rules": Array [
                          Object {
                            "field": "id",
                            "id": "id",
                            "operator": "in",
                            "type": "long",
                            "value": "553092,553052",
                          },
                        ],
                        "valid": true,
                      },
                    },
                    "id": 1,
                    "lastPausedAt": "2021-09-10T04:04:23.835Z",
                    "lastPausedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "name": "Kylas Activity",
                    "payload": Object {
                      "connectedAccount": Object {
                        "id": 1,
                        "name": "Whatsapp Business Account",
                      },
                      "sentTo": "PRIMARY_PHONE_NUMBER",
                      "type": "WHATSAPP",
                      "whatsappTemplate": Object {
                        "id": 47,
                        "name": "Welcome to Kylas",
                      },
                    },
                    "recordActions": Object {
                      "delete": true,
                      "read": true,
                      "readAll": true,
                      "update": true,
                      "updateAll": true,
                      "write": true,
                    },
                    "recordsGenerated": 0,
                    "startDate": "2021-09-10T04:04:23.835Z",
                    "startedAt": "2021-09-10T04:04:23.835Z",
                    "startedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "status": "DRAFT",
                    "totalEngagement": 0,
                    "updatedAt": "2021-09-10T04:04:23.835Z",
                    "updatedBy": Object {
                      "id": 3788,
                      "name": "Andrew Strauss",
                    },
                    "utmCampaign": "utm campaign",
                    "utmContent": "content",
                    "utmMedium": "medium",
                    "utmSource": "google",
                    "utmTerm": "term",
                  }
                }
                fetchData={[Function]}
                flexDirectionForProgressBar="flex-row"
                history={
                  Object {
                    "push": [MockFunction],
                  }
                }
                key="1"
                recordActions={
                  Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  }
                }
                referrer="Campaign List"
              >
                <CampaignActivityCard
                  activity={
                    Object {
                      "actualExpense": Object {
                        "currencyId": 400,
                        "value": 110000,
                      },
                      "bulkJobId": null,
                      "campaign": Object {
                        "id": 123,
                        "name": "campaign name",
                      },
                      "createdAt": "2021-09-10T04:04:23.835Z",
                      "createdBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "endDate": "2021-09-11T04:04:23.835Z",
                      "endedAt": "2021-09-10T04:04:23.835Z",
                      "endedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "entity": "LEAD",
                      "estimatedBudget": Object {
                        "currencyId": 400,
                        "value": 100000,
                      },
                      "filters": Object {
                        "jsonRule": Object {
                          "condition": "AND",
                          "rules": Array [
                            Object {
                              "field": "id",
                              "id": "id",
                              "operator": "in",
                              "type": "long",
                              "value": "553092,553052",
                            },
                          ],
                          "valid": true,
                        },
                      },
                      "id": 1,
                      "lastPausedAt": "2021-09-10T04:04:23.835Z",
                      "lastPausedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "name": "Kylas Activity",
                      "payload": Object {
                        "connectedAccount": Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        },
                        "sentTo": "PRIMARY_PHONE_NUMBER",
                        "type": "WHATSAPP",
                        "whatsappTemplate": Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        },
                      },
                      "recordActions": Object {
                        "delete": true,
                        "read": true,
                        "readAll": true,
                        "update": true,
                        "updateAll": true,
                        "write": true,
                      },
                      "recordsGenerated": 0,
                      "startDate": "2021-09-10T04:04:23.835Z",
                      "startedAt": "2021-09-10T04:04:23.835Z",
                      "startedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "status": "DRAFT",
                      "totalEngagement": 0,
                      "updatedAt": "2021-09-10T04:04:23.835Z",
                      "updatedBy": Object {
                        "id": 3788,
                        "name": "Andrew Strauss",
                      },
                      "utmCampaign": "utm campaign",
                      "utmContent": "content",
                      "utmMedium": "medium",
                      "utmSource": "google",
                      "utmTerm": "term",
                    }
                  }
                  dateFormat="MMM D, YYYY [at] h:mm a"
                  entityLabelMap={
                    Object {
                      "COMPANY": Object {
                        "displayName": "Company",
                        "displayNamePlural": "Companies",
                      },
                      "CONTACT": Object {
                        "displayName": "Student",
                        "displayNamePlural": "Contacts",
                      },
                      "DEAL": Object {
                        "displayName": "Deal",
                        "displayNamePlural": "Deals",
                      },
                      "LEAD": Object {
                        "displayName": "Teacher",
                        "displayNamePlural": "Teachers",
                      },
                      "TASK": Object {
                        "displayName": "Task",
                        "displayNamePlural": "Tasks",
                      },
                      "TEAM": Object {
                        "displayName": "Team",
                        "displayNamePlural": "Teams",
                      },
                      "USER": Object {
                        "displayName": "User",
                        "displayNamePlural": "Users",
                      },
                    }
                  }
                  fetchData={[Function]}
                  flexDirectionForProgressBar="flex-row"
                  history={
                    Object {
                      "push": [MockFunction],
                    }
                  }
                  recordActions={
                    Object {
                      "delete": true,
                      "read": true,
                      "readAll": true,
                      "update": true,
                      "updateAll": true,
                      "write": true,
                    }
                  }
                  referrer="Campaign List"
                  timezone="Asia/Calcutta"
                >
                  <div
                    className="campaign-activity__card"
                    onClick={[Function]}
                  >
                    <div
                      className="activity-card__header"
                    >
                      <div
                        className="d-flex"
                      >
                        <img
                          alt="WHATSAPP"
                          src="test-file-stub"
                        />
                        <h3
                          className="f-15 text-break"
                        >
                          Kylas Activity
                        </h3>
                      </div>
                      <div
                        className="header-actions"
                      >
                        <div
                          className="activity-status"
                          style={
                            Object {
                              "backgroundColor": "#C6DEFF",
                              "border": "1px solid #006DEE",
                              "color": "#006DEE",
                            }
                          }
                        >
                          Draft
                        </div>
                        <MultiActionModal
                          icon={
                            <i
                              className="fas fa-ellipsis-v mt-1"
                            />
                          }
                          options={
                            Array [
                              Object {
                                "action": [Function],
                                "label": "View",
                              },
                              Object {
                                "action": [Function],
                                "label": "Edit",
                              },
                              Object {
                                "action": [Function],
                                "label": "Start",
                              },
                              Object {
                                "action": [Function],
                                "isDisabled": false,
                                "label": "Delete",
                                "tooltip": "",
                              },
                            ]
                          }
                        >
                          <div
                            className="dropdown "
                          >
                            <button
                              aria-expanded="false"
                              aria-haspopup="true"
                              className="btn dropdown-toggle "
                              data-offset="0,3"
                              data-toggle="dropdown"
                              onClick={[Function]}
                              style={
                                Object {
                                  "cursor": "pointer",
                                }
                              }
                              type="button"
                            >
                              <i
                                className="fas fa-ellipsis-v mt-1"
                              />
                            </button>
                            <div
                              className="dropdown-menu dropdown-menu-right"
                              style={
                                Object {
                                  "left": "20px",
                                  "position": "absolute",
                                  "top": "0px",
                                  "transform": "translate3d(-129px, 18px, 0px)",
                                  "willChange": "transform",
                                }
                              }
                              x-placement="bottom-end"
                            >
                              <a
                                className="dropdown-item"
                                href="javascript:void(0);"
                                key="0_option"
                                onClick={[Function]}
                              >
                                View
                              </a>
                              <a
                                className="dropdown-item"
                                href="javascript:void(0);"
                                key="1_option"
                                onClick={[Function]}
                              >
                                Edit
                              </a>
                              <a
                                className="dropdown-item"
                                href="javascript:void(0);"
                                key="2_option"
                                onClick={[Function]}
                              >
                                Start
                              </a>
                              <a
                                className="dropdown-item"
                                href="javascript:void(0);"
                                key="3_option"
                                onClick={[Function]}
                              >
                                Delete
                              </a>
                            </div>
                          </div>
                        </MultiActionModal>
                      </div>
                    </div>
                    <div
                      className="activity-card__body"
                    >
                      <div
                        className="d-flex justify-content-between"
                      >
                        <div
                          className="activity-financial"
                        >
                          <span
                            className="name"
                          >
                            Actual Expense
                          </span>
                          <Connect(ReadOnlyMoney)
                            currencyId={400}
                            value={110000}
                          >
                            <ReadOnlyMoney
                              currencies={
                                Array [
                                  Object {
                                    "displayName": "Indian Rupee",
                                    "id": 13,
                                    "name": "INR",
                                  },
                                ]
                              }
                              currencyId={400}
                              dispatch={[Function]}
                              numberFormat="INDIAN_NUMBER_FORMAT"
                              value={110000}
                            >
                              <TooltipInitialiseContainer>
                                <div
                                  className=""
                                  onClick={[Function]}
                                >
                                  <span
                                    data-html="true"
                                    data-placement="top"
                                    data-toggle="tooltip"
                                    title=" 110,000"
                                  >
                                     1.1 L
                                  </span>
                                </div>
                              </TooltipInitialiseContainer>
                            </ReadOnlyMoney>
                          </Connect(ReadOnlyMoney)>
                        </div>
                        <div
                          className="activity-financial"
                        >
                          <span
                            className="name"
                          >
                            Total Engagement
                          </span>
                          <div>
                            0
                          </div>
                        </div>
                        <div
                          className="activity-financial"
                        >
                          <span
                            className="name"
                          >
                            Teachers
                             Generated
                          </span>
                          <div>
                            0
                          </div>
                        </div>
                      </div>
                      <div
                        className="activity-progress flex-row"
                      >
                        <div
                          className="activity-duration"
                        >
                          <div
                            className="d-flex justify-content-sm-between mb-1"
                          >
                            <span>
                              Start Date: 
                              Sep 10, 2021 at 9:34 am
                            </span>
                            <span>
                              End Date: 
                              Sep 11, 2021 at 9:34 am
                            </span>
                          </div>
                          <ProgressBar
                            indicatorColor="#23B33A"
                            width={100}
                          >
                            <div
                              className="progress-bar__container"
                            >
                              <div
                                className="progress-bar__background"
                              />
                              <div
                                className="progress-bar__indicator"
                                style={
                                  Object {
                                    "backgroundColor": "#23B33A",
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </ProgressBar>
                        </div>
                        <div
                          className="activity-budget"
                        >
                          <div
                            className="d-flex justify-content-sm-between mb-1"
                          >
                            <span>
                              Total Spend
                            </span>
                            <div
                              className="d-flex f-13"
                            >
                              <Connect(ReadOnlyMoney)
                                currencyId={400}
                                value={110000}
                              >
                                <ReadOnlyMoney
                                  currencies={
                                    Array [
                                      Object {
                                        "displayName": "Indian Rupee",
                                        "id": 13,
                                        "name": "INR",
                                      },
                                    ]
                                  }
                                  currencyId={400}
                                  dispatch={[Function]}
                                  numberFormat="INDIAN_NUMBER_FORMAT"
                                  value={110000}
                                >
                                  <TooltipInitialiseContainer>
                                    <div
                                      className=""
                                      onClick={[Function]}
                                    >
                                      <span
                                        data-html="true"
                                        data-placement="top"
                                        data-toggle="tooltip"
                                        title=" 110,000"
                                      >
                                         1.1 L
                                      </span>
                                    </div>
                                  </TooltipInitialiseContainer>
                                </ReadOnlyMoney>
                              </Connect(ReadOnlyMoney)>
                              <span>
                                 / 
                              </span>
                              <Connect(ReadOnlyMoney)
                                currencyId={400}
                                value={100000}
                              >
                                <ReadOnlyMoney
                                  currencies={
                                    Array [
                                      Object {
                                        "displayName": "Indian Rupee",
                                        "id": 13,
                                        "name": "INR",
                                      },
                                    ]
                                  }
                                  currencyId={400}
                                  dispatch={[Function]}
                                  numberFormat="INDIAN_NUMBER_FORMAT"
                                  value={100000}
                                >
                                  <TooltipInitialiseContainer>
                                    <div
                                      className=""
                                      onClick={[Function]}
                                    >
                                      <span
                                        data-html="true"
                                        data-placement="top"
                                        data-toggle="tooltip"
                                        title=" 100,000"
                                      >
                                         1 L
                                      </span>
                                    </div>
                                  </TooltipInitialiseContainer>
                                </ReadOnlyMoney>
                              </Connect(ReadOnlyMoney)>
                            </div>
                          </div>
                          <ProgressBar
                            indicatorColor="#EA4335"
                            width={100}
                          >
                            <div
                              className="progress-bar__container"
                            >
                              <div
                                className="progress-bar__background"
                              />
                              <div
                                className="progress-bar__indicator"
                                style={
                                  Object {
                                    "backgroundColor": "#EA4335",
                                    "width": "100%",
                                  }
                                }
                              />
                            </div>
                          </ProgressBar>
                        </div>
                      </div>
                    </div>
                  </div>
                </CampaignActivityCard>
              </Connect(CampaignActivityCard)>
            </div>
          </div>
        </div>
      </CampaignCard>
    </Router>
  </BrowserRouter>
</Provider>
`;
