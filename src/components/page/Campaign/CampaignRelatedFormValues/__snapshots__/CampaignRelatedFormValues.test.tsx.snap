// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignRelatedFormValues component should render component for campaign-activities 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <Connect(CampaignRelatedFormValues)
        dateFormat="MMM D, YYYY [at] h:mm a"
        entity="campaign-activities"
        entityLabelMap={
          Object {
            "COMPANY": Object {
              "displayName": "Company",
              "displayNamePlural": "Companies",
            },
            "CONTACT": Object {
              "displayName": "Student",
              "displayNamePlural": "Contacts",
            },
            "DEAL": Object {
              "displayName": "Deal",
              "displayNamePlural": "Deals",
            },
            "LEAD": Object {
              "displayName": "Teacher",
              "displayNamePlural": "Teachers",
            },
            "TASK": Object {
              "displayName": "Task",
              "displayNamePlural": "Tasks",
            },
            "TEAM": Object {
              "displayName": "Team",
              "displayNamePlural": "Teams",
            },
            "USER": Object {
              "displayName": "User",
              "displayNamePlural": "Users",
            },
          }
        }
        formValues={
          Object {
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "bulkJobId": null,
            "campaign": Object {
              "id": 123,
              "name": "campaign name",
            },
            "connectedAccount": Object {
              "id": 1,
              "name": "Whatsapp Business Account",
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "entity": Object {
              "disabled": false,
              "displayName": "Lead",
              "id": "LEAD",
              "name": "LEAD",
              "systemDefault": true,
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "filters": Array [
              Object {
                "field": "id",
                "id": "id",
                "operator": "in",
                "type": "long",
                "value": "553092,553052",
              },
            ],
            "id": 1,
            "lastPausedAt": "2021-09-10T04:04:23.835Z",
            "lastPausedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "name": "Kylas Activity",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "sentTo": Object {
              "disabled": false,
              "displayName": "Primary Phone number",
              "id": "PRIMARY_PHONE_NUMBER",
              "name": "PRIMARY_PHONE_NUMBER",
              "systemDefault": true,
            },
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "type": Object {
              "disabled": false,
              "displayName": "WhatsApp",
              "id": "WHATSAPP",
              "name": "WHATSAPP",
              "systemDefault": true,
            },
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
            "whatsappTemplate": Object {
              "id": 47,
              "name": "Welcome to Kylas",
            },
          }
        }
        timezone="Asia/Calcutta"
      >
        <CampaignRelatedFormValues
          dateFormat="MMM D, YYYY [at] h:mm a"
          dispatch={[Function]}
          entity="campaign-activities"
          entityLabelMap={
            Object {
              "COMPANY": Object {
                "displayName": "Company",
                "displayNamePlural": "Companies",
              },
              "CONTACT": Object {
                "displayName": "Student",
                "displayNamePlural": "Contacts",
              },
              "DEAL": Object {
                "displayName": "Deal",
                "displayNamePlural": "Deals",
              },
              "LEAD": Object {
                "displayName": "Teacher",
                "displayNamePlural": "Teachers",
              },
              "TASK": Object {
                "displayName": "Task",
                "displayNamePlural": "Tasks",
              },
              "TEAM": Object {
                "displayName": "Team",
                "displayNamePlural": "Teams",
              },
              "USER": Object {
                "displayName": "User",
                "displayNamePlural": "Users",
              },
            }
          }
          formValues={
            Object {
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "bulkJobId": null,
              "campaign": Object {
                "id": 123,
                "name": "campaign name",
              },
              "connectedAccount": Object {
                "id": 1,
                "name": "Whatsapp Business Account",
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "entity": Object {
                "disabled": false,
                "displayName": "Lead",
                "id": "LEAD",
                "name": "LEAD",
                "systemDefault": true,
              },
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "filters": Array [
                Object {
                  "field": "id",
                  "id": "id",
                  "operator": "in",
                  "type": "long",
                  "value": "553092,553052",
                },
              ],
              "id": 1,
              "lastPausedAt": "2021-09-10T04:04:23.835Z",
              "lastPausedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "name": "Kylas Activity",
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "sentTo": Object {
                "disabled": false,
                "displayName": "Primary Phone number",
                "id": "PRIMARY_PHONE_NUMBER",
                "name": "PRIMARY_PHONE_NUMBER",
                "systemDefault": true,
              },
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "DRAFT",
              "totalEngagement": 0,
              "type": Object {
                "disabled": false,
                "displayName": "WhatsApp",
                "id": "WHATSAPP",
                "name": "WHATSAPP",
                "systemDefault": true,
              },
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
              "whatsappTemplate": Object {
                "id": 47,
                "name": "Welcome to Kylas",
              },
            }
          }
          timezone="Asia/Calcutta"
        >
          <div
            className="campaign-related__form-values campaign__draft-status"
          >
            <div
              className="section"
              key="0"
            >
              <div
                className="section-name"
              >
                Activity Details
              </div>
              <div
                className="row"
              >
                <div
                  className="col-6"
                  key="0"
                >
                  <div
                    className="label"
                  >
                    ID
                  </div>
                  <div
                    className="f-14"
                  >
                    1
                  </div>
                </div>
                <div
                  className="col-6"
                  key="1"
                >
                  <div
                    className="label"
                  >
                    Activity Name
                  </div>
                  <div
                    className="f-14"
                  >
                    Kylas Activity
                  </div>
                </div>
                <div
                  className="col-6"
                  key="2"
                >
                  <div
                    className="label"
                  >
                    Activity Type
                  </div>
                  <div
                    className="f-14"
                  >
                    WhatsApp
                  </div>
                </div>
                <div
                  className="col-6"
                  key="3"
                >
                  <div
                    className="label"
                  >
                    Status
                  </div>
                  <div
                    className="d-flex justify-content-end"
                  >
                    <div
                      className="campaign-related__status"
                      style={
                        Object {
                          "backgroundColor": "#C6DEFF",
                          "border": "1px solid #006DEE",
                          "color": "#006DEE",
                        }
                      }
                    >
                      Draft
                    </div>
                  </div>
                </div>
                <div
                  className="col-6"
                  key="4"
                >
                  <div
                    className="label"
                  >
                    Start Date
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="5"
                >
                  <div
                    className="label"
                  >
                    End Date
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 11, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="6"
                >
                  <div
                    className="label"
                  >
                    Entity
                  </div>
                  <div
                    className="f-14"
                  >
                    Teacher
                  </div>
                </div>
                <div
                  className="col-6"
                  key="7"
                >
                  <div
                    className="label"
                  >
                    Sent To
                  </div>
                  <div
                    className="f-14"
                  >
                    Primary Phone number
                  </div>
                </div>
                <div
                  className="col-6"
                  key="8"
                >
                  <div
                    className="label"
                  >
                    Whatsapp Account
                  </div>
                  <div
                    className="f-14"
                  >
                    Whatsapp Business Account
                  </div>
                </div>
                <div
                  className="col-6"
                  key="9"
                >
                  <div
                    className="label"
                  >
                    Template Name
                  </div>
                  <div
                    className="f-14"
                  >
                    <Connect(WhatsAppTemplatePreviewMode)
                      connectedAccount={
                        Object {
                          "id": 1,
                          "name": "Whatsapp Business Account",
                        }
                      }
                      referrer="Campaign Details"
                      template={
                        Object {
                          "id": 47,
                          "name": "Welcome to Kylas",
                        }
                      }
                    >
                      <WhatsAppTemplatePreviewMode
                        connectedAccount={
                          Object {
                            "id": 1,
                            "name": "Whatsapp Business Account",
                          }
                        }
                        referrer="Campaign Details"
                        template={
                          Object {
                            "id": 47,
                            "name": "Welcome to Kylas",
                          }
                        }
                      >
                        <div
                          className="whatsapp-template__preview-mode"
                        >
                          <span
                            className="preview-cta "
                            onClick={[Function]}
                          >
                            Welcome to Kylas
                          </span>
                        </div>
                      </WhatsAppTemplatePreviewMode>
                    </Connect(WhatsAppTemplatePreviewMode)>
                  </div>
                </div>
                <div
                  className="col-6"
                  key="10"
                >
                  <div
                    className="label"
                  >
                    Created At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="11"
                >
                  <div
                    className="label"
                  >
                    Created By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="12"
                >
                  <div
                    className="label"
                  >
                    Updated At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="13"
                >
                  <div
                    className="label"
                  >
                    Updated By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="14"
                >
                  <div
                    className="label"
                  >
                    Started At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="15"
                >
                  <div
                    className="label"
                  >
                    Started By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="16"
                >
                  <div
                    className="label"
                  >
                    Last Paused At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="17"
                >
                  <div
                    className="label"
                  >
                    Last Paused By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="18"
                >
                  <div
                    className="label"
                  >
                    Last Resumed At
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="19"
                >
                  <div
                    className="label"
                  >
                    Last Resumed By
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="20"
                >
                  <div
                    className="label"
                  >
                    Ended At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="21"
                >
                  <div
                    className="label"
                  >
                    Ended By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
              </div>
            </div>
            <div
              className="section"
              key="1"
            >
              <div
                className="section-name"
              >
                UTM Details
              </div>
              <div
                className="row"
              >
                <div
                  className="col-6"
                  key="0"
                >
                  <div
                    className="label"
                  >
                    UTM Campaign
                  </div>
                  <div
                    className="f-14"
                  >
                    utm campaign
                  </div>
                </div>
                <div
                  className="col-6"
                  key="1"
                >
                  <div
                    className="label"
                  >
                    UTM Source
                  </div>
                  <div
                    className="f-14"
                  >
                    google
                  </div>
                </div>
                <div
                  className="col-6"
                  key="2"
                >
                  <div
                    className="label"
                  >
                    UTM Medium
                  </div>
                  <div
                    className="f-14"
                  >
                    medium
                  </div>
                </div>
                <div
                  className="col-6"
                  key="3"
                >
                  <div
                    className="label"
                  >
                    UTM Content
                  </div>
                  <div
                    className="f-14"
                  >
                    content
                  </div>
                </div>
                <div
                  className="col-6"
                  key="4"
                >
                  <div
                    className="label"
                  >
                    UTM Term
                  </div>
                  <div
                    className="f-14"
                  >
                    term
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CampaignRelatedFormValues>
      </Connect(CampaignRelatedFormValues)>
    </Router>
  </BrowserRouter>
</Provider>
`;

exports[`CampaignRelatedFormValues component should render component for campaigns 1`] = `
<Provider
  store={
    Object {
      "clearActions": [Function],
      "dispatch": [Function],
      "getActions": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <Connect(CampaignRelatedFormValues)
        dateFormat="MMM D, YYYY [at] h:mm a"
        entity="campaigns"
        entityLabelMap={
          Object {
            "COMPANY": Object {
              "displayName": "Company",
              "displayNamePlural": "Companies",
            },
            "CONTACT": Object {
              "displayName": "Student",
              "displayNamePlural": "Contacts",
            },
            "DEAL": Object {
              "displayName": "Deal",
              "displayNamePlural": "Deals",
            },
            "LEAD": Object {
              "displayName": "Teacher",
              "displayNamePlural": "Teachers",
            },
            "TASK": Object {
              "displayName": "Task",
              "displayNamePlural": "Tasks",
            },
            "TEAM": Object {
              "displayName": "Team",
              "displayNamePlural": "Teams",
            },
            "USER": Object {
              "displayName": "User",
              "displayNamePlural": "Users",
            },
          }
        }
        formValues={
          Object {
            "activities": Array [
              Object {
                "actualExpense": Object {
                  "currencyId": 400,
                  "value": 110000,
                },
                "bulkJobId": null,
                "campaign": Object {
                  "id": 123,
                  "name": "campaign name",
                },
                "createdAt": "2021-09-10T04:04:23.835Z",
                "createdBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "endDate": "2021-09-11T04:04:23.835Z",
                "endedAt": "2021-09-10T04:04:23.835Z",
                "endedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "entity": "LEAD",
                "estimatedBudget": Object {
                  "currencyId": 400,
                  "value": 100000,
                },
                "filters": Object {
                  "jsonRule": Object {
                    "condition": "AND",
                    "rules": Array [
                      Object {
                        "field": "id",
                        "id": "id",
                        "operator": "in",
                        "type": "long",
                        "value": "553092,553052",
                      },
                    ],
                    "valid": true,
                  },
                },
                "id": 1,
                "lastPausedAt": "2021-09-10T04:04:23.835Z",
                "lastPausedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "name": "Kylas Activity",
                "payload": Object {
                  "connectedAccount": Object {
                    "id": 1,
                    "name": "Whatsapp Business Account",
                  },
                  "sentTo": "PRIMARY_PHONE_NUMBER",
                  "type": "WHATSAPP",
                  "whatsappTemplate": Object {
                    "id": 47,
                    "name": "Welcome to Kylas",
                  },
                },
                "recordActions": Object {
                  "delete": true,
                  "read": true,
                  "readAll": true,
                  "update": true,
                  "updateAll": true,
                  "write": true,
                },
                "recordsGenerated": 0,
                "startDate": "2021-09-10T04:04:23.835Z",
                "startedAt": "2021-09-10T04:04:23.835Z",
                "startedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "status": "DRAFT",
                "totalEngagement": 0,
                "updatedAt": "2021-09-10T04:04:23.835Z",
                "updatedBy": Object {
                  "id": 3788,
                  "name": "Andrew Strauss",
                },
                "utmCampaign": "utm campaign",
                "utmContent": "content",
                "utmMedium": "medium",
                "utmSource": "google",
                "utmTerm": "term",
              },
            ],
            "actualExpense": Object {
              "currencyId": 400,
              "value": 110000,
            },
            "createdAt": "2021-09-10T04:04:23.835Z",
            "createdBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "description": "Kylas Campaign for Diwali festival",
            "endDate": "2021-09-11T04:04:23.835Z",
            "endedAt": "2021-09-10T04:04:23.835Z",
            "endedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "estimatedBudget": Object {
              "currencyId": 400,
              "value": 100000,
            },
            "id": 1,
            "name": "Kylas Campaign",
            "recordActions": Object {
              "delete": true,
              "read": true,
              "readAll": true,
              "update": true,
              "updateAll": true,
              "write": true,
            },
            "recordsGenerated": 0,
            "startDate": "2021-09-10T04:04:23.835Z",
            "startedAt": "2021-09-10T04:04:23.835Z",
            "startedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "status": "DRAFT",
            "totalEngagement": 0,
            "updatedAt": "2021-09-10T04:04:23.835Z",
            "updatedBy": Object {
              "id": 3788,
              "name": "Andrew Strauss",
            },
            "utmCampaign": "utm campaign",
            "utmContent": "content",
            "utmMedium": "medium",
            "utmSource": "google",
            "utmTerm": "term",
          }
        }
        timezone="Asia/Calcutta"
      >
        <CampaignRelatedFormValues
          dateFormat="MMM D, YYYY [at] h:mm a"
          dispatch={[Function]}
          entity="campaigns"
          entityLabelMap={
            Object {
              "COMPANY": Object {
                "displayName": "Company",
                "displayNamePlural": "Companies",
              },
              "CONTACT": Object {
                "displayName": "Student",
                "displayNamePlural": "Contacts",
              },
              "DEAL": Object {
                "displayName": "Deal",
                "displayNamePlural": "Deals",
              },
              "LEAD": Object {
                "displayName": "Teacher",
                "displayNamePlural": "Teachers",
              },
              "TASK": Object {
                "displayName": "Task",
                "displayNamePlural": "Tasks",
              },
              "TEAM": Object {
                "displayName": "Team",
                "displayNamePlural": "Teams",
              },
              "USER": Object {
                "displayName": "User",
                "displayNamePlural": "Users",
              },
            }
          }
          formValues={
            Object {
              "activities": Array [
                Object {
                  "actualExpense": Object {
                    "currencyId": 400,
                    "value": 110000,
                  },
                  "bulkJobId": null,
                  "campaign": Object {
                    "id": 123,
                    "name": "campaign name",
                  },
                  "createdAt": "2021-09-10T04:04:23.835Z",
                  "createdBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "endDate": "2021-09-11T04:04:23.835Z",
                  "endedAt": "2021-09-10T04:04:23.835Z",
                  "endedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "entity": "LEAD",
                  "estimatedBudget": Object {
                    "currencyId": 400,
                    "value": 100000,
                  },
                  "filters": Object {
                    "jsonRule": Object {
                      "condition": "AND",
                      "rules": Array [
                        Object {
                          "field": "id",
                          "id": "id",
                          "operator": "in",
                          "type": "long",
                          "value": "553092,553052",
                        },
                      ],
                      "valid": true,
                    },
                  },
                  "id": 1,
                  "lastPausedAt": "2021-09-10T04:04:23.835Z",
                  "lastPausedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "name": "Kylas Activity",
                  "payload": Object {
                    "connectedAccount": Object {
                      "id": 1,
                      "name": "Whatsapp Business Account",
                    },
                    "sentTo": "PRIMARY_PHONE_NUMBER",
                    "type": "WHATSAPP",
                    "whatsappTemplate": Object {
                      "id": 47,
                      "name": "Welcome to Kylas",
                    },
                  },
                  "recordActions": Object {
                    "delete": true,
                    "read": true,
                    "readAll": true,
                    "update": true,
                    "updateAll": true,
                    "write": true,
                  },
                  "recordsGenerated": 0,
                  "startDate": "2021-09-10T04:04:23.835Z",
                  "startedAt": "2021-09-10T04:04:23.835Z",
                  "startedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "status": "DRAFT",
                  "totalEngagement": 0,
                  "updatedAt": "2021-09-10T04:04:23.835Z",
                  "updatedBy": Object {
                    "id": 3788,
                    "name": "Andrew Strauss",
                  },
                  "utmCampaign": "utm campaign",
                  "utmContent": "content",
                  "utmMedium": "medium",
                  "utmSource": "google",
                  "utmTerm": "term",
                },
              ],
              "actualExpense": Object {
                "currencyId": 400,
                "value": 110000,
              },
              "createdAt": "2021-09-10T04:04:23.835Z",
              "createdBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "description": "Kylas Campaign for Diwali festival",
              "endDate": "2021-09-11T04:04:23.835Z",
              "endedAt": "2021-09-10T04:04:23.835Z",
              "endedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "estimatedBudget": Object {
                "currencyId": 400,
                "value": 100000,
              },
              "id": 1,
              "name": "Kylas Campaign",
              "recordActions": Object {
                "delete": true,
                "read": true,
                "readAll": true,
                "update": true,
                "updateAll": true,
                "write": true,
              },
              "recordsGenerated": 0,
              "startDate": "2021-09-10T04:04:23.835Z",
              "startedAt": "2021-09-10T04:04:23.835Z",
              "startedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "status": "DRAFT",
              "totalEngagement": 0,
              "updatedAt": "2021-09-10T04:04:23.835Z",
              "updatedBy": Object {
                "id": 3788,
                "name": "Andrew Strauss",
              },
              "utmCampaign": "utm campaign",
              "utmContent": "content",
              "utmMedium": "medium",
              "utmSource": "google",
              "utmTerm": "term",
            }
          }
          timezone="Asia/Calcutta"
        >
          <div
            className="campaign-related__form-values campaign__draft-status"
          >
            <div
              className="section"
              key="0"
            >
              <div
                className="section-name"
              >
                Campaign Details
              </div>
              <div
                className="row"
              >
                <div
                  className="col-6"
                  key="0"
                >
                  <div
                    className="label"
                  >
                    ID
                  </div>
                  <div
                    className="f-14"
                  >
                    1
                  </div>
                </div>
                <div
                  className="col-6"
                  key="1"
                >
                  <div
                    className="label"
                  >
                    Status
                  </div>
                  <div
                    className="d-flex justify-content-end"
                  >
                    <div
                      className="campaign-related__status"
                      style={
                        Object {
                          "backgroundColor": "#C6DEFF",
                          "border": "1px solid #006DEE",
                          "color": "#006DEE",
                        }
                      }
                    >
                      Draft
                    </div>
                  </div>
                </div>
                <div
                  className="col-6"
                  key="2"
                >
                  <div
                    className="label"
                  >
                    Start Date
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="3"
                >
                  <div
                    className="label"
                  >
                    End Date
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 11, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="4"
                >
                  <div
                    className="label"
                  >
                    Created At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="5"
                >
                  <div
                    className="label"
                  >
                    Created By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="6"
                >
                  <div
                    className="label"
                  >
                    Updated At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="7"
                >
                  <div
                    className="label"
                  >
                    Updated By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="8"
                >
                  <div
                    className="label"
                  >
                    Started At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="9"
                >
                  <div
                    className="label"
                  >
                    Started By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
                <div
                  className="col-6"
                  key="10"
                >
                  <div
                    className="label"
                  >
                    Last Paused At
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="11"
                >
                  <div
                    className="label"
                  >
                    Last Paused By
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="12"
                >
                  <div
                    className="label"
                  >
                    Last Resumed At
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="13"
                >
                  <div
                    className="label"
                  >
                    Last Resumed By
                  </div>
                  <div
                    className="f-14"
                  >
                    -
                  </div>
                </div>
                <div
                  className="col-6"
                  key="14"
                >
                  <div
                    className="label"
                  >
                    Ended At
                  </div>
                  <div
                    className="f-14"
                  >
                    Sep 10, 2021 at 9:34 am
                  </div>
                </div>
                <div
                  className="col-6"
                  key="15"
                >
                  <div
                    className="label"
                  >
                    Ended By
                  </div>
                  <div
                    className="f-14"
                  >
                    Andrew Strauss
                  </div>
                </div>
              </div>
            </div>
            <div
              className="section"
              key="1"
            >
              <div
                className="section-name"
              >
                UTM Details
              </div>
              <div
                className="row"
              >
                <div
                  className="col-6"
                  key="0"
                >
                  <div
                    className="label"
                  >
                    UTM Campaign
                  </div>
                  <div
                    className="f-14"
                  >
                    utm campaign
                  </div>
                </div>
                <div
                  className="col-6"
                  key="1"
                >
                  <div
                    className="label"
                  >
                    UTM Source
                  </div>
                  <div
                    className="f-14"
                  >
                    google
                  </div>
                </div>
                <div
                  className="col-6"
                  key="2"
                >
                  <div
                    className="label"
                  >
                    UTM Medium
                  </div>
                  <div
                    className="f-14"
                  >
                    medium
                  </div>
                </div>
                <div
                  className="col-6"
                  key="3"
                >
                  <div
                    className="label"
                  >
                    UTM Content
                  </div>
                  <div
                    className="f-14"
                  >
                    content
                  </div>
                </div>
                <div
                  className="col-6"
                  key="4"
                >
                  <div
                    className="label"
                  >
                    UTM Term
                  </div>
                  <div
                    className="f-14"
                  >
                    term
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CampaignRelatedFormValues>
      </Connect(CampaignRelatedFormValues)>
    </Router>
  </BrowserRouter>
</Provider>
`;
